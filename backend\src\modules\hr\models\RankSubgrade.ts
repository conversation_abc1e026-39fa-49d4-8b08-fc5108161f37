import mongoose, { Schema, Document } from "mongoose";

export interface IRankSubgrade extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  deletedBy?: mongoose.Types.ObjectId;
}

const RankSubgradeSchema = new Schema<IRankSubgrade>(
  {
    name: {
      type: String,
      required: [true, "Rank subgrade name is required"],
      trim: true,
      unique: true,
      maxlength: [100, "Rank subgrade name cannot exceed 100 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes
RankSubgradeSchema.index({ name: 1 }, { unique: true });
RankSubgradeSchema.index({ isDeleted: 1 });
RankSubgradeSchema.index({ isActive: 1 });

// Pre-save middleware
RankSubgradeSchema.pre("save", function (next) {
  if (this.isDeleted && !this.deletedAt) {
    this.deletedAt = new Date();
  }
  next();
});

const RankSubgrade = mongoose.model<IRankSubgrade>(
  "RankSubgrade",
  RankSubgradeSchema
);

export default RankSubgrade;
