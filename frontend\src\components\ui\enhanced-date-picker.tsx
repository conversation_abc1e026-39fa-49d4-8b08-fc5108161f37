"use client";

import React, { useState, useEffect } from "react";
import { format, parse, isValid, startOfMonth, endOfMonth, eachDayOfInterval } from "date-fns";
import { id } from "date-fns/locale";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface EnhancedDatePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
  minYear?: number;
  maxYear?: number;
}

export function EnhancedDatePicker({
  value,
  onChange,
  placeholder = "Pilih tanggal",
  disabled = false,
  className,
  label,
  required = false,
  minYear = 1950,
  maxYear = new Date().getFullYear(),
}: EnhancedDatePickerProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [selectedYear, setSelectedYear] = useState<number>(
    value?.getFullYear() || new Date().getFullYear()
  );
  const [selectedMonth, setSelectedMonth] = useState<number>(
    value?.getMonth() || new Date().getMonth()
  );
  const [calendarDate, setCalendarDate] = useState<Date>(
    value || new Date()
  );

  // Update input value when value prop changes
  useEffect(() => {
    if (value) {
      setInputValue(format(value, "dd/MM/yyyy"));
      setSelectedYear(value.getFullYear());
      setSelectedMonth(value.getMonth());
      setCalendarDate(value);
    } else {
      setInputValue("");
    }
  }, [value]);

  // Generate years array
  const years = Array.from(
    { length: maxYear - minYear + 1 },
    (_, i) => maxYear - i
  );

  // Generate months array
  const months = [
    { value: 0, label: "Januari" },
    { value: 1, label: "Februari" },
    { value: 2, label: "Maret" },
    { value: 3, label: "April" },
    { value: 4, label: "Mei" },
    { value: 5, label: "Juni" },
    { value: 6, label: "Juli" },
    { value: 7, label: "Agustus" },
    { value: 8, label: "September" },
    { value: 9, label: "Oktober" },
    { value: 10, label: "November" },
    { value: 11, label: "Desember" },
  ];

  // Handle manual input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputVal = e.target.value;
    setInputValue(inputVal);

    // Try to parse the input (dd/mm/yyyy format)
    if (inputVal.length === 10) {
      const parsedDate = parse(inputVal, "dd/MM/yyyy", new Date());
      if (isValid(parsedDate)) {
        onChange?.(parsedDate);
        setSelectedYear(parsedDate.getFullYear());
        setSelectedMonth(parsedDate.getMonth());
        setCalendarDate(parsedDate);
      }
    }
  };

  // Handle year/month change
  const handleYearMonthChange = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1);
    setCalendarDate(newDate);
  };

  useEffect(() => {
    handleYearMonthChange();
  }, [selectedYear, selectedMonth]);

  // Handle day selection
  const handleDaySelect = (day: number) => {
    const newDate = new Date(selectedYear, selectedMonth, day);
    onChange?.(newDate);
    setOpen(false);
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const start = startOfMonth(calendarDate);
    const end = endOfMonth(calendarDate);
    const days = eachDayOfInterval({ start, end });
    
    // Add empty cells for days before month start
    const startDay = start.getDay();
    const emptyCells = Array.from({ length: startDay }, (_, i) => null);
    
    return [...emptyCells, ...days];
  };

  const calendarDays = generateCalendarDays();

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className="text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="flex gap-2">
        {/* Manual Input */}
        <Input
          value={inputValue}
          onChange={handleInputChange}
          placeholder="dd/mm/yyyy"
          disabled={disabled}
          className="flex-1"
          maxLength={10}
        />
        
        {/* Calendar Popover */}
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              disabled={disabled}
              className="px-3"
            >
              <CalendarIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="start">
            <div className="p-4 space-y-4">
              {/* Year and Month Selectors */}
              <div className="flex gap-2">
                <Select
                  value={selectedYear.toString()}
                  onValueChange={(value) => setSelectedYear(parseInt(value))}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select
                  value={selectedMonth.toString()}
                  onValueChange={(value) => setSelectedMonth(parseInt(value))}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value.toString()}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Calendar Grid */}
              <div className="space-y-2">
                {/* Header */}
                <div className="grid grid-cols-7 gap-1 text-center text-sm font-medium text-gray-500">
                  <div>Min</div>
                  <div>Sen</div>
                  <div>Sel</div>
                  <div>Rab</div>
                  <div>Kam</div>
                  <div>Jum</div>
                  <div>Sab</div>
                </div>
                
                {/* Days Grid */}
                <div className="grid grid-cols-7 gap-1">
                  {calendarDays.map((day, index) => (
                    <Button
                      key={index}
                      variant={
                        day && value && 
                        day.getDate() === value.getDate() &&
                        day.getMonth() === value.getMonth() &&
                        day.getFullYear() === value.getFullYear()
                          ? "default"
                          : "ghost"
                      }
                      className="h-8 w-8 p-0 text-sm"
                      disabled={!day}
                      onClick={() => day && handleDaySelect(day.getDate())}
                    >
                      {day?.getDate()}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex justify-between text-xs">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const today = new Date();
                    onChange?.(today);
                    setOpen(false);
                  }}
                >
                  Hari Ini
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onChange?.(undefined);
                    setOpen(false);
                  }}
                >
                  Hapus
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
      
      {/* Helper text */}
      <p className="text-xs text-gray-500">
        Format: dd/mm/yyyy (contoh: 11/02/1982)
      </p>
    </div>
  );
}
