"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Award,
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface RankGrade {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const RankGradesPage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [rankGrades, setRankGrades] = useState<RankGrade[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch rank grades from API
  const fetchRankGrades = async () => {
    try {
      setIsLoading(true);

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch("http://localhost:5000/api/hr/rank-grades", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (result.success) {
        setRankGrades(result.data || []);
      } else {
        toast.error("Gagal memuat data golongan", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error fetching rank grades:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRankGrades();
  }, []);

  const filteredRankGrades = rankGrades.filter(
    (grade) =>
      grade.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (grade.description &&
        grade.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleEdit = (id: string) => {
    router.push(`/hr/rank-grades/edit/${id}`);
  };

  const handleDelete = async (id: string, name: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Menghapus golongan...", {
        description: `Sedang menghapus ${name}`,
      });

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.dismiss(loadingToast);
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/rank-grades/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success("Golongan berhasil dihapus!", {
          description: `${name} telah dinonaktifkan dari sistem`,
          action: {
            label: "Undo",
            onClick: () => {
              toast.info("Fitur undo akan segera tersedia");
            },
          },
        });

        // Refresh data
        await fetchRankGrades();
      } else {
        toast.error("Gagal menghapus golongan", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error deleting rank grade:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Rank Grades Page */}
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Golongan</h1>
            <p className="text-gray-600 mt-1">Kelola golongan pangkat</p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => router.push("/hr/rank-grades/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Golongan
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Buat golongan baru</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari golongan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">
                {filteredRankGrades.length} golongan
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Memuat data golongan...</span>
          </div>
        ) : (
          <>
            {/* Rank Grades Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRankGrades.map((grade) => (
                <Card
                  key={grade.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <Award className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            {grade.name}
                          </CardTitle>
                        </div>
                      </div>
                      <Badge
                        variant={grade.isActive ? "default" : "secondary"}
                        className={
                          grade.isActive ? "bg-green-100 text-green-800" : ""
                        }
                      >
                        {grade.isActive ? "Aktif" : "Tidak Aktif"}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600">
                      {grade.description || "Tidak ada deskripsi"}
                    </p>

                    <div className="flex items-center text-xs text-gray-500">
                      <Calendar className="w-3 h-3 mr-1" />
                      Dibuat:{" "}
                      {new Date(grade.createdAt).toLocaleDateString("id-ID")}
                    </div>

                    <div className="flex space-x-2 pt-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(grade.id)}
                            className="flex-1"
                          >
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Edit golongan</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(grade.id, grade.name)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Hapus golongan</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {filteredRankGrades.length === 0 && !isLoading && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Tidak ada golongan
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm
                      ? "Coba sesuaikan kata kunci pencarian."
                      : "Mulai dengan membuat golongan pertama."}
                  </p>
                  {!searchTerm && (
                    <Button onClick={() => router.push("/hr/rank-grades/add")}>
                      <Plus className="w-4 h-4 mr-2" />
                      Tambah Golongan
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </TooltipProvider>
  );
};

export default RankGradesPage;
