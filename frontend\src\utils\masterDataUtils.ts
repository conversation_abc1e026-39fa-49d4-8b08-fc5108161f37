/**
 * Utility functions for handling master data consistently
 * Ensures proper ID mapping between frontend and backend
 */

// Generic interface for master data items
export interface MasterDataItem {
  _id?: string; // For backward compatibility
  id?: string; // Primary ID field from backend transform
  name: string;
  [key: string]: any;
}

/**
 * Get the correct ID from a master data item
 * Prioritizes 'id' field, falls back to '_id'
 */
export const getMasterDataId = (item: MasterDataItem): string => {
  return item.id || item._id || '';
};

/**
 * Map master data items to dropdown options
 * Ensures consistent ID usage across all dropdowns
 */
export const mapToDropdownOptions = (
  items: MasterDataItem[]
): Array<{ value: string; label: string }> => {
  return items.map((item) => ({
    value: getMasterDataId(item),
    label: item.name,
  }));
};

/**
 * Map master data items to SearchableSelect options
 * Ensures consistent ID usage across all SearchableSelect components
 */
export const mapToSearchableSelectOptions = (
  items: MasterDataItem[]
): Array<{ value: string; label: string; disabled?: boolean }> => {
  return items.map((item) => ({
    value: getMasterDataId(item),
    label: item.name,
    disabled: item.isActive === false, // Disable inactive items
  }));
};

/**
 * Find master data item by ID
 * Searches both 'id' and '_id' fields
 */
export const findMasterDataById = (
  items: MasterDataItem[],
  searchId: string
): MasterDataItem | undefined => {
  return items.find(
    (item) => item.id === searchId || item._id === searchId
  );
};

/**
 * Validate that all required master data IDs exist
 * Used before submitting forms to ensure data integrity
 */
export const validateMasterDataIds = (
  formData: Record<string, any>,
  masterData: {
    departments?: MasterDataItem[];
    positions?: MasterDataItem[];
    employmentTypes?: MasterDataItem[];
    rankCategories?: MasterDataItem[];
    rankGrades?: MasterDataItem[];
    rankSubgrades?: MasterDataItem[];
    tags?: MasterDataItem[];
  }
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check department
  if (formData.department && masterData.departments) {
    const dept = findMasterDataById(masterData.departments, formData.department);
    if (!dept) {
      errors.push('Departemen tidak valid');
    }
  }

  // Check position
  if (formData.position && masterData.positions) {
    const pos = findMasterDataById(masterData.positions, formData.position);
    if (!pos) {
      errors.push('Posisi tidak valid');
    }
  }

  // Check employment type
  if (formData.employmentType && masterData.employmentTypes) {
    const empType = findMasterDataById(masterData.employmentTypes, formData.employmentType);
    if (!empType) {
      errors.push('Jenis hubungan kerja tidak valid');
    }
  }

  // Check rank category
  if (formData.rankCategory && masterData.rankCategories) {
    const rankCat = findMasterDataById(masterData.rankCategories, formData.rankCategory);
    if (!rankCat) {
      errors.push('Kategori pangkat tidak valid');
    }
  }

  // Check rank grade
  if (formData.rankGrade && masterData.rankGrades) {
    const rankGrade = findMasterDataById(masterData.rankGrades, formData.rankGrade);
    if (!rankGrade) {
      errors.push('Golongan pangkat tidak valid');
    }
  }

  // Check rank subgrade
  if (formData.rankSubgrade && masterData.rankSubgrades) {
    const rankSubgrade = findMasterDataById(masterData.rankSubgrades, formData.rankSubgrade);
    if (!rankSubgrade) {
      errors.push('Sub golongan pangkat tidak valid');
    }
  }

  // Check tags (array)
  if (formData.tags && Array.isArray(formData.tags) && masterData.tags) {
    for (const tagId of formData.tags) {
      const tag = findMasterDataById(masterData.tags, tagId);
      if (!tag) {
        errors.push(`Tag dengan ID ${tagId} tidak valid`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Clean form data before sending to backend
 * Ensures only valid IDs are sent and removes empty values
 */
export const cleanFormDataForSubmission = (formData: any): any => {
  const cleaned = { ...formData };

  // Remove empty strings and replace with null/undefined
  const cleanValue = (value: any): any => {
    if (typeof value === 'string' && value.trim() === '') {
      return undefined;
    }
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      const cleanedObj: any = {};
      for (const [key, val] of Object.entries(value)) {
        const cleanedVal = cleanValue(val);
        if (cleanedVal !== undefined) {
          cleanedObj[key] = cleanedVal;
        }
      }
      return Object.keys(cleanedObj).length > 0 ? cleanedObj : undefined;
    }
    return value;
  };

  return cleanValue(cleaned);
};

/**
 * Debug helper to log master data structure
 * Useful for troubleshooting ID mapping issues
 */
export const debugMasterData = (
  masterData: Record<string, MasterDataItem[]>,
  label = 'Master Data'
): void => {
  console.group(`🔍 ${label} Debug Info`);
  
  for (const [key, items] of Object.entries(masterData)) {
    console.group(`📋 ${key} (${items.length} items)`);
    
    if (items.length > 0) {
      const sample = items[0];
      console.log('Sample item structure:', {
        hasId: 'id' in sample,
        has_id: '_id' in sample,
        idValue: sample.id,
        _idValue: sample._id,
        name: sample.name,
      });
      
      // Check for inconsistencies
      const inconsistent = items.filter(item => !item.id && !item._id);
      if (inconsistent.length > 0) {
        console.warn(`⚠️ ${inconsistent.length} items missing both id and _id`);
      }
    }
    
    console.groupEnd();
  }
  
  console.groupEnd();
};
