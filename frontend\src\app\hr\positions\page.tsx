"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  UserCheck,
  Plus,
  Search,
  Edit,
  Trash2,
  Building2,
  Calendar,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Position {
  _id?: string;
  id: string;
  name: string;
  department: {
    _id?: string;
    id: string;
    name: string;
  };
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const PositionsPage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch positions from API
  useEffect(() => {
    fetchPositions();
  }, []);

  const fetchPositions = async () => {
    try {
      setIsLoading(true);

      // Try to fetch from API first
      const token = localStorage.getItem("accessToken");
      let apiSuccess = false;

      if (token) {
        try {
          const response = await fetch(
            "http://localhost:5000/api/hr/positions",
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          const result = await response.json();

          if (result.success && Array.isArray(result.data)) {
            console.log("API data loaded successfully:", result.data);
            setPositions(result.data);
            apiSuccess = true;
          }
        } catch (apiError) {
          console.warn("API call failed, will use fallback data:", apiError);
        }
      }

      // If API failed, show error message
      if (!apiSuccess) {
        console.log("API failed to load positions data");
        toast.error("Gagal memuat data posisi jabatan", {
          description: "Tidak dapat terhubung ke server. Silakan coba lagi.",
        });
        setPositions([]);
      }
    } catch (error) {
      console.error("Error fetching positions:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data posisi jabatan dari server",
      });
      setPositions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredPositions = positions.filter(
    (position) =>
      position.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      position.department.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (position.description &&
        position.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleEdit = (position: Position) => {
    const id = position.id || position._id;
    router.push(`/hr/positions/edit/${id}`);
  };

  const handleDelete = async (position: Position) => {
    const id = position.id || position._id;
    const name = position.name;
    try {
      // Show loading toast
      const loadingToast = toast.loading("Menghapus posisi jabatan...", {
        description: `Sedang menghapus ${name}`,
      });

      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.dismiss(loadingToast);
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/positions/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success) {
        // Refresh data
        fetchPositions();
      } else {
        toast.dismiss(loadingToast);
        toast.error("Gagal menghapus posisi jabatan", {
          description: result.message || "Terjadi kesalahan pada server",
        });
        return;
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success("Posisi jabatan berhasil dihapus!", {
        description: `${name} telah dinonaktifkan dari sistem`,
        action: {
          label: "Undo",
          onClick: () => {
            toast.info("Fitur undo akan segera tersedia");
          },
        },
      });
    } catch (error) {
      console.error("Error deleting position:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat menghapus posisi jabatan",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Posisi Jabatan</h1>
            <p className="text-gray-600 mt-1">Kelola jabatan karyawan</p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => router.push("/hr/positions/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Posisi
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Buat posisi jabatan baru</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari posisi jabatan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">
                {filteredPositions.length} posisi
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Memuat data posisi jabatan...</p>
            </CardContent>
          </Card>
        )}

        {/* Positions Grid */}
        {!isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPositions.map((position) => (
              <Card
                key={position.id || position._id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <UserCheck className="w-6 h-6 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          {position.name}
                        </CardTitle>
                        <div className="flex items-center text-sm text-gray-600 mt-1">
                          <Building2 className="w-4 h-4 mr-1" />
                          {position.department.name}
                        </div>
                      </div>
                    </div>
                    <Badge
                      variant={position.isActive ? "default" : "secondary"}
                      className={
                        position.isActive ? "bg-green-100 text-green-800" : ""
                      }
                    >
                      {position.isActive ? "Aktif" : "Tidak Aktif"}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">
                    {position.description || "Tidak ada deskripsi"}
                  </p>

                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    Dibuat:{" "}
                    {new Date(position.createdAt).toLocaleDateString("id-ID")}
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(position)}
                          className="flex-1"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit informasi posisi jabatan</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(position)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Hapus posisi jabatan</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredPositions.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <UserCheck className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada posisi jabatan
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm
                  ? "Coba sesuaikan kata kunci pencarian."
                  : "Mulai dengan membuat posisi jabatan pertama."}
              </p>
              {!searchTerm && (
                <Button onClick={() => router.push("/hr/positions/add")}>
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Posisi
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </TooltipProvider>
  );
};

export default PositionsPage;
