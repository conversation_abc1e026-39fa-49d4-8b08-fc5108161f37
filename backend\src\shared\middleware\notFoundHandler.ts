import { Request, Response, NextFunction } from "express";
import { ApiResponse } from "../types/common";

/**
 * 404 Not Found handler middleware
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const message = `Route ${req.originalUrl} tidak ditemukan`;

  res.status(404).json({
    success: false,
    message,
    error: "ROUTE_NOT_FOUND",
    data: {
      method: req.method,
      url: req.originalUrl,
      timestamp: new Date(),
    },
    timestamp: new Date(),
  } as ApiResponse);
};
