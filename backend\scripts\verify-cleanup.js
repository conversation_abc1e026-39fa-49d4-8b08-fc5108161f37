const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/psg-sisinfo');

const employeeSchema = new mongoose.Schema({}, { strict: false });
const Employee = mongoose.model('Employee', employeeSchema, 'employees');

async function verifyCleanup() {
  try {
    console.log('🔍 Verifying blob URL cleanup...');
    
    // Get all employees
    const allEmployees = await Employee.find({});
    console.log(`📊 Total employees: ${allEmployees.length}`);
    
    let photoStats = {
      total: allEmployees.length,
      hasPhoto: 0,
      hasValidPhoto: 0,
      hasBlobUrl: 0,
      hasBase64: 0,
      noPhoto: 0
    };
    
    console.log('\n📋 Photo analysis:');
    
    allEmployees.forEach((emp, index) => {
      const photo = emp.personal?.profilePhoto;
      const name = emp.personal?.fullName || 'Unknown';
      
      if (photo) {
        photoStats.hasPhoto++;
        
        if (photo.startsWith('blob:')) {
          photoStats.hasBlobUrl++;
          console.log(`❌ ${index + 1}. ${name} - BLOB URL: ${photo.substring(0, 50)}...`);
        } else if (photo.startsWith('data:')) {
          photoStats.hasValidPhoto++;
          photoStats.hasBase64++;
          console.log(`✅ ${index + 1}. ${name} - Valid base64 photo (${photo.length} chars)`);
        } else {
          console.log(`⚠️  ${index + 1}. ${name} - Unknown photo format: ${photo.substring(0, 50)}...`);
        }
      } else {
        photoStats.noPhoto++;
        console.log(`⭕ ${index + 1}. ${name} - No photo (will show initials)`);
      }
    });
    
    console.log('\n📊 Summary:');
    console.log(`Total employees: ${photoStats.total}`);
    console.log(`Has photo: ${photoStats.hasPhoto}`);
    console.log(`Valid photos (base64): ${photoStats.hasBase64}`);
    console.log(`Invalid blob URLs: ${photoStats.hasBlobUrl}`);
    console.log(`No photo: ${photoStats.noPhoto}`);
    
    if (photoStats.hasBlobUrl === 0) {
      console.log('\n✅ SUCCESS: No blob URLs found! All photos are clean.');
    } else {
      console.log(`\n❌ WARNING: Found ${photoStats.hasBlobUrl} employees with blob URLs that need cleanup.`);
    }
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

// Run verification
verifyCleanup();
