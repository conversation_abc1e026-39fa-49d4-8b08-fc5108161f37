"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { CalendarIcon, Edit, Save, X, Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface Child {
  name: string;
  gender: "La<PERSON>-laki" | "<PERSON><PERSON><PERSON><PERSON>" | "";
  dateOfBirth: Date | undefined;
}

interface Sibling {
  name: string;
  gender: "Laki-laki" | "Perempuan" | "";
  dateOfBirth: Date | undefined;
  lastEducation: string;
  occupation: string;
  description: string;
}

interface FamilyInfoData {
  // Spouse and Children
  spouse: {
    name: string;
    dateOfBirth: Date | undefined;
    marriageDate: Date | undefined;
    lastEducation: string;
    occupation: string;
    numberOfChildren: number;
  };

  // Children Identity (up to 4 children)
  children: Child[];

  // Parents Information
  parents: {
    father: {
      name: string;
      dateOfBirth: Date | undefined;
      lastEducation: string;
      occupation: string;
      description: string;
    };
    mother: {
      name: string;
      dateOfBirth: Date | undefined;
      lastEducation: string;
      occupation: string;
      description: string;
    };
  };

  // Siblings Information
  siblings: {
    childOrder: number;
    totalSiblings: number;
    siblingsData: Sibling[];
  };

  // In-laws Information
  inLaws: {
    fatherInLaw: {
      name: string;
      dateOfBirth: Date | undefined;
      lastEducation: string;
      description: string;
    };
    motherInLaw: {
      name: string;
      dateOfBirth: Date | undefined;
      lastEducation: string;
      description: string;
    };
  };
}

interface EmployeeFamilyInfoProps {
  data?: FamilyInfoData;
  onSave: (data: FamilyInfoData) => void;
  isEditing: boolean;
  onEditToggle: () => void;
}

const genderOptions = [
  { value: "Laki-laki", label: "Laki-laki" },
  { value: "Perempuan", label: "Perempuan" },
];

const educationOptions = [
  "SD",
  "SMP",
  "SMA",
  "D1",
  "D2",
  "D3",
  "S1",
  "S2",
  "S3",
  "Tidak Sekolah",
  "Lainnya",
];

export default function EmployeeFamilyInfo({
  data,
  onSave,
  isEditing,
  onEditToggle,
}: EmployeeFamilyInfoProps) {
  const [formData, setFormData] = useState<FamilyInfoData>({
    // Spouse and Children
    spouse: {
      name: data?.spouse?.name || "",
      dateOfBirth: data?.spouse?.dateOfBirth,
      marriageDate: data?.spouse?.marriageDate,
      lastEducation: data?.spouse?.lastEducation || "",
      occupation: data?.spouse?.occupation || "",
      numberOfChildren: data?.spouse?.numberOfChildren || 0,
    },

    // Children Identity (up to 4 children)
    children: data?.children || [],

    // Parents Information
    parents: {
      father: {
        name: data?.parents?.father?.name || "",
        dateOfBirth: data?.parents?.father?.dateOfBirth,
        lastEducation: data?.parents?.father?.lastEducation || "",
        occupation: data?.parents?.father?.occupation || "",
        description: data?.parents?.father?.description || "",
      },
      mother: {
        name: data?.parents?.mother?.name || "",
        dateOfBirth: data?.parents?.mother?.dateOfBirth,
        lastEducation: data?.parents?.mother?.lastEducation || "",
        occupation: data?.parents?.mother?.occupation || "",
        description: data?.parents?.mother?.description || "",
      },
    },

    // Siblings Information
    siblings: {
      childOrder: data?.siblings?.childOrder || 1,
      totalSiblings: data?.siblings?.totalSiblings || 0,
      siblingsData: data?.siblings?.siblingsData || [],
    },

    // In-laws Information
    inLaws: {
      fatherInLaw: {
        name: data?.inLaws?.fatherInLaw?.name || "",
        dateOfBirth: data?.inLaws?.fatherInLaw?.dateOfBirth,
        lastEducation: data?.inLaws?.fatherInLaw?.lastEducation || "",
        description: data?.inLaws?.fatherInLaw?.description || "",
      },
      motherInLaw: {
        name: data?.inLaws?.motherInLaw?.name || "",
        dateOfBirth: data?.inLaws?.motherInLaw?.dateOfBirth,
        lastEducation: data?.inLaws?.motherInLaw?.lastEducation || "",
        description: data?.inLaws?.motherInLaw?.description || "",
      },
    },
  });

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    if (keys.length === 2) {
      setFormData((prev) => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0] as keyof FamilyInfoData],
          [keys[1]]: value,
        },
      }));
    } else if (keys.length === 3) {
      setFormData((prev) => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0] as keyof FamilyInfoData],
          [keys[1]]: {
            ...prev[keys[0] as keyof FamilyInfoData][keys[1] as any],
            [keys[2]]: value,
          },
        },
      }));
    }
  };

  const handleChildChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      children: prev.children.map((child, i) =>
        i === index ? { ...child, [field]: value } : child
      ),
    }));
  };

  const handleSiblingChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      siblings: {
        ...prev.siblings,
        siblingsData: prev.siblings.siblingsData.map((sibling, i) =>
          i === index ? { ...sibling, [field]: value } : sibling
        ),
      },
    }));
  };

  const addChild = () => {
    if (formData.children.length < 4) {
      setFormData((prev) => ({
        ...prev,
        children: [
          ...prev.children,
          { name: "", gender: "", dateOfBirth: undefined },
        ],
      }));
    }
  };

  const removeChild = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      children: prev.children.filter((_, i) => i !== index),
    }));
  };

  const addSibling = () => {
    if (formData.siblings.siblingsData.length < 4) {
      setFormData((prev) => ({
        ...prev,
        siblings: {
          ...prev.siblings,
          siblingsData: [
            ...prev.siblings.siblingsData,
            {
              name: "",
              gender: "",
              dateOfBirth: undefined,
              lastEducation: "",
              occupation: "",
              description: "",
            },
          ],
        },
      }));
    }
  };

  const removeSibling = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      siblings: {
        ...prev.siblings,
        siblingsData: prev.siblings.siblingsData.filter((_, i) => i !== index),
      },
    }));
  };

  const handleSave = () => {
    onSave(formData);
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type: "text" | "select" | "date" | "number" | "textarea" = "text",
    options?: string[] | { value: string; label: string }[],
    required = false
  ) => (
    <div className={type === "textarea" ? "md:col-span-2" : ""}>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {isEditing ? (
        type === "select" ? (
          <Select
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onValueChange={(value) => handleInputChange(field, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem
                  key={typeof option === "string" ? option : option.value}
                  value={typeof option === "string" ? option : option.value}
                >
                  {typeof option === "string" ? option : option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : type === "date" ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !field
                    .split(".")
                    .reduce((obj, key) => obj?.[key], formData) &&
                    "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {field.split(".").reduce((obj, key) => obj?.[key], formData) ? (
                  format(
                    field.split(".").reduce((obj, key) => obj?.[key], formData),
                    "dd MMMM yyyy",
                    { locale: id }
                  )
                ) : (
                  <span>Pilih tanggal</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={field
                  .split(".")
                  .reduce((obj, key) => obj?.[key], formData)}
                onSelect={(date) => handleInputChange(field, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : type === "textarea" ? (
          <Textarea
            id={field}
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder={`Masukkan ${label.toLowerCase()}`}
            rows={3}
          />
        ) : (
          <Input
            id={field}
            type={type}
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onChange={(e) =>
              handleInputChange(
                field,
                type === "number" ? Number(e.target.value) : e.target.value
              )
            }
            placeholder={`Masukkan ${label.toLowerCase()}`}
          />
        )
      ) : (
        <p className="text-sm text-gray-700">
          {field.split(".").reduce((obj, key) => obj?.[key], formData) || "-"}
        </p>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Informasi Keluarga</CardTitle>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Simpan
              </Button>
              <Button size="sm" variant="outline" onClick={onEditToggle}>
                <X className="w-4 h-4 mr-2" />
                Batal
              </Button>
            </>
          ) : (
            <Button size="sm" variant="outline" onClick={onEditToggle}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Spouse and Children */}
        {renderFormGroup(
          "Pasangan dan Anak",
          <>
            {renderField("Nama Pasangan", "spouse.name", "text")}
            {renderField(
              "Tanggal Lahir Pasangan",
              "spouse.dateOfBirth",
              "date"
            )}
            {renderField("Tanggal Menikah", "spouse.marriageDate", "date")}
            {renderField(
              "Pendidikan Terakhir Pasangan",
              "spouse.lastEducation",
              "select",
              educationOptions
            )}
            {renderField("Pekerjaan Pasangan", "spouse.occupation", "text")}
            {renderField("Jumlah Anak", "spouse.numberOfChildren", "number")}
          </>
        )}

        <Separator />

        {/* Children Identity */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 border-b pb-2">
              Identitas Anak
            </h4>
            {isEditing && formData.children.length < 4 && (
              <Button size="sm" variant="outline" onClick={addChild}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Anak
              </Button>
            )}
          </div>

          {formData.children.map((child, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h5 className="font-medium">Anak {index + 1}</h5>
                {isEditing && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => removeChild(index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>Nama Anak {index + 1}</Label>
                  {isEditing ? (
                    <Input
                      value={child.name}
                      onChange={(e) =>
                        handleChildChange(index, "name", e.target.value)
                      }
                      placeholder="Masukkan nama anak"
                    />
                  ) : (
                    <p className="text-sm text-gray-700">{child.name || "-"}</p>
                  )}
                </div>

                <div>
                  <Label>Jenis Kelamin Anak {index + 1}</Label>
                  {isEditing ? (
                    <Select
                      value={child.gender}
                      onValueChange={(value) =>
                        handleChildChange(index, "gender", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih jenis kelamin" />
                      </SelectTrigger>
                      <SelectContent>
                        {genderOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-700">
                      {child.gender || "-"}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Tanggal Lahir Anak {index + 1}</Label>
                  {isEditing ? (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !child.dateOfBirth && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {child.dateOfBirth ? (
                            format(child.dateOfBirth, "dd MMMM yyyy", {
                              locale: id,
                            })
                          ) : (
                            <span>Pilih tanggal</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={child.dateOfBirth}
                          onSelect={(date) =>
                            handleChildChange(index, "dateOfBirth", date)
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  ) : (
                    <p className="text-sm text-gray-700">
                      {child.dateOfBirth
                        ? format(child.dateOfBirth, "dd MMMM yyyy", {
                            locale: id,
                          })
                        : "-"}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Parents Information */}
        {renderFormGroup(
          "Orang Tua Kandung",
          <>
            {renderField("Nama Ayah Kandung", "parents.father.name", "text")}
            {renderField(
              "Tanggal Lahir Ayah Kandung",
              "parents.father.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ayah Kandung",
              "parents.father.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Pekerjaan Ayah Kandung",
              "parents.father.occupation",
              "text"
            )}
            {renderField(
              "Keterangan Ayah Kandung",
              "parents.father.description",
              "textarea"
            )}
            {renderField("Nama Ibu Kandung", "parents.mother.name", "text")}
            {renderField(
              "Tanggal Lahir Ibu Kandung",
              "parents.mother.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ibu Kandung",
              "parents.mother.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Pekerjaan Ibu Kandung",
              "parents.mother.occupation",
              "text"
            )}
            {renderField(
              "Keterangan Ibu Kandung",
              "parents.mother.description",
              "textarea"
            )}
          </>
        )}

        <Separator />

        {/* Siblings Information */}
        {renderFormGroup(
          "Saudara Kandung",
          <>
            {renderField("Anak Ke", "siblings.childOrder", "number")}
            {renderField(
              "Jumlah Saudara Kandung",
              "siblings.totalSiblings",
              "number"
            )}
          </>
        )}

        {/* Siblings Identity */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 border-b pb-2">
              Identitas Saudara Kandung
            </h4>
            {isEditing && formData.siblings.siblingsData.length < 4 && (
              <Button size="sm" variant="outline" onClick={addSibling}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Saudara
              </Button>
            )}
          </div>

          {formData.siblings.siblingsData.map((sibling, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h5 className="font-medium">Saudara Kandung {index + 1}</h5>
                {isEditing && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => removeSibling(index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Nama Saudara Kandung {index + 1}</Label>
                  {isEditing ? (
                    <Input
                      value={sibling.name}
                      onChange={(e) =>
                        handleSiblingChange(index, "name", e.target.value)
                      }
                      placeholder="Masukkan nama saudara"
                    />
                  ) : (
                    <p className="text-sm text-gray-700">
                      {sibling.name || "-"}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Jenis Kelamin</Label>
                  {isEditing ? (
                    <Select
                      value={sibling.gender}
                      onValueChange={(value) =>
                        handleSiblingChange(index, "gender", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih jenis kelamin" />
                      </SelectTrigger>
                      <SelectContent>
                        {genderOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-700">
                      {sibling.gender || "-"}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Tanggal Lahir</Label>
                  {isEditing ? (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !sibling.dateOfBirth && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {sibling.dateOfBirth ? (
                            format(sibling.dateOfBirth, "dd MMMM yyyy", {
                              locale: id,
                            })
                          ) : (
                            <span>Pilih tanggal</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={sibling.dateOfBirth}
                          onSelect={(date) =>
                            handleSiblingChange(index, "dateOfBirth", date)
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  ) : (
                    <p className="text-sm text-gray-700">
                      {sibling.dateOfBirth
                        ? format(sibling.dateOfBirth, "dd MMMM yyyy", {
                            locale: id,
                          })
                        : "-"}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Pendidikan Terakhir</Label>
                  {isEditing ? (
                    <Select
                      value={sibling.lastEducation}
                      onValueChange={(value) =>
                        handleSiblingChange(index, "lastEducation", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih pendidikan" />
                      </SelectTrigger>
                      <SelectContent>
                        {educationOptions.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-700">
                      {sibling.lastEducation || "-"}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Pekerjaan</Label>
                  {isEditing ? (
                    <Input
                      value={sibling.occupation}
                      onChange={(e) =>
                        handleSiblingChange(index, "occupation", e.target.value)
                      }
                      placeholder="Masukkan pekerjaan"
                    />
                  ) : (
                    <p className="text-sm text-gray-700">
                      {sibling.occupation || "-"}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label>Keterangan</Label>
                  {isEditing ? (
                    <Textarea
                      value={sibling.description}
                      onChange={(e) =>
                        handleSiblingChange(
                          index,
                          "description",
                          e.target.value
                        )
                      }
                      placeholder="Masukkan keterangan"
                      rows={2}
                    />
                  ) : (
                    <p className="text-sm text-gray-700">
                      {sibling.description || "-"}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* In-laws Information */}
        {renderFormGroup(
          "Orang Tua Mertua",
          <>
            {renderField("Nama Ayah Mertua", "inLaws.fatherInLaw.name", "text")}
            {renderField(
              "Tanggal Lahir Ayah Mertua",
              "inLaws.fatherInLaw.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ayah Mertua",
              "inLaws.fatherInLaw.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Keterangan Ayah Mertua",
              "inLaws.fatherInLaw.description",
              "textarea"
            )}
            {renderField("Nama Ibu Mertua", "inLaws.motherInLaw.name", "text")}
            {renderField(
              "Tanggal Lahir Ibu Mertua",
              "inLaws.motherInLaw.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ibu Mertua",
              "inLaws.motherInLaw.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Keterangan Ibu Mertua",
              "inLaws.motherInLaw.description",
              "textarea"
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
