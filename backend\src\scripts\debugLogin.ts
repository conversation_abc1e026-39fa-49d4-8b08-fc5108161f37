import mongoose from "mongoose";
import bcrypt from "bcryptjs";
import { connectDatabase } from "../config/database";

async function debugLogin() {
  try {
    console.log("🔍 Debugging login issue...\n");

    // Get database instance
    const db = mongoose.connection.db;
    const usersCollection = db.collection("users");

    // Find ADM001 user
    const user = await usersCollection.findOne({
      $or: [{ employeeId: "ADM001" }, { username: "ADM001" }],
    });

    if (!user) {
      console.log("❌ User ADM001 not found");
      return;
    }

    console.log("👤 User found:");
    console.log(`   ID: ${user._id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   EmployeeId: ${user.employeeId}`);
    console.log(`   Username: ${user.username}`);
    console.log(`   IsActive: ${user.isActive}`);
    console.log(`   IsDeleted: ${user.isDeleted}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Password Hash: ${user.password ? "EXISTS" : "MISSING"}`);
    console.log(
      `   Password Length: ${user.password ? user.password.length : 0}`
    );

    // Test password comparison
    if (user.password) {
      console.log("\n🔐 Testing password comparison:");

      const testPasswords = ["admin123", "Admin123", "ADMIN123"];

      for (const testPassword of testPasswords) {
        try {
          const isMatch = await bcrypt.compare(testPassword, user.password);
          console.log(
            `   "${testPassword}": ${isMatch ? "✅ MATCH" : "❌ NO MATCH"}`
          );
        } catch (error: any) {
          console.log(`   "${testPassword}": ❌ ERROR - ${error.message}`);
        }
      }

      // Test if password is properly hashed
      console.log("\n🔍 Password analysis:");
      console.log(
        `   Starts with $2: ${
          user.password.startsWith("$2") ? "✅ YES (bcrypt)" : "❌ NO"
        }`
      );
      console.log(`   Hash format: ${user.password.substring(0, 10)}...`);
    }

    // Check role
    if (user.role) {
      const rolesCollection = db.collection("roles");
      const role = await rolesCollection.findOne({ _id: user.role });

      console.log("\n🎭 Role details:");
      if (role) {
        console.log(`   Name: ${role.name}`);
        console.log(`   IsActive: ${role.isActive}`);
        console.log(`   Description: ${role.description}`);
      } else {
        console.log("   ❌ Role not found");
      }
    }
  } catch (error) {
    console.error("❌ Error debugging login:", error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await debugLogin();
  } catch (error) {
    console.error("❌ Debug failed:", error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log("\n🔌 Disconnected from MongoDB");
    process.exit(0);
  }
}

// Run the debug
main();
