import { Router } from "express";
import {
  getAllEmployees,
  getEmployeeById,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeeStats,
  downloadExcelTemplate,
} from "../controllers/employeeController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// GET /api/hr/employees/template - Download Excel template
router.get("/template", devBypass, downloadExcelTemplate);

// GET /api/hr/employees/stats - Get employee statistics
router.get("/stats", devBypass, getEmployeeStats);

// GET /api/hr/employees - Get all employees with pagination and search
router.get("/", devBypass, getAllEmployees);

// GET /api/hr/employees/:id - Get employee by ID
router.get("/:id", devBypass, getEmployeeById);

// POST /api/hr/employees - Create new employee
router.post("/", devBypass, createEmployee as any);

// PUT /api/hr/employees/:id - Update employee
router.put("/:id", devBypass, updateEmployee as any);

// DELETE /api/hr/employees/:id - Delete employee (soft delete)
router.delete("/:id", devBypass, deleteEmployee as any);

export default router;
