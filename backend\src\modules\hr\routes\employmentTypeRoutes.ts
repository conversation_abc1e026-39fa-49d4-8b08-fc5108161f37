import express from "express";
import { body } from "express-validator";
import {
  getEmploymentTypes,
  getEmploymentTypeById,
  createEmploymentType,
  updateEmploymentType,
  deleteEmploymentType,
} from "../controllers/employmentTypeController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const employmentTypeValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Employment type name is required")
    .isLength({ min: 2, max: 100 })
    .withMessage("Employment type name must be between 2 and 100 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Description must not exceed 500 characters"),
];

// Routes
router.get("/", devBypass, getEmploymentTypes);
router.get("/:id", devBypass, getEmploymentTypeById);
router.post("/", devBypass, employmentTypeValidation, createEmploymentType);
router.put("/:id", devBypass, employmentTypeValidation, updateEmploymentType);
router.delete("/:id", devBypass, deleteEmploymentType);

export default router;
