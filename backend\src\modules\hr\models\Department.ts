import mongoose, { Schema, Document } from "mongoose";

export interface IDepartment extends Document {
  name: string;
  division?: mongoose.Types.ObjectId;
  manager?: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}

const departmentSchema = new Schema<IDepartment>(
  {
    name: {
      type: String,
      required: [true, "Department name is required"],
      trim: true,
      maxlength: [100, "Department name cannot exceed 100 characters"],
      unique: true,
    },
    division: {
      type: Schema.Types.ObjectId,
      ref: "Division",
    },
    manager: {
      type: String,
      trim: true,
      maxlength: [100, "Manager name cannot exceed 100 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: function (_doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
departmentSchema.index({ name: 1 });
departmentSchema.index({ isActive: 1 });
departmentSchema.index({ createdAt: -1 });

// Virtual for employee count (will be populated later)
departmentSchema.virtual("employeeCount", {
  ref: "Employee",
  localField: "_id",
  foreignField: "hr.department",
  count: true,
});

export const Department = mongoose.model<IDepartment>(
  "Department",
  departmentSchema
);
