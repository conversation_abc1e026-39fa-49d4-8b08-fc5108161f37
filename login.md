┌─────────────────────────────────┬──────────────┬───────────────┐  
│ Email │ Password │ Role │  
├─────────────────────────────────┼──────────────┼───────────────┤  
│ <EMAIL> │ admin123 │ Super Admin │  
│ <EMAIL> │ hrmanager123 │ HR Manager │  
│ <EMAIL>│ inventory123 │ Inventory Mgr │  
│ <EMAIL> │ staff123 │ Staff │  
│ <EMAIL> │ employee123 │ Employee │  
└─────────────────────────────────┴──────────────┴───────────────

# Start both services (auto-stop existing first)

npm run service:start

# Stop all services

npm run service:stop

# Check status

npm run service:status

# Kill all Node.js processes

npm run service:kill
