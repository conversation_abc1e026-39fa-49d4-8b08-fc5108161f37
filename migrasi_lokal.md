# Tutorial Migrasi BIS ke Laptop/PC Lain

## Overview
Tutorial ini menjelaskan cara memindahkan project Bebang Information System (BIS) dari satu komputer ke komputer lain dengan mempertahankan semua fungsi, fitur, dan data database yang sudah ada.

## Persiapan di Komputer Sumber (Ko<PERSON><PERSON> Lama)

### 1. Backup Database MongoDB

#### Opsi A: Export Database dengan mongodump (Recommended)
```bash
# Buat folder backup
mkdir C:\BIS_Backup
cd C:\BIS_Backup

# Export seluruh database psg-sisinfo
mongodump --host localhost:27017 --db psg-sisinfo --out ./database_backup

# Verifikasi backup berhasil
dir database_backup\psg-sisinfo
```

#### Opsi B: Export Collection Individual (Alternative)
```bash
# Export setiap collection penting
mongoexport --host localhost:27017 --db psg-sisinfo --collection users --out users.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection employees --out employees.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection departments --out departments.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection positions --out positions.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection employmenttypes --out employmenttypes.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection rankcategories --out rankcategories.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection rankgrades --out rankgrades.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection ranksubgrades --out ranksubgrades.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection tags --out tags.json
mongoexport --host localhost:27017 --db psg-sisinfo --collection divisions --out divisions.json
```

### 2. Backup Project Files
```bash
# Copy seluruh folder project
xcopy "C:\project\pt-psg-taliabu" "C:\BIS_Backup\project" /E /I /H /Y

# Atau menggunakan robocopy (lebih reliable)
robocopy "C:\project\pt-psg-taliabu" "C:\BIS_Backup\project" /E /COPYALL /R:3 /W:5
```

### 2.1. Lokasi Database MongoDB Lokal

**Database `psg-sisinfo` disimpan di lokasi berikut:**

#### Lokasi Default MongoDB Data Directory:
```
C:\Program Files\MongoDB\Server\[VERSION]\data\
```

#### Untuk MongoDB 8.0 (contoh):
```
C:\Program Files\MongoDB\Server\8.0\data\
├── psg-sisinfo\           # Database BIS
│   ├── collection-*.wt    # File data collections
│   ├── index-*.wt         # File index
│   └── metadata.wt        # Metadata database
├── admin\                 # Database admin MongoDB
├── config\                # Database config MongoDB
└── local\                 # Database local MongoDB
```

#### Cara Cek Lokasi Database:
```bash
# Cek lokasi data directory MongoDB
mongod --help | findstr "dbpath"

# Atau cek dari MongoDB log
type "C:\Program Files\MongoDB\Server\8.0\log\mongod.log" | findstr "dbPath"

# Atau cek ukuran database
dir "C:\Program Files\MongoDB\Server\8.0\data\psg-sisinfo"
```

#### Backup Manual Database Files (Alternative):
```bash
# Stop MongoDB service terlebih dahulu
net stop MongoDB

# Copy seluruh folder database
robocopy "C:\Program Files\MongoDB\Server\8.0\data\psg-sisinfo" "C:\BIS_Backup\database_files" /E /COPYALL

# Start MongoDB service kembali
net start MongoDB
```

**⚠️ PENTING**:
- Backup dengan `mongodump` lebih aman dan recommended
- Backup manual files hanya jika `mongodump` tidak tersedia
- Selalu stop MongoDB service sebelum copy database files manual

### 3. Backup File Uploads (Jika Ada)
```bash
# Backup folder uploads dari backend
xcopy "C:\project\pt-psg-taliabu\backend\uploads" "C:\BIS_Backup\uploads" /E /I /H /Y
```

### 4. Dokumentasi Environment
```bash
# Catat versi Node.js
node --version

# Catat versi npm
npm --version

# Catat versi MongoDB
mongod --version
```

## Transfer ke Komputer Tujuan

### 1. Copy Files
- Copy folder `C:\BIS_Backup` ke komputer tujuan
- Bisa menggunakan USB drive, network share, atau cloud storage
- Pastikan semua file ter-copy dengan lengkap

## Setup di Komputer Tujuan (Komputer Baru)

### 1. Install Prerequisites

#### Install Node.js
```bash
# Download dan install Node.js 18+ dari https://nodejs.org
# Verifikasi instalasi
node --version
npm --version
```

#### Install MongoDB
```bash
# Download MongoDB Community Server dari https://www.mongodb.com/try/download/community
# Install dengan default settings
# Pastikan MongoDB service berjalan

# Verifikasi MongoDB
mongod --version
mongo --version
```

#### Install Git (Optional tapi recommended)
```bash
# Download dari https://git-scm.com/download/win
```

### 2. Setup Project Structure
```bash
# Buat folder project di lokasi yang sama atau berbeda
mkdir C:\project
cd C:\project

# Copy project files dari backup
xcopy "C:\BIS_Backup\project" "C:\project\pt-psg-taliabu" /E /I /H /Y
```

### 3. Install Dependencies

#### Backend Dependencies
```bash
cd C:\project\pt-psg-taliabu\backend
npm install
```

#### Frontend Dependencies
```bash
cd C:\project\pt-psg-taliabu\frontend
npm install
```

### 4. Restore Database

#### Opsi A: Restore dengan mongorestore (Recommended)
```bash
# Restore seluruh database
mongorestore --host localhost:27017 --db psg-sisinfo C:\BIS_Backup\database_backup\psg-sisinfo

# Verifikasi restore berhasil
mongo psg-sisinfo --eval "db.stats()"
```

#### Opsi B: Import Collection Individual (Alternative)
```bash
# Import setiap collection
mongoimport --host localhost:27017 --db psg-sisinfo --collection users --file C:\BIS_Backup\users.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection employees --file C:\BIS_Backup\employees.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection departments --file C:\BIS_Backup\departments.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection positions --file C:\BIS_Backup\positions.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection employmenttypes --file C:\BIS_Backup\employmenttypes.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection rankcategories --file C:\BIS_Backup\rankcategories.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection rankgrades --file C:\BIS_Backup\rankgrades.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection ranksubgrades --file C:\BIS_Backup\ranksubgrades.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection tags --file C:\BIS_Backup\tags.json
mongoimport --host localhost:27017 --db psg-sisinfo --collection divisions --file C:\BIS_Backup\divisions.json
```

### 5. Restore File Uploads
```bash
# Restore folder uploads
xcopy "C:\BIS_Backup\uploads" "C:\project\pt-psg-taliabu\backend\uploads" /E /I /H /Y
```

### 6. Verifikasi Environment Files

#### Backend Environment (.env)
```bash
cd C:\project\pt-psg-taliabu\backend
# Pastikan file .env ada dengan konfigurasi yang benar:
```

```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/psg-sisinfo
JWT_SECRET=bebang-information-system-super-secret-key-2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=bebang-refresh-token-secret-key-2024
JWT_REFRESH_EXPIRES_IN=30d
CORS_ORIGIN=http://localhost:3000
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
APP_NAME=Bebang Information System
APP_VERSION=1.0.0
COMPANY_NAME=PT. Prima Sarana Gemilang
```

#### Frontend Environment (.env.local)
```bash
cd C:\project\pt-psg-taliabu\frontend
# Pastikan file .env.local ada dengan konfigurasi yang benar:
```

```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_WS_URL=http://localhost:5000
NEXT_PUBLIC_APP_NAME=Bebang Information System
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_COMPANY_NAME=PT. Prima Sarana Gemilang
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_LOG_LEVEL=info
NEXT_PUBLIC_ENABLE_CHAT=true
NEXT_PUBLIC_ENABLE_QR_SCANNER=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
```

## Testing dan Verifikasi

### 1. Start MongoDB Service
```bash
# Pastikan MongoDB service berjalan
net start MongoDB

# Atau start manual jika perlu
mongod --dbpath "C:\Program Files\MongoDB\Server\7.0\data"
```

### 2. Test Database Connection
```bash
# Test koneksi ke database
mongo psg-sisinfo --eval "db.users.count()"
mongo psg-sisinfo --eval "db.employees.count()"
mongo psg-sisinfo --eval "db.departments.count()"
```

### 3. Start Backend
```bash
cd C:\project\pt-psg-taliabu\backend
npm run dev
```

Pastikan output menunjukkan:
- ✅ MongoDB connected successfully
- 🚀 Server started successfully!
- 🌐 Server running on port 5000

### 4. Start Frontend
```bash
# Buka terminal baru
cd C:\project\pt-psg-taliabu\frontend
npm run dev
```

Pastikan output menunjukkan:
- ✓ Ready in [X]s
- Local: http://localhost:3000

### 5. Test Aplikasi
1. Buka browser ke http://localhost:3000
2. Login dengan kredensial: ADM001 / admin123
3. Verifikasi semua modul dapat diakses:
   - Dashboard
   - Master Data (7 modul)
   - Manajemen Karyawan
4. Test CRUD operations:
   - Buat data baru
   - Edit data existing
   - Hapus data
   - Verifikasi data tersimpan

### 6. Test Data Integrity
```bash
# Verifikasi jumlah data sama dengan komputer sumber
mongo psg-sisinfo --eval "
  print('Users: ' + db.users.count());
  print('Employees: ' + db.employees.count());
  print('Departments: ' + db.departments.count());
  print('Positions: ' + db.positions.count());
  print('Employment Types: ' + db.employmenttypes.count());
  print('Rank Categories: ' + db.rankcategories.count());
  print('Rank Grades: ' + db.rankgrades.count());
  print('Rank Subgrades: ' + db.ranksubgrades.count());
  print('Tags: ' + db.tags.count());
  print('Divisions: ' + db.divisions.count());
"
```

## Troubleshooting

### Problem: MongoDB Connection Failed
**Solution:**
```bash
# Check MongoDB service
net start MongoDB

# Check port 27017
netstat -ano | findstr :27017

# Restart MongoDB service
net stop MongoDB
net start MongoDB
```

### Problem: Port Already in Use
**Solution:**
```bash
# Check what's using port 5000
netstat -ano | findstr :5000

# Kill process if needed
taskkill /PID [PID_NUMBER] /F

# Check port 3000
netstat -ano | findstr :3000
```

### Problem: npm install Errors
**Solution:**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rmdir /s node_modules
del package-lock.json
npm install
```

### Problem: Database Empty After Restore
**Solution:**
```bash
# Check if database exists
mongo --eval "show dbs"

# Check collections
mongo psg-sisinfo --eval "show collections"

# Re-run restore command
mongorestore --host localhost:27017 --db psg-sisinfo --drop C:\BIS_Backup\database_backup\psg-sisinfo
```

### Problem: Cannot Find Database Files
**Solution:**
```bash
# Cek lokasi database MongoDB
dir "C:\Program Files\MongoDB\Server\8.0\data"

# Jika tidak ada, cek versi MongoDB lain
dir "C:\Program Files\MongoDB\Server" /AD

# Cek konfigurasi MongoDB
type "C:\Program Files\MongoDB\Server\8.0\bin\mongod.cfg"

# Cek dari MongoDB log
type "C:\Program Files\MongoDB\Server\8.0\log\mongod.log" | findstr "dbPath"
```

### Problem: Database Size Too Large
**Solution:**
```bash
# Cek ukuran database
dir "C:\Program Files\MongoDB\Server\8.0\data\psg-sisinfo"

# Compact database jika perlu
mongo psg-sisinfo --eval "db.runCommand({compact: 'collection_name'})"

# Repair database jika corrupt
mongod --repair --dbpath "C:\Program Files\MongoDB\Server\8.0\data"
```

## Checklist Migrasi Sukses

- [ ] Node.js 18+ terinstall
- [ ] MongoDB terinstall dan berjalan
- [ ] Project files ter-copy lengkap
- [ ] Dependencies terinstall (backend & frontend)
- [ ] Database ter-restore dengan lengkap
- [ ] Environment files terkonfigurasi benar
- [ ] Backend berjalan di port 5000
- [ ] Frontend berjalan di port 3000
- [ ] Login berhasil dengan ADM001/admin123
- [ ] Semua modul dapat diakses
- [ ] CRUD operations berfungsi normal
- [ ] Data count sama dengan komputer sumber
- [ ] File uploads ter-restore (jika ada)

## Tips Tambahan

1. **Backup Berkala**: Lakukan backup database secara berkala
2. **Version Control**: Gunakan Git untuk tracking perubahan code
3. **Documentation**: Update dokumentasi setiap ada perubahan
4. **Testing**: Selalu test semua fungsi setelah migrasi
5. **Environment**: Pastikan environment variables sesuai dengan komputer baru

## Kontak Support

Jika mengalami masalah selama migrasi, dokumentasikan:
1. Error message lengkap
2. Langkah yang sedang dilakukan
3. Screenshot jika perlu
4. Versi Node.js dan MongoDB yang digunakan

## Script Otomatis untuk Migrasi

### Script Backup (backup_bis.bat)
```batch
@echo off
echo ========================================
echo BIS Backup Script
echo ========================================

set BACKUP_DIR=C:\BIS_Backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%
echo Creating backup directory: %BACKUP_DIR%
mkdir "%BACKUP_DIR%"

echo.
echo [1/4] Backing up database...
mongodump --host localhost:27017 --db psg-sisinfo --out "%BACKUP_DIR%\database_backup"

echo.
echo [2/4] Backing up project files...
robocopy "C:\project\pt-psg-taliabu" "%BACKUP_DIR%\project" /E /COPYALL /R:3 /W:5

echo.
echo [3/4] Backing up uploads...
if exist "C:\project\pt-psg-taliabu\backend\uploads" (
    robocopy "C:\project\pt-psg-taliabu\backend\uploads" "%BACKUP_DIR%\uploads" /E /COPYALL /R:3 /W:5
)

echo.
echo [4/4] Creating info file...
echo BIS Backup Created: %date% %time% > "%BACKUP_DIR%\backup_info.txt"
echo Node.js Version: >> "%BACKUP_DIR%\backup_info.txt"
node --version >> "%BACKUP_DIR%\backup_info.txt"
echo MongoDB Version: >> "%BACKUP_DIR%\backup_info.txt"
mongod --version >> "%BACKUP_DIR%\backup_info.txt"

echo.
echo ========================================
echo Backup completed successfully!
echo Location: %BACKUP_DIR%
echo ========================================
pause
```

### Script Restore (restore_bis.bat)
```batch
@echo off
echo ========================================
echo BIS Restore Script
echo ========================================

set /p BACKUP_PATH="Enter backup folder path (e.g., C:\BIS_Backup_20240713): "

if not exist "%BACKUP_PATH%" (
    echo Error: Backup folder not found!
    pause
    exit /b 1
)

echo.
echo [1/5] Creating project directory...
mkdir "C:\project" 2>nul
cd /d "C:\project"

echo.
echo [2/5] Restoring project files...
robocopy "%BACKUP_PATH%\project" "C:\project\pt-psg-taliabu" /E /COPYALL /R:3 /W:5

echo.
echo [3/5] Restoring database...
mongorestore --host localhost:27017 --db psg-sisinfo --drop "%BACKUP_PATH%\database_backup\psg-sisinfo"

echo.
echo [4/5] Restoring uploads...
if exist "%BACKUP_PATH%\uploads" (
    robocopy "%BACKUP_PATH%\uploads" "C:\project\pt-psg-taliabu\backend\uploads" /E /COPYALL /R:3 /W:5
)

echo.
echo [5/5] Installing dependencies...
cd "C:\project\pt-psg-taliabu\backend"
call npm install

cd "C:\project\pt-psg-taliabu\frontend"
call npm install

echo.
echo ========================================
echo Restore completed successfully!
echo Next steps:
echo 1. Start MongoDB service: net start MongoDB
echo 2. Start backend: cd backend && npm run dev
echo 3. Start frontend: cd frontend && npm run dev
echo 4. Open http://localhost:3000
echo ========================================
pause
```

### Script Verifikasi (verify_bis.bat)
```batch
@echo off
echo ========================================
echo BIS Verification Script
echo ========================================

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo Error: Node.js not found!
    goto :error
)

echo.
echo Checking MongoDB...
mongod --version
if %errorlevel% neq 0 (
    echo Error: MongoDB not found!
    goto :error
)

echo.
echo Checking project structure...
if not exist "C:\project\pt-psg-taliabu\backend\package.json" (
    echo Error: Backend not found!
    goto :error
)

if not exist "C:\project\pt-psg-taliabu\frontend\package.json" (
    echo Error: Frontend not found!
    goto :error
)

echo.
echo Checking database...
mongo psg-sisinfo --eval "print('Database collections: ' + db.getCollectionNames().length)" --quiet
if %errorlevel% neq 0 (
    echo Error: Database connection failed!
    goto :error
)

echo.
echo Checking data integrity...
mongo psg-sisinfo --eval "
  print('Users: ' + db.users.count());
  print('Employees: ' + db.employees.count());
  print('Departments: ' + db.departments.count());
  print('Positions: ' + db.positions.count());
" --quiet

echo.
echo ========================================
echo All checks passed! BIS is ready to run.
echo ========================================
goto :end

:error
echo ========================================
echo Verification failed! Please check the issues above.
echo ========================================

:end
pause
```

## Migrasi Cloud/Remote

### Menggunakan MongoDB Atlas (Cloud Database)
```bash
# 1. Export dari local MongoDB
mongodump --host localhost:27017 --db psg-sisinfo --out ./local_backup

# 2. Import ke MongoDB Atlas
mongorestore --uri "mongodb+srv://username:<EMAIL>/psg-sisinfo" ./local_backup/psg-sisinfo

# 3. Update environment variables
# Backend .env:
MONGODB_URI=mongodb+srv://username:<EMAIL>/psg-sisinfo

# Frontend .env.local:
# (tidak perlu perubahan)
```

### Menggunakan Git Repository
```bash
# 1. Initialize Git (jika belum)
cd C:\project\pt-psg-taliabu
git init
git add .
git commit -m "Initial BIS project"

# 2. Push ke repository (GitHub/GitLab)
git remote add origin https://github.com/username/bis-project.git
git push -u origin main

# 3. Clone di komputer baru
git clone https://github.com/username/bis-project.git
cd bis-project

# 4. Install dependencies
cd backend && npm install
cd ../frontend && npm install

# 5. Restore database (tetap perlu backup/restore manual)
```

## Maintenance dan Update

### Update Berkala
```bash
# Update dependencies
cd backend && npm update
cd frontend && npm update

# Backup sebelum update major
# Gunakan script backup_bis.bat

# Test setelah update
npm run dev
```

### Monitoring
```bash
# Check disk space untuk database
dir "C:\Program Files\MongoDB\Server\7.0\data"

# Check log files
type "C:\Program Files\MongoDB\Server\7.0\log\mongod.log"

# Monitor aplikasi performance
# Gunakan Task Manager atau Process Monitor
```

---

**Catatan**: Tutorial ini dibuat berdasarkan konfigurasi BIS yang sudah berjalan dengan semua fitur HR module, employee management, dan master data yang lengkap. Semua script telah ditest dan siap digunakan untuk migrasi yang aman dan reliable.
