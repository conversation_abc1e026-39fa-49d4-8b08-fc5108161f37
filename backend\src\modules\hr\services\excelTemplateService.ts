import ExcelJS from "exceljs";

export interface TemplateField {
  column: string;
  field: string;
  label: string;
  required: boolean;
  example: string;
  validation?: string[];
}

// Template fields - hanya field wajib untuk quick implementation
export const ESSENTIAL_TEMPLATE_FIELDS: TemplateField[] = [
  {
    column: "A",
    field: "personal.employeeId",
    label: "NIK*",
    required: true,
    example: "EMP001",
  },
  {
    column: "B",
    field: "personal.fullName",
    label: "<PERSON>a <PERSON>*",
    required: true,
    example: "John Doe",
  },
  {
    column: "C",
    field: "personal.gender",
    label: "<PERSON><PERSON>",
    required: true,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "D",
    field: "personal.placeOfBirth",
    label: "Tempat Lahir*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "E",
    field: "personal.dateOfBirth",
    label: "<PERSON><PERSON>*",
    required: true,
    example: "1990-01-15",
  },
  {
    column: "F",
    field: "personal.phone",
    label: "No HP*",
    required: true,
    example: "081234567890",
  },
  {
    column: "G",
    field: "personal.religion",
    label: "Agama*",
    required: true,
    example: "Islam",
    validation: [
      "Islam",
      "Kristen",
      "Katolik",
      "Hindu",
      "Buddha",
      "Konghucu",
      "Lainnya",
    ],
  },
  {
    column: "H",
    field: "personal.bloodType",
    label: "Golongan Darah*",
    required: true,
    example: "A",
    validation: ["A", "B", "AB", "O"],
  },
  {
    column: "I",
    field: "hr.division",
    label: "Divisi*",
    required: true,
    example: "OPERATIONAL",
  },
  {
    column: "J",
    field: "hr.department",
    label: "Departemen*",
    required: true,
    example: "PRODUKSI",
  },
  {
    column: "K",
    field: "hr.position",
    label: "Jabatan*",
    required: true,
    example: "Staff",
  },
  {
    column: "L",
    field: "hr.contract.employmentType",
    label: "Status Kerja*",
    required: true,
    example: "TETAP",
  },
  {
    column: "M",
    field: "hr.contract.hireDate",
    label: "Tanggal Masuk*",
    required: true,
    example: "2024-01-15",
  },
  {
    column: "N",
    field: "hr.emergency.contactName",
    label: "Nama Kontak Darurat*",
    required: true,
    example: "Jane Doe",
  },
  {
    column: "O",
    field: "hr.emergency.contactPhone",
    label: "HP Kontak Darurat*",
    required: true,
    example: "081234567891",
  },
  {
    column: "P",
    field: "hr.emergency.address",
    label: "Alamat Kontak Darurat*",
    required: true,
    example: "Jl. Sudirman No. 123, Jakarta",
  },
];

export class ExcelTemplateService {
  async generateEssentialTemplate(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Employee Data
    const dataSheet = workbook.addWorksheet("Employee_Data");

    // Add headers
    const headerRow = dataSheet.addRow(
      ESSENTIAL_TEMPLATE_FIELDS.map((field) => field.label)
    );
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE6F3FF" },
    };

    // Add example data
    const exampleRow = dataSheet.addRow(
      ESSENTIAL_TEMPLATE_FIELDS.map((field) => field.example)
    );
    exampleRow.font = { italic: true };
    exampleRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFF0F8FF" },
    };

    // Auto-fit columns
    dataSheet.columns.forEach((column, index) => {
      const field = ESSENTIAL_TEMPLATE_FIELDS[index];
      column.width = Math.max(field.label.length, field.example.length) + 5;
    });

    // Sheet 2: Reference Data
    const refSheet = workbook.addWorksheet("Reference_Data");

    // Add reference data for dropdowns
    const referenceData = {
      "Jenis Kelamin": ["Laki-laki", "Perempuan"],
      Agama: [
        "Islam",
        "Kristen",
        "Katolik",
        "Hindu",
        "Buddha",
        "Konghucu",
        "Lainnya",
      ],
      "Golongan Darah": ["A", "B", "AB", "O"],
    };

    let currentCol = 1;
    Object.entries(referenceData).forEach(([title, values]) => {
      refSheet.getCell(1, currentCol).value = title;
      refSheet.getCell(1, currentCol).font = { bold: true };

      values.forEach((value, index) => {
        refSheet.getCell(index + 2, currentCol).value = value;
      });

      currentCol++;
    });

    // Sheet 3: Instructions
    const instructionSheet = workbook.addWorksheet("Instructions");

    const instructions = [
      "PETUNJUK PENGGUNAAN TEMPLATE EMPLOYEE IMPORT",
      "",
      '1. ISI DATA KARYAWAN PADA SHEET "Employee_Data"',
      "2. KOLOM DENGAN TANDA (*) WAJIB DIISI",
      "3. GUNAKAN FORMAT YANG SESUAI:",
      "   - Tanggal: YYYY-MM-DD (contoh: 2024-01-15)",
      "   - Jenis Kelamin: Laki-laki atau Perempuan",
      "   - Agama: Islam, Kristen, Katolik, Hindu, Buddha, Konghucu, Lainnya",
      "   - Golongan Darah: A, B, AB, atau O",
      "",
      "4. JANGAN MENGUBAH NAMA KOLOM ATAU SHEET",
      "5. MAKSIMAL 500 BARIS DATA PER UPLOAD",
      "6. HAPUS BARIS CONTOH SEBELUM UPLOAD",
      "",
      "FIELD WAJIB YANG HARUS DIISI:",
      "- NIK (Nomor Induk Karyawan)",
      "- Nama Lengkap",
      "- Jenis Kelamin",
      "- Tempat Lahir",
      "- Tanggal Lahir",
      "- No HP",
      "- Agama",
      "- Golongan Darah",
      "- Divisi",
      "- Departemen",
      "- Jabatan",
      "- Status Kerja",
      "- Tanggal Masuk",
      "- Nama Kontak Darurat",
      "- HP Kontak Darurat",
      "- Alamat Kontak Darurat",
    ];

    instructions.forEach((instruction, index) => {
      const cell = instructionSheet.getCell(index + 1, 1);
      cell.value = instruction;
      if (index === 0) {
        cell.font = { bold: true, size: 14 };
      } else if (
        instruction.startsWith("FIELD WAJIB") ||
        instruction.match(/^\d+\./)
      ) {
        cell.font = { bold: true };
      }
    });

    instructionSheet.getColumn(1).width = 50;

    // Convert to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async generateCustomTemplate(_selectedFields: string[]): Promise<Buffer> {
    // Implementation for custom template
    // For now, return essential template
    return this.generateEssentialTemplate();
  }
}

export const excelTemplateService = new ExcelTemplateService();
