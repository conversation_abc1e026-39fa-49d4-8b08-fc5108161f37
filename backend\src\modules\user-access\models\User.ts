import mongoose, { Schema, Types } from "mongoose";
import bcrypt from "bcryptjs";
import {
  BaseDocument,
  TimestampFields,
  SoftDeleteFields,
} from "../../../shared/types/common";

export interface IUser extends BaseDocument, TimestampFields, SoftDeleteFields {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  fullName: string;
  employeeId?: string;
  phone?: string;
  avatar?: string;
  role: Types.ObjectId;
  permissions: string[];
  isActive: boolean;
  isEmailVerified: boolean;
  emailVerifiedAt?: Date;
  lastLoginAt?: Date;
  lastLoginIP?: string;
  passwordChangedAt?: Date;
  refreshTokens: string[];
  loginAttempts: number;
  lockUntil?: Date;

  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateFullName(): string;
  isAccountLocked(): boolean;
  incrementLoginAttempts(): Promise<void>;
  resetLoginAttempts(): Promise<void>;
}

const userSchema = new Schema<IUser>(
  {
    email: {
      type: String,
      required: [true, "Email wajib diisi"],
      unique: true,
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Format email tidak valid",
      ],
    },
    password: {
      type: String,
      required: [true, "Password wajib diisi"],
      minlength: [6, "Password minimal 6 karakter"],
      select: false,
    },
    firstName: {
      type: String,
      required: [true, "Nama depan wajib diisi"],
      trim: true,
      maxlength: [50, "Nama depan maksimal 50 karakter"],
    },
    lastName: {
      type: String,
      required: [true, "Nama belakang wajib diisi"],
      trim: true,
      maxlength: [50, "Nama belakang maksimal 50 karakter"],
    },
    fullName: {
      type: String,
      trim: true,
    },
    employeeId: {
      type: String,
      unique: true,
      sparse: true,
      trim: true,
      uppercase: true,
    },
    phone: {
      type: String,
      trim: true,
      match: [
        /^(\+62|62|0)8[1-9][0-9]{6,9}$/,
        "Format nomor telepon tidak valid",
      ],
    },
    avatar: {
      type: String,
      trim: true,
    },
    role: {
      type: Schema.Types.ObjectId,
      ref: "Role",
      required: [true, "Role wajib diisi"],
    },
    permissions: [
      {
        type: String,
        trim: true,
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    emailVerifiedAt: {
      type: Date,
    },
    lastLoginAt: {
      type: Date,
    },
    lastLoginIP: {
      type: String,
      trim: true,
    },
    passwordChangedAt: {
      type: Date,
      default: Date.now,
    },
    refreshTokens: [
      {
        type: String,
      },
    ],
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: function (_doc, ret) {
        delete ret.password;
        delete ret.refreshTokens;
        delete ret.__v;
        return ret;
      },
    },
    toObject: { virtuals: true },
  }
);

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ employeeId: 1 });
userSchema.index({ role: 1 });
userSchema.index({ isActive: 1, isDeleted: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for account lock status
userSchema.virtual("isLocked").get(function () {
  return !!(this.lockUntil && this.lockUntil > new Date());
});

// Pre-save middleware
userSchema.pre("save", async function (next) {
  // Generate full name
  this.fullName = this.generateFullName();

  // Hash password if modified
  if (this.isModified("password")) {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    this.passwordChangedAt = new Date();
  }

  next();
});

// Instance methods
userSchema.methods.comparePassword = async function (
  candidatePassword: string
): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateFullName = function (): string {
  return `${this.firstName} ${this.lastName}`.trim();
};

userSchema.methods.isAccountLocked = function (): boolean {
  return !!(this.lockUntil && this.lockUntil > new Date());
};

userSchema.methods.incrementLoginAttempts = async function (): Promise<void> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    });
  }

  const updates: any = { $inc: { loginAttempts: 1 } };

  // If we have max attempts and no lock, lock account
  if (this.loginAttempts + 1 >= 5 && !this.isAccountLocked()) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }

  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = async function (): Promise<void> {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
  });
};

// Static methods
userSchema.statics.findByEmail = function (email: string) {
  return this.findOne({ email: email.toLowerCase(), isDeleted: false });
};

userSchema.statics.findByEmployeeId = function (employeeId: string) {
  return this.findOne({
    employeeId: employeeId.toUpperCase(),
    isDeleted: false,
  });
};

export const User = mongoose.model<IUser>("User", userSchema);
export default User;
