import { Request, Response } from "express";
import { User, IUser } from "../models/User";
import { IRole } from "../models/Role";
import { JWTService } from "../../../config/jwt";
import { ApiResponse } from "../../../shared/types/common";

export interface LoginRequest {
  username: string; // employeeId or email
  password: string;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    fullName: string;
    employeeId?: string | undefined;
    role: {
      id: string;
      name: string;
      permissions: string[];
    };
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * User login
 */
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { username, password }: LoginRequest = req.body;

    // Validation
    if (!username || !password) {
      res.status(400).json({
        success: false,
        message: "Username dan password wajib diisi",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Find user by employeeId or email
    const user = await User.findOne({
      $and: [
        {
          $or: [
            { employeeId: username.toUpperCase() },
            { email: username.toLowerCase() },
          ],
        },
        {
          $or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
        },
      ],
    })
      .populate("role")
      .select("+password");

    if (!user) {
      res.status(401).json({
        success: false,
        message: "Username atau password tidak valid",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Check if account is locked
    if (user.isAccountLocked()) {
      res.status(423).json({
        success: false,
        message:
          "Akun terkunci karena terlalu banyak percobaan login. Coba lagi nanti.",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Check if user is active
    if (!user.isActive) {
      res.status(401).json({
        success: false,
        message: "Akun tidak aktif. Hubungi administrator.",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      // Increment login attempts
      await user.incrementLoginAttempts();

      res.status(401).json({
        success: false,
        message: "Username atau password tidak valid",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }

    // Check role
    const userRole = user.role as any as IRole;
    if (!userRole || !userRole.isActive) {
      res.status(403).json({
        success: false,
        message: "Role tidak aktif. Hubungi administrator.",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Generate tokens
    const tokenPayload = {
      userId: user._id.toString(),
      email: user.email,
      role: userRole.name,
      permissions: userRole.getPermissionsList(),
    };

    const tokens = JWTService.generateTokenPair(tokenPayload);

    // Save refresh token
    user.refreshTokens.push(tokens.refreshToken);
    user.lastLoginAt = new Date();
    user.lastLoginIP = req.ip || "";
    await user.save();

    // Prepare response
    const response: LoginResponse = {
      user: {
        id: user._id.toString(),
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        fullName: user.fullName,
        employeeId: user.employeeId || undefined,
        role: {
          id: userRole._id.toString(),
          name: userRole.name,
          permissions: userRole.getPermissionsList(),
        },
      },
      tokens,
    };

    res.status(200).json({
      success: true,
      message: "Login berhasil",
      data: response,
      timestamp: new Date(),
    } as ApiResponse<LoginResponse>);
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Terjadi kesalahan saat login",
      error: "INTERNAL_SERVER_ERROR",
      timestamp: new Date(),
    } as ApiResponse);
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { refreshToken }: RefreshTokenRequest = req.body;

    if (!refreshToken) {
      res.status(400).json({
        success: false,
        message: "Refresh token wajib diisi",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Verify refresh token
    let decoded;
    try {
      decoded = JWTService.verifyRefreshToken(refreshToken);
    } catch (error) {
      res.status(401).json({
        success: false,
        message: "Refresh token tidak valid",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Find user
    const user = await User.findById(decoded.userId)
      .populate("role")
      .select("+refreshTokens");

    if (!user || !user.isActive || user.isDeleted) {
      res.status(401).json({
        success: false,
        message: "User tidak valid",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Check if refresh token exists in user's tokens
    if (!user.refreshTokens.includes(refreshToken)) {
      res.status(401).json({
        success: false,
        message: "Refresh token tidak valid",
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    // Generate new tokens
    const userRole = user.role as any as IRole;
    const tokenPayload = {
      userId: user._id.toString(),
      email: user.email,
      role: userRole.name,
      permissions: userRole.getPermissionsList(),
    };

    const newTokens = JWTService.generateTokenPair(tokenPayload);

    // Replace old refresh token with new one
    const tokenIndex = user.refreshTokens.indexOf(refreshToken);
    user.refreshTokens[tokenIndex] = newTokens.refreshToken;
    await user.save();

    res.status(200).json({
      success: true,
      message: "Token berhasil diperbarui",
      data: { tokens: newTokens },
      timestamp: new Date(),
    } as ApiResponse);
  } catch (error) {
    console.error("Refresh token error:", error);
    res.status(500).json({
      success: false,
      message: "Terjadi kesalahan saat memperbarui token",
      error: "INTERNAL_SERVER_ERROR",
      timestamp: new Date(),
    } as ApiResponse);
  }
};

/**
 * User logout
 */
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken }: RefreshTokenRequest = req.body;
    const user = req.user as IUser;

    if (refreshToken && user) {
      // Remove refresh token from user's tokens
      user.refreshTokens = user.refreshTokens.filter(
        (token) => token !== refreshToken
      );
      await user.save();
    }

    res.status(200).json({
      success: true,
      message: "Logout berhasil",
      timestamp: new Date(),
    } as ApiResponse);
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      success: false,
      message: "Terjadi kesalahan saat logout",
      error: "INTERNAL_SERVER_ERROR",
      timestamp: new Date(),
    } as ApiResponse);
  }
};

/**
 * Get current user profile
 */
export const getProfile = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const user = req.user as IUser;
    const userRole = req.userRole as IRole;

    const profile = {
      id: user._id.toString(),
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: user.fullName,
      employeeId: user.employeeId,
      phone: user.phone,
      avatar: user.avatar,
      isEmailVerified: user.isEmailVerified,
      lastLoginAt: user.lastLoginAt,
      role: {
        id: userRole._id.toString(),
        name: userRole.name,
        description: userRole.description,
        permissions: userRole.getPermissionsList(),
      },
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    res.status(200).json({
      success: true,
      message: "Profil berhasil diambil",
      data: profile,
      timestamp: new Date(),
    } as ApiResponse);
  } catch (error) {
    console.error("Get profile error:", error);
    res.status(500).json({
      success: false,
      message: "Terjadi kesalahan saat mengambil profil",
      error: "INTERNAL_SERVER_ERROR",
      timestamp: new Date(),
    } as ApiResponse);
  }
};
