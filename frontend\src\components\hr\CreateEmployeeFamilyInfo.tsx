"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { CalendarIcon, Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker";
import { cn } from "@/lib/utils";

interface Child {
  name: string;
  gender: "<PERSON><PERSON>-laki" | "Perempuan" | "";
  dateOfBirth: string;
}

interface Sibling {
  name: string;
  gender: "Laki-laki" | "Perempuan" | "";
  dateOfBirth: string;
  lastEducation: string;
  occupation: string;
  description: string;
}

interface FamilyInfoData {
  // Spouse and Children
  spouse: {
    name: string;
    dateOfBirth: string;
    marriageDate: string;
    lastEducation: string;
    occupation: string;
    numberOfChildren: number;
  };

  // Children Identity (up to 4 children)
  children: Child[];

  // Parents Information
  parents: {
    father: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      occupation: string;
      description: string;
    };
    mother: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      occupation: string;
      description: string;
    };
  };

  // Siblings Information
  siblings: {
    childOrder: number;
    totalSiblings: number;
    siblingsData: Sibling[];
  };

  // In-laws Information
  inLaws: {
    fatherInLaw: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      description: string;
    };
    motherInLaw: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      description: string;
    };
  };
}

interface CreateEmployeeFamilyInfoProps {
  data: FamilyInfoData;
  onUpdate: (data: Partial<FamilyInfoData>) => void;
}

const genderOptions = [
  { value: "Laki-laki", label: "Laki-laki" },
  { value: "Perempuan", label: "Perempuan" },
];

const educationOptions = [
  "SD",
  "SMP",
  "SMA",
  "D1",
  "D2",
  "D3",
  "S1",
  "S2",
  "S3",
  "Tidak Sekolah",
  "Lainnya",
];

export default function CreateEmployeeFamilyInfo({
  data,
  onUpdate,
}: CreateEmployeeFamilyInfoProps) {
  // Helper function to safely get nested value
  const getNestedValue = (obj: any, path: string): any => {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  };

  // Helper function to safely set nested value
  const setNestedValue = (obj: any, path: string, value: any): any => {
    const keys = path.split(".");
    const result = { ...obj };
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (current[key] === undefined || current[key] === null) {
        current[key] = {};
      } else {
        current[key] = { ...current[key] };
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
    return result;
  };

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    let updateData: any = {};

    if (keys.length === 1) {
      updateData[field] = value;
    } else if (keys.length === 2) {
      updateData[keys[0]] = {
        ...data[keys[0] as keyof FamilyInfoData],
        [keys[1]]: value,
      };
    } else if (keys.length === 3) {
      updateData[keys[0]] = {
        ...data[keys[0] as keyof FamilyInfoData],
        [keys[1]]: {
          ...(data[keys[0] as keyof FamilyInfoData] as any)?.[keys[1]],
          [keys[2]]: value,
        },
      };
    }

    onUpdate(updateData);
  };

  const handleChildChange = (index: number, field: string, value: any) => {
    const updatedChildren = [...data.children];
    updatedChildren[index] = { ...updatedChildren[index], [field]: value };
    onUpdate({ children: updatedChildren });
  };

  const handleSiblingChange = (index: number, field: string, value: any) => {
    const updatedSiblingsData = [...data.siblings.siblingsData];
    updatedSiblingsData[index] = {
      ...updatedSiblingsData[index],
      [field]: value,
    };
    onUpdate({
      siblings: { ...data.siblings, siblingsData: updatedSiblingsData },
    });
  };

  const addChild = () => {
    if (data.children.length < 4) {
      onUpdate({
        children: [...data.children, { name: "", gender: "", dateOfBirth: "" }],
      });
    }
  };

  const removeChild = (index: number) => {
    onUpdate({
      children: data.children.filter((_, i) => i !== index),
    });
  };

  const addSibling = () => {
    if (data.siblings.siblingsData.length < 5) {
      onUpdate({
        siblings: {
          ...data.siblings,
          siblingsData: [
            ...data.siblings.siblingsData,
            {
              name: "",
              gender: "",
              dateOfBirth: "",
              lastEducation: "",
              occupation: "",
              description: "",
            },
          ],
        },
      });
    }
  };

  const removeSibling = (index: number) => {
    onUpdate({
      siblings: {
        ...data.siblings,
        siblingsData: data.siblings.siblingsData.filter((_, i) => i !== index),
      },
    });
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type: "text" | "select" | "date" | "number" | "textarea" = "text",
    options?: string[] | { value: string; label: string }[],
    required = false
  ) => (
    <div className={type === "textarea" ? "md:col-span-2" : ""}>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {type === "select" ? (
        <Select
          value={getNestedValue(data, field) || ""}
          onValueChange={(value) => handleInputChange(field, value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {options?.map((option) => (
              <SelectItem
                key={typeof option === "string" ? option : option.value}
                value={typeof option === "string" ? option : option.value}
              >
                {typeof option === "string" ? option : option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : type === "date" ? (
        <EnhancedDatePicker
          value={
            getNestedValue(data, field)
              ? new Date(getNestedValue(data, field))
              : undefined
          }
          onChange={(date) =>
            handleInputChange(field, date ? date.toISOString() : "")
          }
          placeholder="Pilih tanggal"
          minYear={1950}
          maxYear={new Date().getFullYear()}
        />
      ) : type === "textarea" ? (
        <Textarea
          id={field}
          value={getNestedValue(data, field) || ""}
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={`Masukkan ${label.toLowerCase()}`}
          rows={3}
        />
      ) : (
        <Input
          id={field}
          type={type}
          value={getNestedValue(data, field) || ""}
          onChange={(e) =>
            handleInputChange(
              field,
              type === "number" ? Number(e.target.value) : e.target.value
            )
          }
          placeholder={`Masukkan ${label.toLowerCase()}`}
        />
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informasi Keluarga</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Spouse and Children */}
        {renderFormGroup(
          "Pasangan dan Anak",
          <>
            {renderField("Nama Pasangan", "spouse.name", "text")}
            {renderField(
              "Tanggal Lahir Pasangan",
              "spouse.dateOfBirth",
              "date"
            )}
            {renderField("Tanggal Menikah", "spouse.marriageDate", "date")}
            {renderField(
              "Pendidikan Terakhir Pasangan",
              "spouse.lastEducation",
              "select",
              educationOptions
            )}
            {renderField("Pekerjaan Pasangan", "spouse.occupation", "text")}
            {renderField("Jumlah Anak", "spouse.numberOfChildren", "number")}
          </>
        )}

        <Separator />

        {/* Children Identity */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 border-b pb-2">
              Identitas Anak
            </h4>
            {data.children.length < 4 && (
              <Button size="sm" variant="outline" onClick={addChild}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Anak
              </Button>
            )}
          </div>

          {data.children.map((child, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h5 className="font-medium">Anak {index + 1}</h5>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => removeChild(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>Nama Anak {index + 1}</Label>
                  <Input
                    value={child.name}
                    onChange={(e) =>
                      handleChildChange(index, "name", e.target.value)
                    }
                    placeholder="Masukkan nama anak"
                  />
                </div>

                <div>
                  <Label>Jenis Kelamin Anak {index + 1}</Label>
                  <Select
                    value={child.gender}
                    onValueChange={(value) =>
                      handleChildChange(index, "gender", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih jenis kelamin" />
                    </SelectTrigger>
                    <SelectContent>
                      {genderOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Tanggal Lahir Anak {index + 1}</Label>
                  <EnhancedDatePicker
                    value={
                      child.dateOfBirth
                        ? new Date(child.dateOfBirth)
                        : undefined
                    }
                    onChange={(date) =>
                      handleChildChange(
                        index,
                        "dateOfBirth",
                        date ? date.toISOString() : ""
                      )
                    }
                    placeholder="Pilih tanggal"
                    minYear={2000}
                    maxYear={new Date().getFullYear()}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Parents Information */}
        {renderFormGroup(
          "Orang Tua Kandung",
          <>
            {renderField("Nama Ayah Kandung", "parents.father.name", "text")}
            {renderField(
              "Tanggal Lahir Ayah Kandung",
              "parents.father.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ayah Kandung",
              "parents.father.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Pekerjaan Ayah Kandung",
              "parents.father.occupation",
              "text"
            )}
            {renderField(
              "Keterangan Ayah Kandung",
              "parents.father.description",
              "textarea"
            )}
            {renderField("Nama Ibu Kandung", "parents.mother.name", "text")}
            {renderField(
              "Tanggal Lahir Ibu Kandung",
              "parents.mother.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ibu Kandung",
              "parents.mother.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Pekerjaan Ibu Kandung",
              "parents.mother.occupation",
              "text"
            )}
            {renderField(
              "Keterangan Ibu Kandung",
              "parents.mother.description",
              "textarea"
            )}
          </>
        )}

        <Separator />

        {/* Siblings Information */}
        {renderFormGroup(
          "Saudara Kandung",
          <>
            {renderField("Anak Ke", "siblings.childOrder", "number")}
            {renderField(
              "Jumlah Saudara Kandung",
              "siblings.totalSiblings",
              "number"
            )}
          </>
        )}

        {/* Siblings Identity */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 border-b pb-2">
              Identitas Saudara Kandung
            </h4>
            {data.siblings.siblingsData.length < 5 && (
              <Button size="sm" variant="outline" onClick={addSibling}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Saudara
              </Button>
            )}
          </div>

          {data.siblings.siblingsData.map((sibling, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h5 className="font-medium">Saudara Kandung {index + 1}</h5>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => removeSibling(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Nama Saudara Kandung {index + 1}</Label>
                  <Input
                    value={sibling.name}
                    onChange={(e) =>
                      handleSiblingChange(index, "name", e.target.value)
                    }
                    placeholder="Masukkan nama saudara"
                  />
                </div>

                <div>
                  <Label>Jenis Kelamin</Label>
                  <Select
                    value={sibling.gender}
                    onValueChange={(value) =>
                      handleSiblingChange(index, "gender", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih jenis kelamin" />
                    </SelectTrigger>
                    <SelectContent>
                      {genderOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Tanggal Lahir</Label>
                  <EnhancedDatePicker
                    value={
                      sibling.dateOfBirth
                        ? new Date(sibling.dateOfBirth)
                        : undefined
                    }
                    onChange={(date) =>
                      handleSiblingChange(
                        index,
                        "dateOfBirth",
                        date ? date.toISOString() : ""
                      )
                    }
                    placeholder="Pilih tanggal"
                    minYear={1950}
                    maxYear={new Date().getFullYear()}
                  />
                </div>

                <div>
                  <Label>Pendidikan Terakhir</Label>
                  <Select
                    value={sibling.lastEducation}
                    onValueChange={(value) =>
                      handleSiblingChange(index, "lastEducation", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih pendidikan" />
                    </SelectTrigger>
                    <SelectContent>
                      {educationOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Pekerjaan</Label>
                  <Input
                    value={sibling.occupation}
                    onChange={(e) =>
                      handleSiblingChange(index, "occupation", e.target.value)
                    }
                    placeholder="Masukkan pekerjaan"
                  />
                </div>

                <div className="md:col-span-2">
                  <Label>Keterangan</Label>
                  <Textarea
                    value={sibling.description}
                    onChange={(e) =>
                      handleSiblingChange(index, "description", e.target.value)
                    }
                    placeholder="Masukkan keterangan"
                    rows={2}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* In-laws Information */}
        {renderFormGroup(
          "Orang Tua Mertua",
          <>
            {renderField("Nama Ayah Mertua", "inLaws.fatherInLaw.name", "text")}
            {renderField(
              "Tanggal Lahir Ayah Mertua",
              "inLaws.fatherInLaw.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ayah Mertua",
              "inLaws.fatherInLaw.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Keterangan Ayah Mertua",
              "inLaws.fatherInLaw.description",
              "textarea"
            )}
            {renderField("Nama Ibu Mertua", "inLaws.motherInLaw.name", "text")}
            {renderField(
              "Tanggal Lahir Ibu Mertua",
              "inLaws.motherInLaw.dateOfBirth",
              "date"
            )}
            {renderField(
              "Pendidikan Terakhir Ibu Mertua",
              "inLaws.motherInLaw.lastEducation",
              "select",
              educationOptions
            )}
            {renderField(
              "Keterangan Ibu Mertua",
              "inLaws.motherInLaw.description",
              "textarea"
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
