"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { CalendarIcon, Edit, Save, X } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface PersonalInfoData {
  // Employee Biodata
  fullName: string;
  gender: "<PERSON><PERSON>-la<PERSON>" | "Perempuan" | "";
  placeOfBirth: string;
  dateOfBirth: Date | undefined;

  // Identification
  religion: string;
  bloodType: "A" | "B" | "AB" | "O" | "";
  familyCardNumber: string;
  idCardNumber: string;
  taxNumber: string;
  bpjsTkNumber: string;
  nikKkNumber: string;
  taxStatus: string;

  // Current Address
  currentAddress: {
    street: string;
    city: string;
    province: string;
  };

  // ID Card Address
  idCardAddress: {
    street: string;
    city: string;
    province: string;
  };

  // Contact Information
  contact: {
    mobilePhone1: string;
    mobilePhone2: string;
    homePhone1: string;
    homePhone2: string;
  };

  // Marital Status and Children
  maritalInfo: {
    status: string;
    spouseName: string;
    spouseJob: string;
    numberOfChildren: number;
  };

  // Bank Account
  bankAccount: {
    accountNumber: string;
    accountHolder: string;
    bankName: string;
  };
}

interface EmployeePersonalInfoProps {
  data?: PersonalInfoData;
  onSave: (data: PersonalInfoData) => void;
  isEditing: boolean;
  onEditToggle: () => void;
}

const religionOptions = [
  "Islam",
  "Kristen",
  "Katolik",
  "Hindu",
  "Buddha",
  "Konghucu",
  "Lainnya",
];

const bloodTypeOptions = ["A", "B", "AB", "O"];

const genderOptions = [
  { value: "Laki-laki", label: "Laki-laki" },
  { value: "Perempuan", label: "Perempuan" },
];

const maritalStatusOptions = [
  "Belum Menikah",
  "Menikah",
  "Cerai",
  "Janda/Duda",
];

const taxStatusOptions = [
  "TK/0",
  "TK/1",
  "TK/2",
  "TK/3",
  "K/0",
  "K/1",
  "K/2",
  "K/3",
];

export default function EmployeePersonalInfo({
  data,
  onSave,
  isEditing,
  onEditToggle,
}: EmployeePersonalInfoProps) {
  const [formData, setFormData] = useState<PersonalInfoData>({
    // Employee Biodata
    fullName: data?.fullName || "",
    gender: data?.gender || "",
    placeOfBirth: data?.placeOfBirth || "",
    dateOfBirth: data?.dateOfBirth,

    // Identification
    religion: data?.religion || "",
    bloodType: data?.bloodType || "",
    familyCardNumber: data?.familyCardNumber || "",
    idCardNumber: data?.idCardNumber || "",
    taxNumber: data?.taxNumber || "",
    bpjsTkNumber: data?.bpjsTkNumber || "",
    nikKkNumber: data?.nikKkNumber || "",
    taxStatus: data?.taxStatus || "",

    // Current Address
    currentAddress: {
      street: data?.currentAddress?.street || "",
      city: data?.currentAddress?.city || "",
      province: data?.currentAddress?.province || "",
    },

    // ID Card Address
    idCardAddress: {
      street: data?.idCardAddress?.street || "",
      city: data?.idCardAddress?.city || "",
      province: data?.idCardAddress?.province || "",
    },

    // Contact Information
    contact: {
      mobilePhone1: data?.contact?.mobilePhone1 || "",
      mobilePhone2: data?.contact?.mobilePhone2 || "",
      homePhone1: data?.contact?.homePhone1 || "",
      homePhone2: data?.contact?.homePhone2 || "",
    },

    // Marital Status and Children
    maritalInfo: {
      status: data?.maritalInfo?.status || "",
      spouseName: data?.maritalInfo?.spouseName || "",
      spouseJob: data?.maritalInfo?.spouseJob || "",
      numberOfChildren: data?.maritalInfo?.numberOfChildren || 0,
    },

    // Bank Account
    bankAccount: {
      accountNumber: data?.bankAccount?.accountNumber || "",
      accountHolder: data?.bankAccount?.accountHolder || "",
      bankName: data?.bankAccount?.bankName || "",
    },
  });

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    if (keys.length === 1) {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    } else if (keys.length === 2) {
      setFormData((prev) => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0] as keyof PersonalInfoData],
          [keys[1]]: value,
        },
      }));
    }
  };

  const copyAddressToIdCard = () => {
    setFormData((prev) => ({
      ...prev,
      idCardAddress: { ...prev.currentAddress },
    }));
    toast.success("Alamat domisili berhasil disalin ke alamat KTP");
  };

  const handleSave = () => {
    // Validate required fields
    const requiredFields = [
      { field: "fullName", label: "Nama lengkap" },
      { field: "gender", label: "Jenis kelamin" },
      { field: "placeOfBirth", label: "Tempat lahir" },
      { field: "dateOfBirth", label: "Tanggal lahir" },
      { field: "religion", label: "Agama" },
      { field: "bloodType", label: "Golongan darah" },
      { field: "familyCardNumber", label: "Nomor KK" },
      { field: "idCardNumber", label: "Nomor KTP" },
      { field: "taxStatus", label: "Status pajak" },
    ];

    for (const { field, label } of requiredFields) {
      if (!formData[field as keyof PersonalInfoData]) {
        toast.error(`${label} harus diisi`);
        return;
      }
    }

    // Validate nested required fields
    const nestedRequiredFields = [
      { field: "currentAddress.street", label: "Alamat domisili - Jalan" },
      { field: "currentAddress.city", label: "Alamat domisili - Kota" },
      { field: "currentAddress.province", label: "Alamat domisili - Provinsi" },
      { field: "idCardAddress.street", label: "Alamat KTP - Jalan" },
      { field: "idCardAddress.city", label: "Alamat KTP - Kota" },
      { field: "idCardAddress.province", label: "Alamat KTP - Provinsi" },
      { field: "contact.mobilePhone1", label: "Nomor HP 1" },
      { field: "maritalInfo.status", label: "Status pernikahan" },
      { field: "bankAccount.accountNumber", label: "Nomor rekening" },
      { field: "bankAccount.accountHolder", label: "Nama pemegang rekening" },
      { field: "bankAccount.bankName", label: "Nama bank" },
    ];

    for (const { field, label } of nestedRequiredFields) {
      const keys = field.split(".");
      let value = formData;
      for (const key of keys) {
        value = value[key as keyof typeof value];
      }
      if (!value) {
        toast.error(`${label} harus diisi`);
        return;
      }
    }

    onSave(formData);
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type: "text" | "select" | "date" | "number" = "text",
    options?: string[] | { value: string; label: string }[],
    required = false
  ) => (
    <div>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {isEditing ? (
        type === "select" ? (
          <Select
            value={
              field.includes(".")
                ? field.split(".").reduce((obj, key) => obj?.[key], formData)
                : (formData[field as keyof PersonalInfoData] as string)
            }
            onValueChange={(value) => handleInputChange(field, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem
                  key={typeof option === "string" ? option : option.value}
                  value={typeof option === "string" ? option : option.value}
                >
                  {typeof option === "string" ? option : option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : type === "date" ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.dateOfBirth && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.dateOfBirth ? (
                  format(formData.dateOfBirth, "dd MMMM yyyy", { locale: id })
                ) : (
                  <span>Pilih tanggal</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.dateOfBirth}
                onSelect={(date) => handleInputChange(field, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : (
          <Input
            id={field}
            type={type}
            value={
              field.includes(".")
                ? field.split(".").reduce((obj, key) => obj?.[key], formData)
                : (formData[field as keyof PersonalInfoData] as string)
            }
            onChange={(e) =>
              handleInputChange(
                field,
                type === "number" ? Number(e.target.value) : e.target.value
              )
            }
            placeholder={`Masukkan ${label.toLowerCase()}`}
          />
        )
      ) : (
        <p className="text-sm text-gray-700">
          {field.includes(".")
            ? field.split(".").reduce((obj, key) => obj?.[key], formData) || "-"
            : (formData[field as keyof PersonalInfoData] as string) || "-"}
        </p>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Informasi Personal</CardTitle>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Simpan
              </Button>
              <Button size="sm" variant="outline" onClick={onEditToggle}>
                <X className="w-4 h-4 mr-2" />
                Batal
              </Button>
            </>
          ) : (
            <Button size="sm" variant="outline" onClick={onEditToggle}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Employee Biodata */}
        {renderFormGroup(
          "Data Karyawan",
          <>
            {renderField("Nama Lengkap", "fullName", "text", undefined, true)}
            {renderField(
              "Jenis Kelamin",
              "gender",
              "select",
              genderOptions,
              true
            )}
            {renderField(
              "Tempat Lahir",
              "placeOfBirth",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Tanggal Lahir",
              "dateOfBirth",
              "date",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* Identification */}
        {renderFormGroup(
          "Identifikasi",
          <>
            {renderField("Agama", "religion", "select", religionOptions, true)}
            {renderField(
              "Golongan Darah",
              "bloodType",
              "select",
              bloodTypeOptions,
              true
            )}
            {renderField(
              "Nomor KK",
              "familyCardNumber",
              "text",
              undefined,
              true
            )}
            {renderField("Nomor KTP", "idCardNumber", "text", undefined, true)}
            {renderField("NPWP", "taxNumber", "text")}
            {renderField("BPJS TK", "bpjsTkNumber", "text")}
            {renderField("NIK KK", "nikKkNumber", "text")}
            {renderField(
              "Status Pajak",
              "taxStatus",
              "select",
              taxStatusOptions,
              true
            )}
          </>
        )}

        <Separator />

        {/* Current Address */}
        {renderFormGroup(
          "Alamat Domisili",
          <>
            {renderField(
              "Jalan",
              "currentAddress.street",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Kota",
              "currentAddress.city",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Provinsi",
              "currentAddress.province",
              "text",
              undefined,
              true
            )}
            <div></div> {/* Empty div for grid alignment */}
          </>
        )}

        <Separator />

        {/* ID Card Address */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 border-b pb-2">
              Alamat KTP
            </h4>
            {isEditing && (
              <Button size="sm" variant="outline" onClick={copyAddressToIdCard}>
                Salin dari Alamat Domisili
              </Button>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderField(
              "Jalan",
              "idCardAddress.street",
              "text",
              undefined,
              true
            )}
            {renderField("Kota", "idCardAddress.city", "text", undefined, true)}
            {renderField(
              "Provinsi",
              "idCardAddress.province",
              "text",
              undefined,
              true
            )}
          </div>
        </div>

        <Separator />

        {/* Contact Information */}
        {renderFormGroup(
          "Informasi Kontak",
          <>
            {renderField(
              "HP 1",
              "contact.mobilePhone1",
              "text",
              undefined,
              true
            )}
            {renderField("HP 2", "contact.mobilePhone2", "text")}
            {renderField("Telepon Rumah 1", "contact.homePhone1", "text")}
            {renderField("Telepon Rumah 2", "contact.homePhone2", "text")}
          </>
        )}

        <Separator />

        {/* Marital Status and Children */}
        {renderFormGroup(
          "Status Pernikahan dan Anak",
          <>
            {renderField(
              "Status Pernikahan",
              "maritalInfo.status",
              "select",
              maritalStatusOptions,
              true
            )}
            {renderField("Nama Pasangan", "maritalInfo.spouseName", "text")}
            {renderField("Pekerjaan Pasangan", "maritalInfo.spouseJob", "text")}
            {renderField(
              "Jumlah Anak",
              "maritalInfo.numberOfChildren",
              "number"
            )}
          </>
        )}

        <Separator />

        {/* Bank Account */}
        {renderFormGroup(
          "Rekening Bank",
          <>
            {renderField(
              "Nomor Rekening",
              "bankAccount.accountNumber",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Pemegang Rekening",
              "bankAccount.accountHolder",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Bank",
              "bankAccount.bankName",
              "text",
              undefined,
              true
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
