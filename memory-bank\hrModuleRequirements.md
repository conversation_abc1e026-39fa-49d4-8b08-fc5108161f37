# HR Module Requirements - Bebang Information System

## 🎯 **Module Overview**

**Module Name**: Human Resources Management  
**Type**: Standalone Module  
**Navigation**: Independent dengan back to welcome page  
**Styling**: Consistent design across all menus  

## 📋 **Module Structure**

### 1. **Master Data Management**

#### A. Department
- **Fields**: 
  - Nama Department
  - Manager
  - Keterangan
- **Operations**: CRUD operations

#### B. Posisi Jabatan
- **Fields**:
  - Nama Jabatan
  - Department (reference to master data)
  - Keterangan
- **Operations**: CRUD operations

#### C. Kategori <PERSON>
- **Fields**:
  - <PERSON>a <PERSON>
  - Keterangan
- **Operations**: CRUD operations

#### D. <PERSON>
- **Fields**:
  - Nama <PERSON>
  - Ke<PERSON>ngan
- **Operations**: CRUD operations

#### E. Sub Golongan
- **Fields**:
  - Nama Sub Golongan
  - Keterangan
- **Operations**: CRUD operations

#### F. <PERSON><PERSON>
- **Fields**:
  - Nama Hubungan Kerja
  - Keterangan
- **Operations**: CRUD operations

#### G. Tags
- **Fields**:
  - Tags
  - Keterangan
- **Operations**: CRUD operations

### 2. **Employee Profile Management**

#### **Header Section**
- Nama Lengkap
- Posisi Jabatan (from master data)
- Department
- Nomor Induk Karyawan (manual input)
- Tags (multi-select from master data)
- Nomor Handphone
- Email
- Photo Upload & View

#### **Body Section - 3 Categories**

##### **Category 1: Personal Information**

**Group: Employee Biodata**
- Employee Full Name
- Gender
- Place of Birth
- Date of Birth

**Group: Identification**
- Religion
- Blood Type
- Family Card Number (KK)
- Identity Card Number (KTP)
- Tax Number (NPWP)
- BPJS-TK Number
- No NIK KK
- Tax Status

**Group: Alamat Domisili**
- Street
- City
- Province

**Group: Alamat KTP**
- Street
- City
- Province

**Group: Contact Information**
- Mobile Phone 1
- Mobile Phone 2
- Home Phone 1
- Home Phone 2

**Group: Marital Status and Children**
- Marital Status
- Nama Pasangan
- Pekerjaan Pasangan
- Number of Children

**Group: Bank Account**
- Bank Account Number
- Bank Account Holder
- Bank Name

##### **Category 2: Informasi HR**

**Group: Kontrak**
- Jenis Hubungan (from master data)
- Tanggal Masuk
- Tanggal Kontrak
- Tanggal Akhir Kontrak

**Group: Education**
- Certificate Level
- Field of Study
- School Name
- City of the School
- Description

**Group: Pangkat dan Golongan**
- Job Position
- Kategori Pangkat (from master data)
- Golongan Pangkat (from master data)
- Sub Golongan Pangkat (from master data)
- No Dana Pensiun

**Group: Emergency**
- Contact Name
- Contact Phone
- Relationship
- Address

**Group: POO/POH**
- Point of Original (POO)
- Point of Hire (POH)

**Group: Seragam dan Sepatu Kerja**
- Ukuran Seragam Kerja
- Ukuran Sepatu Kerja

##### **Category 3: Informasi Keluarga**

**Group: Pasangan dan Anak**
- Nama Pasangan
- Tanggal Lahir Pasangan
- Pendidikan Terakhir Pasangan
- Pekerjaan Pasangan
- Jumlah Anak

**Group: Identitas Anak (Up to 4 Children)**
For each child (1-4):
- Nama Anak
- Jenis Kelamin Anak
- Tanggal Lahir Anak

**Group: Saudara Kandung**
- Anak Ke
- Jumlah Saudara Kandung

**Group: Identitas Saudara Kandung (Up to 4 Siblings)**
For each sibling (1-4):
- Nama Saudara Kandung
- Jenis Kelamin Saudara Kandung
- Tanggal Lahir Saudara Kandung
- Pendidikan Terakhir Saudara Kandung
- Pekerjaan Saudara Kandung
- Keterangan

**Group: Orang Tua Mertua**
- Nama Ayah Mertua
- Tanggal Lahir Ayah Mertua
- Pendidikan Terakhir Ayah Mertua
- Keterangan

- Nama Ibu Mertua
- Tanggal Lahir Ibu Mertua
- Pendidikan Terakhir Ibu Mertua
- Keterangan

## 🎨 **UI/UX Requirements**

### Navigation
- **Back to Welcome**: Clear navigation back to main dashboard
- **Module Independence**: No other module menus visible
- **Breadcrumb**: Clear navigation path

### Styling
- **Consistent Design**: Same styling across all HR menus
- **Professional Look**: Corporate-friendly interface
- **Responsive**: Works on desktop and mobile
- **Form Organization**: Clear grouping and categories

### Features
- **Photo Upload**: Employee photo upload and display
- **Multi-select**: Tags selection from master data
- **Form Validation**: Comprehensive input validation
- **Data Relationships**: Master data integration
- **CRUD Operations**: Full create, read, update, delete

## 🔧 **Technical Implementation**

### Backend
- **Models**: Employee, Department, Position, Rank, etc.
- **APIs**: RESTful endpoints for all operations
- **File Upload**: Photo upload service
- **Validation**: Server-side validation
- **Relationships**: Proper data relationships

### Frontend
- **Components**: Reusable form components
- **State Management**: Efficient state handling
- **File Upload**: Photo upload component
- **Form Handling**: Multi-step form management
- **Validation**: Client-side validation

### Database
- **Collections**: Separate collections for each master data
- **References**: Proper ObjectId references
- **Indexing**: Performance optimization
- **Validation**: Schema-level validation

## 🚀 **Implementation Priority**

1. **Phase 1**: Master Data Models & APIs
2. **Phase 2**: Employee Model & Basic CRUD
3. **Phase 3**: Photo Upload Service
4. **Phase 4**: Frontend Components & Forms
5. **Phase 5**: Integration & Testing
6. **Phase 6**: UI Polish & Optimization

## 📝 **Future Enhancements**

- Leave Management
- Performance Reviews
- Document Management
- Reporting & Analytics
- Approval Workflows
- Audit Trail Integration
