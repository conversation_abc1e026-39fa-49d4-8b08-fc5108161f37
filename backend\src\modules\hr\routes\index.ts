import { Router } from "express";
import divisionRoutes from "./divisionRoutes";
import departmentRoutes from "./departmentRoutes";
import positionRoutes from "./positionRoutes";
import employeeRoutes from "./employeeRoutes";
import employmentTypeRoutes from "./employmentTypeRoutes";
import rankCategoryRoutes from "./rankCategoryRoutes";
import rankGradeRoutes from "./rankGradeRoutes";
import rankSubgradeRoutes from "./rankSubgradeRoutes";
import tagRoutes from "./tagRoutes";

const router = Router();

// Mount routes
router.use("/divisions", divisionRoutes);
router.use("/departments", departmentRoutes);
router.use("/positions", positionRoutes);
router.use("/employees", employeeRoutes);
router.use("/employment-types", employmentTypeRoutes);
router.use("/rank-categories", rankCategoryRoutes);
router.use("/rank-grades", rankGradeRoutes);
router.use("/rank-subgrades", rankSubgradeRoutes);
router.use("/tags", tagRoutes);

export default router;
