import mongoose, { Schema, Document } from "mongoose";

export interface IPosition extends Document {
  name: string;
  department: mongoose.Types.ObjectId;
  description?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  deletedBy?: mongoose.Types.ObjectId;
}

const PositionSchema = new Schema<IPosition>(
  {
    name: {
      type: String,
      required: [true, "Position name is required"],
      trim: true,
      maxlength: [100, "Position name cannot exceed 100 characters"],
    },
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: [true, "Department is required"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes
PositionSchema.index({ name: 1, department: 1 }, { unique: true });
PositionSchema.index({ department: 1 });
PositionSchema.index({ isDeleted: 1 });
PositionSchema.index({ isActive: 1 });

// Pre-save middleware
PositionSchema.pre("save", function (next) {
  if (this.isDeleted && !this.deletedAt) {
    this.deletedAt = new Date();
  }
  next();
});

const Position = mongoose.model<IPosition>("Position", PositionSchema);

export default Position;
