"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { PhoneInput } from "@/components/ui/phone-input";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Camera, Upload, X } from "lucide-react";
import { toast } from "sonner";

interface EditEmployeeHeaderProps {
  data: {
    personal: {
      fullName: string;
      employeeId: string;
      phone: string;
      email?: string;
      profilePhoto?: string;
    };
    hr: {
      division: string;
      department: string;
      position: string;
      companyEmail?: string;
      manager?: string;
      directSupervisor?: string;
      tags?: string[];
      status?: string;
    };
  };
  onUpdate: (section: "personal" | "hr", field: string, value: any) => void;
  masterData: {
    divisions: Array<{
      _id?: string;
      id?: string;
      name: string;
      isActive?: boolean;
    }>;
    departments: Array<{
      _id?: string;
      id?: string;
      name: string;
      isActive?: boolean;
    }>;
    positions: Array<{
      _id?: string;
      id?: string;
      name: string;
      isActive?: boolean;
    }>;
    tags: Array<{
      _id?: string;
      id?: string;
      name: string;
      color: string;
      isActive?: boolean;
    }>;
    employees: Array<{
      _id?: string;
      id?: string;
      personal?: {
        fullName: string;
        employeeId: string;
      };
      isActive?: boolean;
    }>;
  };
  onTagToggle: (tagId: string) => void;
  onPhotoUpload: (file: File) => void;
}

export default function EditEmployeeHeader({
  data,
  onUpdate,
  masterData,
  onTagToggle,
  onPhotoUpload,
}: EditEmployeeHeaderProps) {
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Ukuran file terlalu besar", {
          description: "Maksimal ukuran file adalah 5MB",
        });
        return;
      }

      if (!file.type.startsWith("image/")) {
        toast.error("Format file tidak didukung", {
          description: "Hanya file gambar yang diperbolehkan",
        });
        return;
      }

      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handlePhotoUploadClick = async () => {
    if (!selectedFile) return;

    try {
      onPhotoUpload(selectedFile);
      setPhotoDialogOpen(false);
      setSelectedFile(null);
      setPreviewUrl("");
      toast.success("Foto profil berhasil diupload");
    } catch (error) {
      toast.error("Gagal mengupload foto profil");
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Debug log untuk melihat data foto
  console.log("EditEmployeeHeader - profilePhoto:", data.personal.profilePhoto);

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
        <CardTitle className="text-lg font-semibold">
          Informasi Utama Karyawan
        </CardTitle>
        <p className="text-blue-100 text-xs">Edit informasi dasar karyawan</p>
      </CardHeader>
      <CardContent className="space-y-4 p-4">
        <div className="flex items-start gap-6">
          {/* Profile Photo */}
          <div className="relative flex flex-col items-center">
            <div className="w-32 h-32 border-3 border-white shadow-lg rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
              {data.personal.profilePhoto &&
              data.personal.profilePhoto.trim() !== "" ? (
                <img
                  src={data.personal.profilePhoto}
                  alt={data.personal.fullName}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    console.log(
                      "Image failed to load:",
                      data.personal.profilePhoto
                    );
                    // Hide the image and show initials instead
                    e.currentTarget.style.display = "none";
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      parent.innerHTML = `<span class="text-xl font-semibold text-white">${getInitials(
                        data.personal.fullName || "NN"
                      )}</span>`;
                    }
                  }}
                  onLoad={() => {
                    console.log(
                      "Image loaded successfully:",
                      data.personal.profilePhoto
                    );
                  }}
                />
              ) : (
                <span className="text-xl font-semibold text-white">
                  {getInitials(data.personal.fullName || "NN")}
                </span>
              )}
            </div>

            <Dialog open={photoDialogOpen} onOpenChange={setPhotoDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  className="absolute -bottom-1 -right-1 rounded-full w-8 h-8 p-0 shadow-lg"
                >
                  <Camera className="w-4 h-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload Foto Profil</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="flex flex-col items-center gap-4">
                    {previewUrl && (
                      <div className="relative">
                        <img
                          src={previewUrl}
                          alt="Preview"
                          className="w-40 h-40 rounded-lg object-cover border-2 border-gray-200"
                        />
                        <Button
                          size="sm"
                          variant="destructive"
                          className="absolute -top-2 -right-2 rounded-full w-6 h-6 p-0"
                          onClick={() => {
                            setPreviewUrl("");
                            setSelectedFile(null);
                          }}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    )}

                    <div className="flex flex-col items-center gap-2">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="photo-upload"
                      />
                      <Label
                        htmlFor="photo-upload"
                        className="cursor-pointer flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      >
                        <Upload className="w-4 h-4" />
                        Pilih Foto
                      </Label>
                      <p className="text-sm text-gray-500">
                        Maksimal 5MB, format JPG/PNG
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2 justify-end">
                    <Button
                      variant="outline"
                      onClick={() => setPhotoDialogOpen(false)}
                    >
                      Batal
                    </Button>
                    <Button
                      onClick={handlePhotoUploadClick}
                      disabled={!selectedFile}
                    >
                      Upload
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Employee Information */}
          <div className="flex-1 space-y-4">
            {/* Row 1: Full Name (spans full width) */}
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="fullName"
                  className="text-xs font-medium text-gray-700"
                >
                  Nama Lengkap *
                </Label>
                <Input
                  id="fullName"
                  value={data.personal.fullName}
                  onChange={(e) =>
                    onUpdate("personal", "fullName", e.target.value)
                  }
                  placeholder="Masukkan nama lengkap"
                  className="h-9"
                />
              </div>
            </div>

            {/* Row 2: Employee ID and Position */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="employeeId"
                  className="text-xs font-medium text-gray-700"
                >
                  Nomor Induk Karyawan *
                </Label>
                <Input
                  id="employeeId"
                  value={data.personal.employeeId}
                  onChange={(e) =>
                    onUpdate("personal", "employeeId", e.target.value)
                  }
                  placeholder="Masukkan nomor induk karyawan"
                  className="h-9"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="position"
                  className="text-xs font-medium text-gray-700"
                >
                  Posisi Jabatan *
                </Label>
                <SearchableSelect
                  value={data.hr.position}
                  onValueChange={(value) => onUpdate("hr", "position", value)}
                  placeholder="Pilih posisi jabatan"
                  searchPlaceholder="Cari posisi jabatan..."
                  emptyMessage="Tidak ada posisi jabatan ditemukan."
                  options={masterData.positions
                    .filter(
                      (position) =>
                        (position.id || position._id) &&
                        position.name &&
                        position.isActive !== false
                    )
                    .map((position) => ({
                      value: position.id || position._id,
                      label: position.name,
                    }))}
                />
              </div>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 my-2"></div>

            {/* Row 3: Division and Department */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="division"
                  className="text-xs font-medium text-gray-700"
                >
                  Divisi *
                </Label>
                <SearchableSelect
                  value={data.hr.division}
                  onValueChange={(value) => {
                    onUpdate("hr", "division", value);
                    // Reset department when division changes
                    onUpdate("hr", "department", "");
                  }}
                  placeholder="Pilih divisi"
                  searchPlaceholder="Cari divisi..."
                  emptyMessage="Tidak ada divisi ditemukan."
                  options={masterData.divisions
                    .filter(
                      (div) =>
                        (div.id || div._id) && div.name && div.isActive === true
                    )
                    .map((div) => ({
                      value: div.id || div._id,
                      label: div.name,
                    }))}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="department"
                  className="text-xs font-medium text-gray-700"
                >
                  Departemen *
                </Label>
                <SearchableSelect
                  value={data.hr.department}
                  onValueChange={(value) => onUpdate("hr", "department", value)}
                  placeholder="Pilih departemen"
                  searchPlaceholder="Cari departemen..."
                  emptyMessage="Tidak ada departemen ditemukan."
                  options={masterData.departments
                    .filter((dept) => {
                      return (
                        (dept.id || dept._id) &&
                        dept.name &&
                        dept.isActive === true
                      );
                    })
                    .map((dept) => ({
                      value: dept.id || dept._id,
                      label: dept.name,
                    }))}
                />
              </div>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 my-2"></div>

            {/* Row 4: Email and Phone */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="companyEmail"
                  className="text-xs font-medium text-gray-700"
                >
                  Email Perusahaan
                </Label>
                <Input
                  id="companyEmail"
                  type="email"
                  value={data.hr.companyEmail || ""}
                  onChange={(e) =>
                    onUpdate("hr", "companyEmail", e.target.value)
                  }
                  placeholder="Masukkan email perusahaan"
                  className="h-9"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="phone"
                  className="text-xs font-medium text-gray-700"
                >
                  Nomor Handphone * (Sinkron dengan HP 1)
                </Label>
                <PhoneInput
                  id="phone"
                  value={data.personal.phone || ""}
                  onChange={(value) => {
                    onUpdate("personal", "phone", value);
                  }}
                  placeholder="Masukkan nomor handphone"
                />
              </div>
            </div>

            {/* Row 5: Manager and Direct Supervisor */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="manager"
                  className="text-xs font-medium text-gray-700"
                >
                  Manager
                </Label>
                <SearchableSelect
                  value={data.hr.manager || ""}
                  onValueChange={(value) => onUpdate("hr", "manager", value)}
                  placeholder="Pilih manager"
                  searchPlaceholder="Cari manager..."
                  emptyMessage="Tidak ada manager ditemukan."
                  options={masterData.employees
                    .filter(
                      (emp) =>
                        (emp.id || emp._id) &&
                        emp.personal?.fullName &&
                        emp.isActive !== false &&
                        (emp.id || emp._id) !== data.personal.employeeId
                    )
                    .map((emp) => ({
                      value: emp.id || emp._id,
                      label: `${emp.personal?.fullName} (${emp.personal?.employeeId})`,
                    }))}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="directSupervisor"
                  className="text-xs font-medium text-gray-700"
                >
                  Atasan Langsung
                </Label>
                <SearchableSelect
                  value={data.hr.directSupervisor || ""}
                  onValueChange={(value) =>
                    onUpdate("hr", "directSupervisor", value)
                  }
                  placeholder="Pilih atasan langsung"
                  searchPlaceholder="Cari atasan langsung..."
                  emptyMessage="Tidak ada atasan langsung ditemukan."
                  options={masterData.employees
                    .filter(
                      (emp) =>
                        (emp.id || emp._id) &&
                        emp.personal?.fullName &&
                        emp.isActive !== false &&
                        (emp.id || emp._id) !== data.personal.employeeId
                    )
                    .map((emp) => ({
                      value: emp.id || emp._id,
                      label: `${emp.personal?.fullName} (${emp.personal?.employeeId})`,
                    }))}
                />
              </div>
            </div>

            {/* Row 6: Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="status"
                  className="text-xs font-medium text-gray-700"
                >
                  Status Karyawan *
                </Label>
                <Select
                  value={data.hr.status || "Aktif"}
                  onValueChange={(value) => onUpdate("hr", "status", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Pilih status karyawan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem key="Aktif" value="Aktif">
                      🟢 Aktif
                    </SelectItem>
                    <SelectItem key="Probation" value="Probation">
                      🟡 Probation
                    </SelectItem>
                    <SelectItem key="Cuti" value="Cuti">
                      🔵 Cuti
                    </SelectItem>
                    <SelectItem key="Notice Period" value="Notice Period">
                      🟠 Notice Period
                    </SelectItem>
                    <SelectItem key="Tidak Aktif" value="Tidak Aktif">
                      ⚫ Tidak Aktif
                    </SelectItem>
                    <SelectItem key="Resign" value="Resign">
                      🔴 Resign
                    </SelectItem>
                    <SelectItem key="Terminated" value="Terminated">
                      ❌ Terminated
                    </SelectItem>
                    <SelectItem key="Pension" value="Pension">
                      🏆 Pension
                    </SelectItem>
                    <SelectItem key="Kontrak Habis" value="Kontrak Habis">
                      📋 Kontrak Habis
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 my-2"></div>

            {/* Row 7: Tags (full width) */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-gray-700">Tags</Label>
              <div className="flex flex-wrap gap-2">
                {masterData.tags
                  .filter(
                    (tag) =>
                      (tag.id || tag._id) &&
                      tag.name &&
                      tag.color &&
                      tag.isActive === true
                  )
                  .map((tag) => {
                    const tagId = tag.id || tag._id;
                    return (
                      <Badge
                        key={tagId}
                        variant={
                          (data.hr.tags || []).includes(tagId)
                            ? "default"
                            : "outline"
                        }
                        className="cursor-pointer px-2 py-1 text-xs font-medium transition-all duration-200 hover:scale-105"
                        style={{
                          backgroundColor: (data.hr.tags || []).includes(tagId)
                            ? tag.color
                            : "transparent",
                          borderColor: tag.color,
                          color: (data.hr.tags || []).includes(tagId)
                            ? "white"
                            : tag.color,
                        }}
                        onClick={() => onTagToggle(tagId)}
                      >
                        {tag.name}
                      </Badge>
                    );
                  })}
              </div>
              {masterData.tags.length > 0 &&
                (data.hr.tags || []).length === 0 && (
                  <p className="text-xs text-gray-500 italic">
                    Pilih tags yang sesuai untuk karyawan ini
                  </p>
                )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
