import { toast } from "sonner";

// Base API URL
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

// Master Data Interfaces
export interface Division {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  name: string;
  description?: string;
  isActive: boolean;
}

export interface Department {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  name: string;
  manager?: string;
  description?: string;
  isActive: boolean;
}

export interface Position {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field from backend transform
  name: string;
  department: {
    _id?: string; // For backward compatibility
    id: string; // Primary ID field from backend transform
    name: string;
  };
  description?: string;
  isActive: boolean;
}

export interface EmploymentType {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  name: string;
  description?: string;
  isActive: boolean;
}

export interface RankCategory {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  name: string;
  description?: string;
  isActive: boolean;
}

export interface RankGrade {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  name: string;
  description?: string;
  isActive: boolean;
}

export interface RankSubgrade {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  name: string;
  description?: string;
  isActive: boolean;
}

export interface Tag {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field from backend transform
  name: string;
  color: string;
  description?: string;
  isActive: boolean;
}

export interface EmployeeBasic {
  _id?: string; // For backward compatibility
  id: string; // Primary ID field
  fullName: string;
  employeeId: string;
  position?: string;
  department?: string;
  isActive?: boolean;
}

// API Response interface
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("accessToken");
  }
  return null;
};

// Helper function to set mock token for development
const setMockAuthToken = (): void => {
  if (typeof window !== "undefined") {
    // Set a mock token for development/testing
    localStorage.setItem("accessToken", "mock-token-for-development");
    console.log("Mock auth token set for development");
  }
};

// Helper function to make authenticated requests
const makeRequest = async (url: string, options: RequestInit = {}) => {
  const token = getAuthToken();

  const headers = {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// Master Data Service Functions
export const masterDataService = {
  // Get all divisions
  async getDivisions(): Promise<ApiResponse<Division[]>> {
    try {
      return await makeRequest("/hr/divisions");
    } catch (error) {
      // Silent fail for auth errors - handled in getAllMasterData
      throw error;
    }
  },

  // Get all departments
  async getDepartments(): Promise<ApiResponse<Department[]>> {
    try {
      return await makeRequest("/hr/departments");
    } catch (error) {
      // Silent fail for auth errors - handled in getAllMasterData
      throw error;
    }
  },

  // Get all positions
  async getPositions(): Promise<ApiResponse<Position[]>> {
    try {
      return await makeRequest("/hr/positions");
    } catch (error) {
      // Silent fail for auth errors - handled in getAllMasterData
      throw error;
    }
  },

  // Get all employment types
  async getEmploymentTypes(): Promise<ApiResponse<EmploymentType[]>> {
    try {
      return await makeRequest("/hr/employment-types");
    } catch (error) {
      // Silent fail for auth errors - handled in getAllMasterData
      throw error;
    }
  },

  // Get all rank categories
  async getRankCategories(): Promise<ApiResponse<RankCategory[]>> {
    try {
      return await makeRequest("/hr/rank-categories");
    } catch (error) {
      // Silently fail for missing routes - handled in getAllMasterData
      throw error;
    }
  },

  // Get all rank grades
  async getRankGrades(): Promise<ApiResponse<RankGrade[]>> {
    try {
      return await makeRequest("/hr/rank-grades");
    } catch (error) {
      // Silently fail for missing routes - handled in getAllMasterData
      throw error;
    }
  },

  // Get all rank subgrades
  async getRankSubgrades(): Promise<ApiResponse<RankSubgrade[]>> {
    try {
      return await makeRequest("/hr/rank-subgrades");
    } catch (error) {
      // Silently fail for missing routes - handled in getAllMasterData
      throw error;
    }
  },

  // Get all tags
  async getTags(): Promise<ApiResponse<Tag[]>> {
    try {
      return await makeRequest("/hr/tags");
    } catch (error) {
      // Silently fail for missing routes - handled in getAllMasterData
      throw error;
    }
  },

  // Get all employees (basic info for manager/supervisor selection)
  async getEmployees(): Promise<ApiResponse<EmployeeBasic[]>> {
    try {
      const response = await makeRequest("/hr/employees?limit=1000"); // Get all employees

      // Transform the response data to match EmployeeBasic interface
      const transformedData = response.data.map((employee: any) => ({
        _id: employee._id,
        id: employee._id,
        fullName: employee.personal?.fullName || "",
        employeeId: employee.personal?.employeeId || "",
        position: employee.hr?.position?.name || "",
        department: employee.hr?.department?.name || "",
        isActive: employee.isActive !== false,
      }));

      return {
        ...response,
        data: transformedData,
      };
    } catch (error) {
      // Silently fail for missing routes - handled in getAllMasterData
      throw error;
    }
  },

  // Get all master data at once from API
  async getAllMasterData(): Promise<{
    divisions: Division[];
    departments: Department[];
    positions: Position[];
    employmentTypes: EmploymentType[];
    rankCategories: RankCategory[];
    rankGrades: RankGrade[];
    rankSubgrades: RankSubgrade[];
    tags: Tag[];
    employees: EmployeeBasic[];
  }> {
    try {
      // Set mock token if not exists (for development)
      if (
        typeof window !== "undefined" &&
        !localStorage.getItem("accessToken")
      ) {
        localStorage.setItem("accessToken", "mock-token-for-development");
        console.log("Mock auth token set for development");
      }

      console.log("Fetching all master data from API...");

      // Fetch all master data from API
      const [
        divisionsRes,
        departmentsRes,
        positionsRes,
        employmentTypesRes,
        rankCategoriesRes,
        rankGradesRes,
        rankSubgradesRes,
        tagsRes,
        employeesRes,
      ] = await Promise.all([
        this.getDivisions(),
        this.getDepartments(),
        this.getPositions(),
        this.getEmploymentTypes(),
        this.getRankCategories(),
        this.getRankGrades(),
        this.getRankSubgrades(),
        this.getTags(),
        this.getEmployees(),
      ]);

      console.log("API responses received:", {
        divisions: divisionsRes.data.length,
        departments: departmentsRes.data.length,
        positions: positionsRes.data.length,
        employmentTypes: employmentTypesRes.data.length,
        rankCategories: rankCategoriesRes.data.length,
        rankGrades: rankGradesRes.data.length,
        rankSubgrades: rankSubgradesRes.data.length,
        tags: tagsRes.data.length,
        employees: employeesRes.data.length,
      });

      return {
        divisions: divisionsRes.data,
        departments: departmentsRes.data,
        positions: positionsRes.data,
        employmentTypes: employmentTypesRes.data,
        rankCategories: rankCategoriesRes.data,
        rankGrades: rankGradesRes.data,
        rankSubgrades: rankSubgradesRes.data,
        tags: tagsRes.data,
        employees: employeesRes.data,
      };
    } catch (error) {
      console.error("Error fetching master data from API:", error);
      throw new Error(
        "Failed to load master data. Please check if all master data APIs are available."
      );
    }
  },
};
