import mongoose from "mongoose";
import { connectDatabase } from "../src/config/database";

// Import models
import { Department } from "../src/modules/hr/models/Department";
import Position from "../src/modules/hr/models/Position";
import RankCategory from "../src/modules/hr/models/RankCategory";
import RankGrade from "../src/modules/hr/models/RankGrade";
import RankSubgrade from "../src/modules/hr/models/RankSubgrade";
import EmploymentType from "../src/modules/hr/models/EmploymentType";
import Tag from "../src/modules/hr/models/Tag";

async function cleanHRData() {
  try {
    console.log("🧹 Starting HR data cleanup...");
    console.log("📍 Environment:", process.env.NODE_ENV || "development");

    // Connect to database
    await connectDatabase();

    console.log("\n🗑️ Cleaning HR master data...");

    // Clean all HR master data collections
    const departmentResult = await Department.deleteMany({});
    console.log(`✅ Deleted ${departmentResult.deletedCount} departments`);

    const positionResult = await Position.deleteMany({});
    console.log(`✅ Deleted ${positionResult.deletedCount} positions`);

    const rankCategoryResult = await RankCategory.deleteMany({});
    console.log(
      `✅ Deleted ${rankCategoryResult.deletedCount} rank categories`
    );

    const rankGradeResult = await RankGrade.deleteMany({});
    console.log(`✅ Deleted ${rankGradeResult.deletedCount} rank grades`);

    const rankSubgradeResult = await RankSubgrade.deleteMany({});
    console.log(`✅ Deleted ${rankSubgradeResult.deletedCount} rank subgrades`);

    const employmentTypeResult = await EmploymentType.deleteMany({});
    console.log(
      `✅ Deleted ${employmentTypeResult.deletedCount} employment types`
    );

    const tagResult = await Tag.deleteMany({});
    console.log(`✅ Deleted ${tagResult.deletedCount} tags`);

    console.log("\n🗑️ Cleaning employee data...");

    // Clean employees collection directly
    const employeeResult = await mongoose.connection.db
      .collection("employees")
      .deleteMany({});
    console.log(`✅ Deleted ${employeeResult.deletedCount} employees`);

    console.log("\n📊 Cleanup Summary:");
    console.log("┌─────────────────────┬───────────────┐");
    console.log("│ Collection          │ Records Deleted │");
    console.log("├─────────────────────┼───────────────┤");
    console.log(
      `│ Departments         │ ${departmentResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Positions           │ ${positionResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Rank Categories     │ ${rankCategoryResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Rank Grades         │ ${rankGradeResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Rank Subgrades      │ ${rankSubgradeResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Employment Types    │ ${employmentTypeResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Tags                │ ${tagResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log(
      `│ Employees           │ ${employeeResult.deletedCount
        .toString()
        .padStart(13)} │`
    );
    console.log("└─────────────────────┴───────────────┘");

    const totalDeleted =
      departmentResult.deletedCount +
      positionResult.deletedCount +
      rankCategoryResult.deletedCount +
      rankGradeResult.deletedCount +
      rankSubgradeResult.deletedCount +
      employmentTypeResult.deletedCount +
      tagResult.deletedCount +
      employeeResult.deletedCount;

    console.log(`\n🎯 Total records deleted: ${totalDeleted}`);

    // Verify credentials are still intact
    console.log("\n🔍 Verifying credentials...");

    const User = mongoose.model("User");
    const Role = mongoose.model("Role");

    const userCount = await User.countDocuments();
    const roleCount = await Role.countDocuments();

    console.log(`✅ Users preserved: ${userCount}`);
    console.log(`✅ Roles preserved: ${roleCount}`);

    if (userCount === 0 || roleCount === 0) {
      console.log("⚠️ WARNING: Some credentials may have been affected!");
      console.log("   Please run: npm run seed to restore credentials");
    } else {
      console.log("✅ All credentials are safe and preserved");
    }

    console.log("\n🎉 HR data cleanup completed successfully!");
    console.log("📝 You can now input real data through the application");
    console.log("🔐 All user credentials and roles are preserved");
  } catch (error) {
    console.error("❌ Error cleaning HR data:", error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log("\n🔌 Disconnected from MongoDB");
    process.exit(0);
  }
}

// Run cleanup if this file is executed directly
if (require.main === module) {
  cleanHRData();
}

export default cleanHRData;
