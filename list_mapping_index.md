# Employee Bulk Import System - Field Mapping Document

## Overview
This document provides a comprehensive mapping between Excel template columns and Employee database fields for the Employee Bulk Import System (Phase 3B). The mapping is organized by column index position and includes all fields from the Employee model.

## Field Mapping Structure
Format: `Column Letter (Index) - database_field_name`

---

## HEADER/MAIN INFORMATION FIELDS

### A (1) - personal.employeeId
**Description**: Employee ID/NIK (Nomor Induk <PERSON>wan)  
**Type**: String (Required, Unique, Uppercase)  
**Example**: EMP001, HR001, ADM001

### B (2) - personal.fullName
**Description**: Full Name (<PERSON>a <PERSON>)  
**Type**: String (Required, Max 100 chars)  
**Example**: Ahmad <PERSON>

### C (3) - hr.division
**Description**: Division (Divisi)  
**Type**: ObjectId Reference to Division collection  
**Example**: Division Name (will be mapped to ObjectId)

### D (4) - hr.department
**Description**: Department (Departemen)  
**Type**: ObjectId Reference to Department collection  
**Example**: Department Name (will be mapped to ObjectId)

### E (5) - hr.position
**Description**: Position (Jabatan)  
**Type**: ObjectId Reference to Position collection  
**Example**: Position Name (will be mapped to ObjectId)

### F (6) - hr.companyEmail
**Description**: Company Email (Email Perusahaan)  
**Type**: String (Optional, Unique, Lowercase)  
**Example**: <EMAIL>

### G (7) - personal.phone
**Description**: Phone Number (Nomor Telepon)  
**Type**: String (Required)  
**Example**: +62812345678

### H (8) - hr.manager
**Description**: Manager (Manager)  
**Type**: ObjectId Reference to Employee collection  
**Example**: Manager Employee ID (will be mapped to ObjectId)

### I (9) - hr.directSupervisor
**Description**: Direct Supervisor (Atasan Langsung)  
**Type**: ObjectId Reference to Employee collection  
**Example**: Supervisor Employee ID (will be mapped to ObjectId)

### J (10) - status
**Description**: Employee Status (Status Karyawan)  
**Type**: Enum String (Required)  
**Options**: Aktif, Probation, Cuti, Tidak Aktif, Notice Period, Terminated, Resigned, Retired, Suspended  
**Example**: Aktif

### K (11) - hr.tags
**Description**: Tags (Tag/Label)  
**Type**: Array of ObjectId References to Tag collection  
**Example**: Tag1,Tag2,Tag3 (comma-separated, will be mapped to ObjectIds)

---

## PERSONAL INFORMATION FIELDS

### L (12) - personal.gender
**Description**: Gender (Jenis Kelamin)  
**Type**: Enum String (Required)  
**Options**: Laki-laki, Perempuan  
**Example**: Laki-laki

### M (13) - personal.placeOfBirth
**Description**: Place of Birth (Tempat Lahir)  
**Type**: String (Required)  
**Example**: Jakarta

### N (14) - personal.dateOfBirth
**Description**: Date of Birth (Tanggal Lahir)  
**Type**: Date (Required)  
**Format**: YYYY-MM-DD or DD/MM/YYYY  
**Example**: 1990-05-15

### O (15) - personal.email
**Description**: Personal Email (Email Pribadi)  
**Type**: String (Optional, Unique, Lowercase)  
**Example**: <EMAIL>

### P (16) - personal.religion
**Description**: Religion (Agama)  
**Type**: Enum String (Required)  
**Options**: Islam, Kristen, Katolik, Hindu, Buddha, Konghucu, Lainnya  
**Example**: Islam

### Q (17) - personal.bloodType
**Description**: Blood Type (Golongan Darah)  
**Type**: Enum String (Required)  
**Options**: A, B, AB, O  
**Example**: A

### R (18) - personal.familyCardNumber
**Description**: Family Card Number (Nomor KK)  
**Type**: String (Required)  
**Example**: 320**********123

### S (19) - personal.idCardNumber
**Description**: ID Card Number (Nomor KTP)  
**Type**: String (Required)  
**Example**: 320**********123

### T (20) - personal.taxNumber
**Description**: Tax Number (NPWP)  
**Type**: String (Optional)  
**Example**: 12.345.678.9-012.000

### U (21) - personal.bpjsTkNumber
**Description**: BPJS TK Number (Nomor BPJS TK)  
**Type**: String (Optional)  
**Example**: **********1

### V (22) - personal.nikKkNumber
**Description**: NIK KK Number (NIK di KK)  
**Type**: String (Optional)  
**Example**: 320**********123

### W (23) - personal.taxStatus
**Description**: Tax Status (Status Pajak)  
**Type**: Enum String (Required)  
**Options**: TK/0, TK/1, TK/2, TK/3, K/0, K/1, K/2, K/3  
**Example**: TK/0

---

## ADDRESS INFORMATION FIELDS

### X (24) - personal.currentAddress.street
**Description**: Current Address - Street (Alamat Domisili - Jalan)  
**Type**: String (Optional)  
**Example**: Jl. Merdeka No. 123

### Y (25) - personal.currentAddress.city
**Description**: Current Address - City (Alamat Domisili - Kota)  
**Type**: String (Optional)  
**Example**: Jakarta Pusat

### Z (26) - personal.currentAddress.province
**Description**: Current Address - Province (Alamat Domisili - Provinsi)  
**Type**: String (Optional)  
**Example**: DKI Jakarta

### AA (27) - personal.idCardAddress.street
**Description**: ID Card Address - Street (Alamat KTP - Jalan)  
**Type**: String (Optional)  
**Example**: Jl. Sudirman No. 456

### AB (28) - personal.idCardAddress.city
**Description**: ID Card Address - City (Alamat KTP - Kota)  
**Type**: String (Optional)  
**Example**: Jakarta Selatan

### AC (29) - personal.idCardAddress.province
**Description**: ID Card Address - Province (Alamat KTP - Provinsi)  
**Type**: String (Optional)  
**Example**: DKI Jakarta

---

## CONTACT INFORMATION FIELDS

### AD (30) - personal.contact.mobilePhone1
**Description**: Mobile Phone 1 (HP 1)  
**Type**: String (Optional)  
**Example**: +62812345678

### AE (31) - personal.contact.mobilePhone2
**Description**: Mobile Phone 2 (HP 2)  
**Type**: String (Optional)  
**Example**: +62812345679

### AF (32) - personal.contact.homePhone1
**Description**: Home Phone 1 (Telepon Rumah 1)  
**Type**: String (Optional)  
**Example**: 021-1234567

### AG (33) - personal.contact.homePhone2
**Description**: Home Phone 2 (Telepon Rumah 2)  
**Type**: String (Optional)  
**Example**: 021-1234568

---

## MARITAL STATUS FIELDS

### AH (34) - personal.maritalInfo.status
**Description**: Marital Status (Status Pernikahan)  
**Type**: Enum String (Required)  
**Options**: Belum Menikah, Menikah, Cerai, Janda/Duda  
**Example**: Menikah

### AI (35) - personal.maritalInfo.spouseName
**Description**: Spouse Name (Nama Pasangan)  
**Type**: String (Optional)  
**Example**: Siti Nurhaliza

### AJ (36) - personal.maritalInfo.spouseJob
**Description**: Spouse Job (Pekerjaan Pasangan)  
**Type**: String (Optional)  
**Example**: Guru

### AK (37) - personal.maritalInfo.numberOfChildren
**Description**: Number of Children (Jumlah Anak)  
**Type**: Number (Default: 0, Min: 0)  
**Example**: 2

---

## BANK ACCOUNT FIELDS

### AL (38) - personal.bankAccount.accountNumber
**Description**: Bank Account Number (Nomor Rekening)  
**Type**: String (Required)  
**Example**: **********

### AM (39) - personal.bankAccount.accountHolder
**Description**: Account Holder Name (Nama Pemegang Rekening)  
**Type**: String (Required)  
**Example**: Ahmad Budi Santoso

### AN (40) - personal.bankAccount.bankName
**Description**: Bank Name (Nama Bank)  
**Type**: String (Required)  
**Example**: Bank Mandiri

---

## HR CONTRACT INFORMATION FIELDS

### AO (41) - hr.contract.employmentType
**Description**: Employment Type (Jenis Kontrak)  
**Type**: ObjectId Reference to EmploymentType collection  
**Example**: Employment Type Name (will be mapped to ObjectId)

### AP (42) - hr.contract.hireDate
**Description**: Hire Date (Tanggal Masuk)  
**Type**: Date (Required)  
**Format**: YYYY-MM-DD or DD/MM/YYYY  
**Example**: 2023-01-15

### AQ (43) - hr.contract.contractDate
**Description**: Contract Date (Tanggal Kontrak)  
**Type**: Date (Optional)  
**Format**: YYYY-MM-DD or DD/MM/YYYY  
**Example**: 2023-01-15

### AR (44) - hr.contract.contractEndDate
**Description**: Contract End Date (Tanggal Berakhir Kontrak)  
**Type**: Date (Optional)  
**Format**: YYYY-MM-DD or DD/MM/YYYY  
**Example**: 2024-01-15

### AS (45) - hr.contract.permanentDate
**Description**: Permanent Date (Tanggal Tetap)  
**Type**: Date (Optional)  
**Format**: YYYY-MM-DD or DD/MM/YYYY  
**Example**: 2023-07-15

---

## EDUCATION FIELDS

### AT (46) - hr.education.certificateLevel
**Description**: Certificate Level (Tingkat Pendidikan)  
**Type**: Enum String (Optional)  
**Options**: SD, SMP, SMA, SMK, D1, D2, D3, S1, S2, S3  
**Example**: S1

### AU (47) - hr.education.fieldOfStudy
**Description**: Field of Study (Bidang Studi)  
**Type**: String (Optional)  
**Example**: Teknik Informatika

### AV (48) - hr.education.schoolName
**Description**: School Name (Nama Sekolah/Universitas)  
**Type**: String (Optional)  
**Example**: Universitas Indonesia

### AW (49) - hr.education.schoolCity
**Description**: School City (Kota Sekolah/Universitas)  
**Type**: String (Optional)  
**Example**: Jakarta

### AX (50) - hr.education.graduationStatus
**Description**: Graduation Status (Status Kelulusan)  
**Type**: Enum String (Optional)  
**Options**: Lulus, Tidak Lulus, Sedang Belajar  
**Example**: Lulus

### AY (51) - hr.education.description
**Description**: Education Description (Deskripsi Pendidikan)  
**Type**: String (Optional)  
**Example**: Cumlaude dengan IPK 3.8

---

## RANK AND GRADE FIELDS

### AZ (52) - hr.rank.rankCategory
**Description**: Rank Category (Kategori Pangkat)  
**Type**: ObjectId Reference to RankCategory collection  
**Example**: Rank Category Name (will be mapped to ObjectId)

### BA (53) - hr.rank.rankGrade
**Description**: Rank Grade (Tingkat Pangkat)  
**Type**: ObjectId Reference to RankGrade collection  
**Example**: Rank Grade Name (will be mapped to ObjectId)

### BB (54) - hr.rank.rankSubgrade
**Description**: Rank Subgrade (Sub Tingkat Pangkat)  
**Type**: ObjectId Reference to RankSubgrade collection  
**Example**: Rank Subgrade Name (will be mapped to ObjectId)

### BC (55) - hr.rank.pensionFundNumber
**Description**: Pension Fund Number (Nomor Dana Pensiun)  
**Type**: String (Optional)  
**Example**: DP123456789

---

## EMERGENCY CONTACT FIELDS

### BD (56) - hr.emergency.contactName
**Description**: Emergency Contact Name (Nama Kontak Darurat)  
**Type**: String (Required)  
**Example**: Siti Nurhaliza

### BE (57) - hr.emergency.contactPhone
**Description**: Emergency Contact Phone (Telepon Kontak Darurat)  
**Type**: String (Required)  
**Example**: +62812345678

### BF (58) - hr.emergency.contactPhone2
**Description**: Emergency Contact Phone 2 (Telepon Kontak Darurat 2)  
**Type**: String (Optional)  
**Example**: +62812345679

### BG (59) - hr.emergency.relationship
**Description**: Emergency Contact Relationship (Hubungan Kontak Darurat)  
**Type**: String (Required)  
**Example**: Istri

### BH (60) - hr.emergency.address
**Description**: Emergency Contact Address (Alamat Kontak Darurat)  
**Type**: String (Required)  
**Example**: Jl. Merdeka No. 123, Jakarta

---

## LOCATION FIELDS (POO/POH)

### BI (61) - hr.location.pointOfOrigin
**Description**: Point of Origin (POO - Tempat Asal)  
**Type**: String (Required)  
**Example**: Jakarta

### BJ (62) - hr.location.pointOfHire
**Description**: Point of Hire (POH - Tempat Kerja)  
**Type**: String (Required)  
**Example**: Taliabu

---

## UNIFORM AND WORK EQUIPMENT FIELDS

### BK (63) - hr.uniform.workUniformSize
**Description**: Work Uniform Size (Ukuran Seragam Kerja)  
**Type**: Enum String (Required)  
**Options**: XS, S, M, L, XL, XXL, XXXL  
**Example**: L

### BL (64) - hr.uniform.workShoesSize
**Description**: Work Shoes Size (Ukuran Sepatu Kerja)  
**Type**: String (Required)  
**Example**: 42

---

## SALARY FIELDS

### BM (65) - hr.salary.basic
**Description**: Basic Salary (Gaji Pokok)  
**Type**: Number (Optional)  
**Example**: 5000000

### BN (66) - hr.salary.allowances.transport
**Description**: Transport Allowance (Tunjangan Transport)  
**Type**: Number (Default: 0)  
**Example**: 500000

### BO (67) - hr.salary.allowances.meal
**Description**: Meal Allowance (Tunjangan Makan)  
**Type**: Number (Default: 0)  
**Example**: 300000

### BP (68) - hr.salary.allowances.communication
**Description**: Communication Allowance (Tunjangan Komunikasi)  
**Type**: Number (Default: 0)  
**Example**: 200000

### BQ (69) - hr.salary.allowances.position
**Description**: Position Allowance (Tunjangan Jabatan)  
**Type**: Number (Default: 0)  
**Example**: 1000000

### BR (70) - hr.salary.allowances.other
**Description**: Other Allowance (Tunjangan Lainnya)  
**Type**: Number (Default: 0)  
**Example**: 100000

### BS (71) - hr.workSchedule
**Description**: Work Schedule (Jadwal Kerja)
**Type**: Enum String (Default: Regular)
**Options**: Regular, Shift, Flexible, Remote, Part Time
**Example**: Regular

---

## FAMILY INFORMATION - SPOUSE FIELDS

### BT (72) - family.spouse.name
**Description**: Spouse Name (Nama Pasangan)
**Type**: String (Optional)
**Example**: Siti Nurhaliza

### BU (73) - family.spouse.dateOfBirth
**Description**: Spouse Date of Birth (Tanggal Lahir Pasangan)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1992-03-20

### BV (74) - family.spouse.marriageDate
**Description**: Marriage Date (Tanggal Menikah)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2015-06-15

### BW (75) - family.spouse.lastEducation
**Description**: Spouse Last Education (Pendidikan Terakhir Pasangan)
**Type**: String (Optional)
**Example**: S1 Pendidikan

### BX (76) - family.spouse.occupation
**Description**: Spouse Occupation (Pekerjaan Pasangan)
**Type**: String (Optional)
**Example**: Guru

### BY (77) - family.spouse.numberOfChildren
**Description**: Number of Children (Jumlah Anak)
**Type**: Number (Default: 0, Min: 0)
**Example**: 2

---

## FAMILY INFORMATION - CHILDREN FIELDS (Up to 4 children)

### BZ (78) - family.children[0].name
**Description**: Child 1 Name (Nama Anak 1)
**Type**: String (Optional)
**Example**: Ahmad Junior

### CA (79) - family.children[0].gender
**Description**: Child 1 Gender (Jenis Kelamin Anak 1)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### CB (80) - family.children[0].dateOfBirth
**Description**: Child 1 Date of Birth (Tanggal Lahir Anak 1)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2016-08-10

### CC (81) - family.children[1].name
**Description**: Child 2 Name (Nama Anak 2)
**Type**: String (Optional)
**Example**: Siti Junior

### CD (82) - family.children[1].gender
**Description**: Child 2 Gender (Jenis Kelamin Anak 2)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Perempuan

### CE (83) - family.children[1].dateOfBirth
**Description**: Child 2 Date of Birth (Tanggal Lahir Anak 2)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2018-12-05

### CF (84) - family.children[2].name
**Description**: Child 3 Name (Nama Anak 3)
**Type**: String (Optional)
**Example**: Budi Junior

### CG (85) - family.children[2].gender
**Description**: Child 3 Gender (Jenis Kelamin Anak 3)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### CH (86) - family.children[2].dateOfBirth
**Description**: Child 3 Date of Birth (Tanggal Lahir Anak 3)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2020-04-15

### CI (87) - family.children[3].name
**Description**: Child 4 Name (Nama Anak 4)
**Type**: String (Optional)
**Example**: Rina Junior

### CJ (88) - family.children[3].gender
**Description**: Child 4 Gender (Jenis Kelamin Anak 4)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Perempuan

### CK (89) - family.children[3].dateOfBirth
**Description**: Child 4 Date of Birth (Tanggal Lahir Anak 4)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2022-01-20

---

## FAMILY INFORMATION - PARENTS FIELDS

### CL (90) - family.parents.father.name
**Description**: Father Name (Nama Ayah)
**Type**: String (Optional)
**Example**: Budi Santoso

### CM (91) - family.parents.father.dateOfBirth
**Description**: Father Date of Birth (Tanggal Lahir Ayah)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1965-01-10

### CN (92) - family.parents.father.lastEducation
**Description**: Father Last Education (Pendidikan Terakhir Ayah)
**Type**: String (Optional)
**Example**: SMA

### CO (93) - family.parents.father.occupation
**Description**: Father Occupation (Pekerjaan Ayah)
**Type**: String (Optional)
**Example**: Petani

### CP (94) - family.parents.father.description
**Description**: Father Description (Deskripsi Ayah)
**Type**: String (Optional)
**Example**: Pensiunan PNS

### CQ (95) - family.parents.mother.name
**Description**: Mother Name (Nama Ibu)
**Type**: String (Optional)
**Example**: Siti Rahayu

### CR (96) - family.parents.mother.dateOfBirth
**Description**: Mother Date of Birth (Tanggal Lahir Ibu)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1968-05-15

### CS (97) - family.parents.mother.lastEducation
**Description**: Mother Last Education (Pendidikan Terakhir Ibu)
**Type**: String (Optional)
**Example**: SMP

### CT (98) - family.parents.mother.occupation
**Description**: Mother Occupation (Pekerjaan Ibu)
**Type**: String (Optional)
**Example**: Ibu Rumah Tangga

### CU (99) - family.parents.mother.description
**Description**: Mother Description (Deskripsi Ibu)
**Type**: String (Optional)
**Example**: Ibu rumah tangga

---

## FAMILY INFORMATION - SIBLINGS FIELDS

### CV (100) - family.siblings.childOrder
**Description**: Child Order (Anak Ke-)
**Type**: Number (Min: 1)
**Example**: 2

### CW (101) - family.siblings.totalSiblings
**Description**: Total Siblings (Jumlah Saudara Kandung)
**Type**: Number (Default: 0, Min: 0)
**Example**: 3

### CX (102) - family.siblings.siblingsData[0].name
**Description**: Sibling 1 Name (Nama Saudara 1)
**Type**: String (Optional)
**Example**: Andi Santoso

### CY (103) - family.siblings.siblingsData[0].gender
**Description**: Sibling 1 Gender (Jenis Kelamin Saudara 1)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### CZ (104) - family.siblings.siblingsData[0].dateOfBirth
**Description**: Sibling 1 Date of Birth (Tanggal Lahir Saudara 1)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1988-03-12

### DA (105) - family.siblings.siblingsData[0].lastEducation
**Description**: Sibling 1 Last Education (Pendidikan Terakhir Saudara 1)
**Type**: String (Optional)
**Example**: S1

### DB (106) - family.siblings.siblingsData[0].occupation
**Description**: Sibling 1 Occupation (Pekerjaan Saudara 1)
**Type**: String (Optional)
**Example**: Dokter

### DC (107) - family.siblings.siblingsData[0].description
**Description**: Sibling 1 Description (Deskripsi Saudara 1)
**Type**: String (Optional)
**Example**: Dokter di RS Swasta

### DD (108) - family.siblings.siblingsData[1].name
**Description**: Sibling 2 Name (Nama Saudara 2)
**Type**: String (Optional)
**Example**: Rina Santoso

### DE (109) - family.siblings.siblingsData[1].gender
**Description**: Sibling 2 Gender (Jenis Kelamin Saudara 2)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Perempuan

### DF (110) - family.siblings.siblingsData[1].dateOfBirth
**Description**: Sibling 2 Date of Birth (Tanggal Lahir Saudara 2)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1992-07-20

### DG (111) - family.siblings.siblingsData[1].lastEducation
**Description**: Sibling 2 Last Education (Pendidikan Terakhir Saudara 2)
**Type**: String (Optional)
**Example**: D3

### DH (112) - family.siblings.siblingsData[1].occupation
**Description**: Sibling 2 Occupation (Pekerjaan Saudara 2)
**Type**: String (Optional)
**Example**: Perawat

### DI (113) - family.siblings.siblingsData[1].description
**Description**: Sibling 2 Description (Deskripsi Saudara 2)
**Type**: String (Optional)
**Example**: Perawat di Puskesmas

### DJ (114) - family.siblings.siblingsData[2].name
**Description**: Sibling 3 Name (Nama Saudara 3)
**Type**: String (Optional)
**Example**: Dedi Santoso

### DK (115) - family.siblings.siblingsData[2].gender
**Description**: Sibling 3 Gender (Jenis Kelamin Saudara 3)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### DL (116) - family.siblings.siblingsData[2].dateOfBirth
**Description**: Sibling 3 Date of Birth (Tanggal Lahir Saudara 3)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1995-11-08

### DM (117) - family.siblings.siblingsData[2].lastEducation
**Description**: Sibling 3 Last Education (Pendidikan Terakhir Saudara 3)
**Type**: String (Optional)
**Example**: SMA

### DN (118) - family.siblings.siblingsData[2].occupation
**Description**: Sibling 3 Occupation (Pekerjaan Saudara 3)
**Type**: String (Optional)
**Example**: Mahasiswa

### DO (119) - family.siblings.siblingsData[2].description
**Description**: Sibling 3 Description (Deskripsi Saudara 3)
**Type**: String (Optional)
**Example**: Mahasiswa Teknik

---

## FAMILY INFORMATION - IN-LAWS FIELDS

### DP (120) - family.inLaws.fatherInLaw.name
**Description**: Father-in-Law Name (Nama Mertua Laki-laki)
**Type**: String (Optional)
**Example**: Hasan Wijaya

### DQ (121) - family.inLaws.fatherInLaw.dateOfBirth
**Description**: Father-in-Law Date of Birth (Tanggal Lahir Mertua Laki-laki)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1960-02-14

### DR (122) - family.inLaws.fatherInLaw.lastEducation
**Description**: Father-in-Law Last Education (Pendidikan Terakhir Mertua Laki-laki)
**Type**: String (Optional)
**Example**: S1

### DS (123) - family.inLaws.fatherInLaw.description
**Description**: Father-in-Law Description (Deskripsi Mertua Laki-laki)
**Type**: String (Optional)
**Example**: Pensiunan Guru

### DT (124) - family.inLaws.motherInLaw.name
**Description**: Mother-in-Law Name (Nama Mertua Perempuan)
**Type**: String (Optional)
**Example**: Fatimah Wijaya

### DU (125) - family.inLaws.motherInLaw.dateOfBirth
**Description**: Mother-in-Law Date of Birth (Tanggal Lahir Mertua Perempuan)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1963-09-25

### DV (126) - family.inLaws.motherInLaw.lastEducation
**Description**: Mother-in-Law Last Education (Pendidikan Terakhir Mertua Perempuan)
**Type**: String (Optional)
**Example**: SMA

### DW (127) - family.inLaws.motherInLaw.description
**Description**: Mother-in-Law Description (Deskripsi Mertua Perempuan)
**Type**: String (Optional)
**Example**: Ibu rumah tangga

---

## SYSTEM FIELDS

### DX (128) - isActive
**Description**: Active Status (Status Aktif)
**Type**: Boolean (Default: true)
**Example**: true

### DY (129) - createdAt
**Description**: Created Date (Tanggal Dibuat)
**Type**: Date (Auto-generated)
**Format**: YYYY-MM-DD HH:mm:ss
**Example**: 2024-01-15 10:30:00

### DZ (130) - updatedAt
**Description**: Updated Date (Tanggal Diperbarui)
**Type**: Date (Auto-generated)
**Format**: YYYY-MM-DD HH:mm:ss
**Example**: 2024-01-15 10:30:00

---

## SUMMARY

**Total Fields**: 130 columns
**Required Fields**: 37 fields
**Optional Fields**: 93 fields
**Reference Fields**: 11 fields (Division, Department, Position, Tags, Manager, DirectSupervisor, EmploymentType, RankCategory, RankGrade, RankSubgrade)
**Array Fields**: 3 fields (Tags, Children, Siblings)

## NOTES FOR IMPLEMENTATION

1. **Reference Field Mapping**: Fields with ObjectId references will need to be mapped from names to ObjectIds during import
2. **Date Format Handling**: Support both YYYY-MM-DD and DD/MM/YYYY formats
3. **Array Field Processing**: Handle comma-separated values for Tags and dynamic arrays for Children/Siblings
4. **Validation**: Implement comprehensive validation for required fields and enum values
5. **Error Handling**: Provide detailed error messages for invalid data
6. **Batch Processing**: Process imports in batches for better performance
7. **Progress Tracking**: Implement real-time progress updates during import
8. **Duplicate Prevention**: Check for existing Employee IDs before import

---

**Document Version**: 1.0
**Created**: 2024-12-07
**Purpose**: Employee Bulk Import System (Phase 3B) Implementation
**Project**: Bebang Information System (BIS) - PT. Prima Sarana Gemilang
