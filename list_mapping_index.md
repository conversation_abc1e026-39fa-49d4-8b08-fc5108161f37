# Employee Bulk Import System - Field Mapping Document (Ultra Complete Template)

## Overview
This document provides the CORRECT mapping between Excel Ultra Complete Template columns and Employee database fields for the Employee Bulk Import System (Phase 3B). This mapping is based on the actual template used in the system (ultraCompleteTemplateService.ts).

## Field Mapping Structure
Format: `Column Letter (Index) - database_field_name`

---

## PERSONAL INFORMATION FIELDS

### A (1) - personal.employeeId
**Frontend Label**: NIK*
**Template Label**: NIK*
**Description**: Employee ID/NIK (Nomor Induk Ka<PERSON>wan)
**Type**: String (Required, Unique, Uppercase)
**Example**: EMP001

### B (2) - personal.fullName
**Frontend Label**: <PERSON>a <PERSON>*
**Template Label**: Nama <PERSON>*
**Description**: Full Name (<PERSON>a <PERSON>)
**Type**: String (Required, Max 100 chars)
**Example**: <PERSON>

### C (3) - personal.gender
**Frontend Label**: <PERSON><PERSON>*
**Template Label**: <PERSON><PERSON>*
**Description**: Gender (<PERSON><PERSON>)
**Type**: Enum String (Required)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### D (4) - personal.placeOfBirth
**Frontend Label**: Tempat Lahir*
**Template Label**: Tempat Lahir*
**Description**: Place of Birth (Tempat Lahir)
**Type**: String (Required)
**Example**: Jakarta

### E (5) - personal.dateOfBirth
**Frontend Label**: Tanggal Lahir*
**Template Label**: Tanggal Lahir*
**Description**: Date of Birth (Tanggal Lahir)
**Type**: Date (Required)
**Format**: DD-MM-YYYY
**Example**: 15-01-1990

### F (6) - personal.email
**Frontend Label**: Email Pribadi
**Template Label**: Email Pribadi
**Description**: Personal Email (Email Pribadi)
**Type**: String (Optional, Unique, Lowercase)
**Example**: <EMAIL>

### G (7) - personal.phone
**Frontend Label**: No HP*
**Template Label**: No HP*
**Description**: Phone Number (Nomor HP)
**Type**: String (Required)
**Example**: 08**********

### H (8) - personal.religion
**Frontend Label**: Agama*
**Template Label**: Agama*
**Description**: Religion (Agama)
**Type**: Enum String (Required)
**Options**: Islam, Kristen, Katolik, Hindu, Buddha, Konghucu, Lainnya
**Example**: Islam

### I (9) - personal.bloodType
**Frontend Label**: Golongan Darah*
**Template Label**: Golongan Darah*
**Description**: Blood Type (Golongan Darah)
**Type**: Enum String (Required)
**Options**: A, B, AB, O
**Example**: A

### J (10) - personal.familyCardNumber
**Frontend Label**: No KK*
**Template Label**: No KK*
**Description**: Family Card Number (Nomor KK)
**Type**: String (Required)
**Example**: 317**********123

### K (11) - personal.idCardNumber
**Frontend Label**: No KTP*
**Template Label**: No KTP*
**Description**: ID Card Number (Nomor KTP)
**Type**: String (Required)
**Example**: 317**********123

### L (12) - personal.taxNumber
**Frontend Label**: NPWP
**Template Label**: NPWP
**Description**: Tax Number (NPWP)
**Type**: String (Optional)
**Example**: **********12345

### M (13) - personal.bpjsTkNumber
**Frontend Label**: No BPJS TK
**Template Label**: No BPJS TK
**Description**: BPJS TK Number (Nomor BPJS TK)
**Type**: String (Optional)
**Example**: **********1

### N (14) - personal.nikKkNumber
**Frontend Label**: NIK KK
**Template Label**: NIK KK
**Description**: NIK KK Number (NIK di KK)
**Type**: String (Optional)
**Example**: 317**********123

### O (15) - personal.taxStatus
**Frontend Label**: Status Pajak*
**Template Label**: Status Pajak*
**Description**: Tax Status (Status Pajak)
**Type**: Enum String (Required)
**Options**: TK/0, TK/1, TK/2, TK/3, K/0, K/1, K/2, K/3
**Example**: TK/0

---

## ADDRESS INFORMATION FIELDS

### P (16) - personal.currentAddress.street
**Frontend Label**: Alamat Domisili - Jalan*
**Template Label**: Alamat Domisili - Jalan*
**Description**: Current Address - Street (Alamat Domisili - Jalan)
**Type**: String (Required)
**Example**: Jl. Sudirman No. 123

### Q (17) - personal.currentAddress.city
**Frontend Label**: Alamat Domisili - Kota*
**Template Label**: Alamat Domisili - Kota*
**Description**: Current Address - City (Alamat Domisili - Kota)
**Type**: String (Required)
**Example**: Jakarta Pusat

### R (18) - personal.currentAddress.province
**Frontend Label**: Alamat Domisili - Provinsi*
**Template Label**: Alamat Domisili - Provinsi*
**Description**: Current Address - Province (Alamat Domisili - Provinsi)
**Type**: String (Required)
**Example**: DKI Jakarta

### S (19) - personal.idCardAddress.street
**Frontend Label**: Alamat KTP - Jalan*
**Template Label**: Alamat KTP - Jalan*
**Description**: ID Card Address - Street (Alamat KTP - Jalan)
**Type**: String (Required)
**Example**: Jl. Thamrin No. 456

### T (20) - personal.idCardAddress.city
**Frontend Label**: Alamat KTP - Kota*
**Template Label**: Alamat KTP - Kota*
**Description**: ID Card Address - City (Alamat KTP - Kota)
**Type**: String (Required)
**Example**: Jakarta Pusat

### U (21) - personal.idCardAddress.province
**Frontend Label**: Alamat KTP - Provinsi*
**Template Label**: Alamat KTP - Provinsi*
**Description**: ID Card Address - Province (Alamat KTP - Provinsi)
**Type**: String (Required)
**Example**: DKI Jakarta

---

## CONTACT INFORMATION FIELDS

### V (22) - personal.contact.mobilePhone1
**Frontend Label**: HP 1*
**Template Label**: HP 1*
**Description**: Mobile Phone 1 (HP 1)
**Type**: String (Required)
**Example**: 08**********

### W (23) - personal.contact.mobilePhone2
**Frontend Label**: HP 2
**Template Label**: HP 2
**Description**: Mobile Phone 2 (HP 2)
**Type**: String (Optional)
**Example**: 081234567891

### X (24) - personal.contact.homePhone1
**Frontend Label**: Telepon Rumah 1
**Template Label**: Telepon Rumah 1
**Description**: Home Phone 1 (Telepon Rumah 1)
**Type**: String (Optional)
**Example**: 0212345678

### M (13) - personal.placeOfBirth
**Frontend Label**: Tempat Lahir
**Description**: Place of Birth (Tempat Lahir)
**Type**: String (Required)
**Example**: Jakarta

### N (14) - personal.dateOfBirth
**Frontend Label**: Tanggal Lahir
**Description**: Date of Birth (Tanggal Lahir)
**Type**: Date (Required)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1990-05-15

### O (15) - personal.email
**Frontend Label**: Email
**Description**: Personal Email (Email Pribadi)
**Type**: String (Optional, Unique, Lowercase)
**Example**: <EMAIL>

### P (16) - personal.religion
**Frontend Label**: Agama
**Description**: Religion (Agama)
**Type**: Enum String (Required)
**Options**: Islam, Kristen, Katolik, Hindu, Buddha, Konghucu, Lainnya
**Example**: Islam

### Q (17) - personal.bloodType
**Frontend Label**: Golongan Darah
**Description**: Blood Type (Golongan Darah)
**Type**: Enum String (Required)
**Options**: A, B, AB, O
**Example**: A

### R (18) - personal.familyCardNumber
**Frontend Label**: Nomor KK
**Description**: Family Card Number (Nomor KK)
**Type**: String (Required)
**Example**: 320**********123

### S (19) - personal.idCardNumber
**Frontend Label**: Nomor KTP
**Description**: ID Card Number (Nomor KTP)
**Type**: String (Required)
**Example**: 320**********123

### T (20) - personal.taxNumber
**Frontend Label**: NPWP
**Description**: Tax Number (NPWP)
**Type**: String (Optional)
**Example**: 12.345.678.9-012.000

### U (21) - personal.bpjsTkNumber
**Frontend Label**: Nomor BPJS TK
**Description**: BPJS TK Number (Nomor BPJS TK)
**Type**: String (Optional)
**Example**: **********1

### V (22) - personal.nikKkNumber
**Frontend Label**: NIK di KK
**Description**: NIK KK Number (NIK di KK)
**Type**: String (Optional)
**Example**: 320**********123

### W (23) - personal.taxStatus
**Frontend Label**: Status Pajak
**Description**: Tax Status (Status Pajak)
**Type**: Enum String (Required)
**Options**: TK/0, TK/1, TK/2, TK/3, K/0, K/1, K/2, K/3
**Example**: TK/0

---

## ADDRESS INFORMATION FIELDS

### X (24) - personal.currentAddress.street
**Frontend Label**: Alamat Domisili - Jalan
**Description**: Current Address - Street (Alamat Domisili - Jalan)
**Type**: String (Optional)
**Example**: Jl. Merdeka No. 123

### Y (25) - personal.currentAddress.city
**Frontend Label**: Alamat Domisili - Kota
**Description**: Current Address - City (Alamat Domisili - Kota)
**Type**: String (Optional)
**Example**: Jakarta Pusat

### Z (26) - personal.currentAddress.province
**Frontend Label**: Alamat Domisili - Provinsi
**Description**: Current Address - Province (Alamat Domisili - Provinsi)
**Type**: String (Optional)
**Example**: DKI Jakarta

### AA (27) - personal.idCardAddress.street
**Frontend Label**: Alamat KTP - Jalan
**Description**: ID Card Address - Street (Alamat KTP - Jalan)
**Type**: String (Optional)
**Example**: Jl. Sudirman No. 456

### AB (28) - personal.idCardAddress.city
**Frontend Label**: Alamat KTP - Kota
**Description**: ID Card Address - City (Alamat KTP - Kota)
**Type**: String (Optional)
**Example**: Jakarta Selatan

### AC (29) - personal.idCardAddress.province
**Frontend Label**: Alamat KTP - Provinsi
**Description**: ID Card Address - Province (Alamat KTP - Provinsi)
**Type**: String (Optional)
**Example**: DKI Jakarta

---

## CONTACT INFORMATION FIELDS

### AD (30) - personal.contact.mobilePhone1
**Frontend Label**: HP 1
**Description**: Mobile Phone 1 (HP 1)
**Type**: String (Optional)
**Example**: +62812345678

### AE (31) - personal.contact.mobilePhone2
**Frontend Label**: HP 2
**Description**: Mobile Phone 2 (HP 2)
**Type**: String (Optional)
**Example**: +62812345679

### AF (32) - personal.contact.homePhone1
**Frontend Label**: Telepon Rumah 1
**Description**: Home Phone 1 (Telepon Rumah 1)
**Type**: String (Optional)
**Example**: 021-1234567

### AG (33) - personal.contact.homePhone2
**Frontend Label**: Telepon Rumah 2
**Description**: Home Phone 2 (Telepon Rumah 2)
**Type**: String (Optional)
**Example**: 021-1234568

---

## MARITAL STATUS FIELDS

### AH (34) - personal.maritalInfo.status
**Frontend Label**: Status Pernikahan
**Description**: Marital Status (Status Pernikahan)
**Type**: Enum String (Required)
**Options**: Belum Menikah, Menikah, Cerai, Janda/Duda
**Example**: Menikah

### AI (35) - personal.maritalInfo.spouseName
**Frontend Label**: Nama Pasangan
**Description**: Spouse Name (Nama Pasangan)
**Type**: String (Optional)
**Example**: Siti Nurhaliza

### AJ (36) - personal.maritalInfo.spouseJob
**Frontend Label**: Pekerjaan Pasangan
**Description**: Spouse Job (Pekerjaan Pasangan)
**Type**: String (Optional)
**Example**: Guru

### AK (37) - personal.maritalInfo.numberOfChildren
**Frontend Label**: Jumlah Anak
**Description**: Number of Children (Jumlah Anak)
**Type**: Number (Default: 0, Min: 0)
**Example**: 2

---

## BANK ACCOUNT FIELDS

### AL (38) - personal.bankAccount.accountNumber
**Frontend Label**: Nomor Rekening
**Description**: Bank Account Number (Nomor Rekening)
**Type**: String (Required)
**Example**: **********

### AM (39) - personal.bankAccount.accountHolder
**Frontend Label**: Nama Pemegang Rekening
**Description**: Account Holder Name (Nama Pemegang Rekening)
**Type**: String (Required)
**Example**: Ahmad Budi Santoso

### AN (40) - personal.bankAccount.bankName
**Frontend Label**: Nama Bank
**Description**: Bank Name (Nama Bank)
**Type**: String (Required)
**Example**: Bank Mandiri

---

## HR CONTRACT INFORMATION FIELDS

### AO (41) - hr.contract.employmentType
**Frontend Label**: Jenis Kontrak
**Description**: Employment Type (Jenis Kontrak)
**Type**: ObjectId Reference to EmploymentType collection
**Example**: Employment Type Name (will be mapped to ObjectId)

### AP (42) - hr.contract.hireDate
**Frontend Label**: Tanggal Masuk
**Description**: Hire Date (Tanggal Masuk)
**Type**: Date (Required)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2023-01-15

### AQ (43) - hr.contract.contractDate
**Frontend Label**: Tanggal Kontrak
**Description**: Contract Date (Tanggal Kontrak)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2023-01-15

### AR (44) - hr.contract.contractEndDate
**Frontend Label**: Tanggal Berakhir Kontrak
**Description**: Contract End Date (Tanggal Berakhir Kontrak)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2024-01-15

### AS (45) - hr.contract.permanentDate
**Frontend Label**: Tanggal Tetap
**Description**: Permanent Date (Tanggal Tetap)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2023-07-15

---

## EDUCATION FIELDS

### AT (46) - hr.education.certificateLevel
**Frontend Label**: Tingkat Pendidikan
**Description**: Certificate Level (Tingkat Pendidikan)
**Type**: Enum String (Optional)
**Options**: SD, SMP, SMA, SMK, D1, D2, D3, S1, S2, S3
**Example**: S1

### AU (47) - hr.education.fieldOfStudy
**Frontend Label**: Bidang Studi
**Description**: Field of Study (Bidang Studi)
**Type**: String (Optional)
**Example**: Teknik Informatika

### AV (48) - hr.education.schoolName
**Frontend Label**: Nama Sekolah/Universitas
**Description**: School Name (Nama Sekolah/Universitas)
**Type**: String (Optional)
**Example**: Universitas Indonesia

### AW (49) - hr.education.schoolCity
**Frontend Label**: Kota Sekolah/Universitas
**Description**: School City (Kota Sekolah/Universitas)
**Type**: String (Optional)
**Example**: Jakarta

### AX (50) - hr.education.graduationStatus
**Frontend Label**: Status Kelulusan
**Description**: Graduation Status (Status Kelulusan)
**Type**: Enum String (Optional)
**Options**: Lulus, Tidak Lulus, Sedang Belajar
**Example**: Lulus

### AY (51) - hr.education.description
**Frontend Label**: Deskripsi Pendidikan
**Description**: Education Description (Deskripsi Pendidikan)
**Type**: String (Optional)
**Example**: Cumlaude dengan IPK 3.8

---

## RANK AND GRADE FIELDS

### AZ (52) - hr.rank.rankCategory
**Description**: Rank Category (Kategori Pangkat)  
**Type**: ObjectId Reference to RankCategory collection  
**Example**: Rank Category Name (will be mapped to ObjectId)

### BA (53) - hr.rank.rankGrade
**Description**: Rank Grade (Tingkat Pangkat)  
**Type**: ObjectId Reference to RankGrade collection  
**Example**: Rank Grade Name (will be mapped to ObjectId)

### BB (54) - hr.rank.rankSubgrade
**Description**: Rank Subgrade (Sub Tingkat Pangkat)  
**Type**: ObjectId Reference to RankSubgrade collection  
**Example**: Rank Subgrade Name (will be mapped to ObjectId)

### BC (55) - hr.rank.pensionFundNumber
**Description**: Pension Fund Number (Nomor Dana Pensiun)  
**Type**: String (Optional)  
**Example**: DP123456789

---

## EMERGENCY CONTACT FIELDS

### BD (56) - hr.emergency.contactName
**Frontend Label**: Nama Kontak Darurat
**Description**: Emergency Contact Name (Nama Kontak Darurat)
**Type**: String (Required)
**Example**: Siti Nurhaliza

### BE (57) - hr.emergency.contactPhone
**Frontend Label**: Telepon Kontak Darurat
**Description**: Emergency Contact Phone (Telepon Kontak Darurat)
**Type**: String (Required)
**Example**: +62812345678

### BF (58) - hr.emergency.contactPhone2
**Frontend Label**: Telepon Kontak Darurat 2
**Description**: Emergency Contact Phone 2 (Telepon Kontak Darurat 2)
**Type**: String (Optional)
**Example**: +62812345679

### BG (59) - hr.emergency.relationship
**Frontend Label**: Hubungan Kontak Darurat
**Description**: Emergency Contact Relationship (Hubungan Kontak Darurat)
**Type**: String (Required)
**Example**: Istri

### BH (60) - hr.emergency.address
**Frontend Label**: Alamat Kontak Darurat
**Description**: Emergency Contact Address (Alamat Kontak Darurat)
**Type**: String (Required)
**Example**: Jl. Merdeka No. 123, Jakarta

---

## LOCATION FIELDS (POO/POH)

### BI (61) - hr.location.pointOfOrigin
**Description**: Point of Origin (POO - Tempat Asal)  
**Type**: String (Required)  
**Example**: Jakarta

### BJ (62) - hr.location.pointOfHire
**Description**: Point of Hire (POH - Tempat Kerja)  
**Type**: String (Required)  
**Example**: Taliabu

---

## UNIFORM AND WORK EQUIPMENT FIELDS

### BK (63) - hr.uniform.workUniformSize
**Description**: Work Uniform Size (Ukuran Seragam Kerja)  
**Type**: Enum String (Required)  
**Options**: XS, S, M, L, XL, XXL, XXXL  
**Example**: L

### BL (64) - hr.uniform.workShoesSize
**Description**: Work Shoes Size (Ukuran Sepatu Kerja)  
**Type**: String (Required)  
**Example**: 42

---

## SALARY FIELDS

### BM (65) - hr.salary.basic
**Description**: Basic Salary (Gaji Pokok)  
**Type**: Number (Optional)  
**Example**: 5000000

### BN (66) - hr.salary.allowances.transport
**Description**: Transport Allowance (Tunjangan Transport)  
**Type**: Number (Default: 0)  
**Example**: 500000

### BO (67) - hr.salary.allowances.meal
**Description**: Meal Allowance (Tunjangan Makan)  
**Type**: Number (Default: 0)  
**Example**: 300000

### BP (68) - hr.salary.allowances.communication
**Description**: Communication Allowance (Tunjangan Komunikasi)  
**Type**: Number (Default: 0)  
**Example**: 200000

### BQ (69) - hr.salary.allowances.position
**Description**: Position Allowance (Tunjangan Jabatan)  
**Type**: Number (Default: 0)  
**Example**: 1000000

### BR (70) - hr.salary.allowances.other
**Description**: Other Allowance (Tunjangan Lainnya)  
**Type**: Number (Default: 0)  
**Example**: 100000

### BS (71) - hr.workSchedule
**Description**: Work Schedule (Jadwal Kerja)
**Type**: Enum String (Default: Regular)
**Options**: Regular, Shift, Flexible, Remote, Part Time
**Example**: Regular

---

## FAMILY INFORMATION - SPOUSE FIELDS

### BT (72) - family.spouse.name
**Frontend Label**: Nama Pasangan
**Description**: Spouse Name (Nama Pasangan)
**Type**: String (Optional)
**Example**: Siti Nurhaliza

### BU (73) - family.spouse.dateOfBirth
**Frontend Label**: Tanggal Lahir Pasangan
**Description**: Spouse Date of Birth (Tanggal Lahir Pasangan)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1992-03-20

### BV (74) - family.spouse.marriageDate
**Frontend Label**: Tanggal Menikah
**Description**: Marriage Date (Tanggal Menikah)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2015-06-15

### BW (75) - family.spouse.lastEducation
**Frontend Label**: Pendidikan Terakhir Pasangan
**Description**: Spouse Last Education (Pendidikan Terakhir Pasangan)
**Type**: String (Optional)
**Example**: S1 Pendidikan

### BX (76) - family.spouse.occupation
**Frontend Label**: Pekerjaan Pasangan
**Description**: Spouse Occupation (Pekerjaan Pasangan)
**Type**: String (Optional)
**Example**: Guru

### BY (77) - family.spouse.numberOfChildren
**Frontend Label**: Jumlah Anak
**Description**: Number of Children (Jumlah Anak)
**Type**: Number (Default: 0, Min: 0)
**Example**: 2

---

## FAMILY INFORMATION - CHILDREN FIELDS (Up to 4 children)

### BZ (78) - family.children[0].name
**Description**: Child 1 Name (Nama Anak 1)
**Type**: String (Optional)
**Example**: Ahmad Junior

### CA (79) - family.children[0].gender
**Description**: Child 1 Gender (Jenis Kelamin Anak 1)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### CB (80) - family.children[0].dateOfBirth
**Description**: Child 1 Date of Birth (Tanggal Lahir Anak 1)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2016-08-10

### CC (81) - family.children[1].name
**Description**: Child 2 Name (Nama Anak 2)
**Type**: String (Optional)
**Example**: Siti Junior

### CD (82) - family.children[1].gender
**Description**: Child 2 Gender (Jenis Kelamin Anak 2)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Perempuan

### CE (83) - family.children[1].dateOfBirth
**Description**: Child 2 Date of Birth (Tanggal Lahir Anak 2)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2018-12-05

### CF (84) - family.children[2].name
**Description**: Child 3 Name (Nama Anak 3)
**Type**: String (Optional)
**Example**: Budi Junior

### CG (85) - family.children[2].gender
**Description**: Child 3 Gender (Jenis Kelamin Anak 3)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### CH (86) - family.children[2].dateOfBirth
**Description**: Child 3 Date of Birth (Tanggal Lahir Anak 3)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2020-04-15

### CI (87) - family.children[3].name
**Description**: Child 4 Name (Nama Anak 4)
**Type**: String (Optional)
**Example**: Rina Junior

### CJ (88) - family.children[3].gender
**Description**: Child 4 Gender (Jenis Kelamin Anak 4)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Perempuan

### CK (89) - family.children[3].dateOfBirth
**Description**: Child 4 Date of Birth (Tanggal Lahir Anak 4)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 2022-01-20

---

## FAMILY INFORMATION - PARENTS FIELDS

### CL (90) - family.parents.father.name
**Frontend Label**: Nama Ayah Kandung
**Description**: Father Name (Nama Ayah)
**Type**: String (Optional)
**Example**: Budi Santoso

### CM (91) - family.parents.father.dateOfBirth
**Frontend Label**: Tanggal Lahir Ayah Kandung
**Description**: Father Date of Birth (Tanggal Lahir Ayah)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1965-01-10

### CN (92) - family.parents.father.lastEducation
**Frontend Label**: Pendidikan Terakhir Ayah Kandung
**Description**: Father Last Education (Pendidikan Terakhir Ayah)
**Type**: String (Optional)
**Example**: SMA

### CO (93) - family.parents.father.occupation
**Frontend Label**: Pekerjaan Ayah Kandung
**Description**: Father Occupation (Pekerjaan Ayah)
**Type**: String (Optional)
**Example**: Petani

### CP (94) - family.parents.father.description
**Frontend Label**: Keterangan Ayah Kandung
**Description**: Father Description (Deskripsi Ayah)
**Type**: String (Optional)
**Example**: Pensiunan PNS

### CQ (95) - family.parents.mother.name
**Frontend Label**: Nama Ibu Kandung
**Description**: Mother Name (Nama Ibu)
**Type**: String (Optional)
**Example**: Siti Rahayu

### CR (96) - family.parents.mother.dateOfBirth
**Frontend Label**: Tanggal Lahir Ibu Kandung
**Description**: Mother Date of Birth (Tanggal Lahir Ibu)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1968-05-15

### CS (97) - family.parents.mother.lastEducation
**Frontend Label**: Pendidikan Terakhir Ibu Kandung
**Description**: Mother Last Education (Pendidikan Terakhir Ibu)
**Type**: String (Optional)
**Example**: SMP

### CT (98) - family.parents.mother.occupation
**Frontend Label**: Pekerjaan Ibu Kandung
**Description**: Mother Occupation (Pekerjaan Ibu)
**Type**: String (Optional)
**Example**: Ibu Rumah Tangga

### CU (99) - family.parents.mother.description
**Frontend Label**: Keterangan Ibu Kandung
**Description**: Mother Description (Deskripsi Ibu)
**Type**: String (Optional)
**Example**: Ibu rumah tangga

---

## FAMILY INFORMATION - SIBLINGS FIELDS

### CV (100) - family.siblings.childOrder
**Description**: Child Order (Anak Ke-)
**Type**: Number (Min: 1)
**Example**: 2

### CW (101) - family.siblings.totalSiblings
**Description**: Total Siblings (Jumlah Saudara Kandung)
**Type**: Number (Default: 0, Min: 0)
**Example**: 3

### CX (102) - family.siblings.siblingsData[0].name
**Description**: Sibling 1 Name (Nama Saudara 1)
**Type**: String (Optional)
**Example**: Andi Santoso

### CY (103) - family.siblings.siblingsData[0].gender
**Description**: Sibling 1 Gender (Jenis Kelamin Saudara 1)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### CZ (104) - family.siblings.siblingsData[0].dateOfBirth
**Description**: Sibling 1 Date of Birth (Tanggal Lahir Saudara 1)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1988-03-12

### DA (105) - family.siblings.siblingsData[0].lastEducation
**Description**: Sibling 1 Last Education (Pendidikan Terakhir Saudara 1)
**Type**: String (Optional)
**Example**: S1

### DB (106) - family.siblings.siblingsData[0].occupation
**Description**: Sibling 1 Occupation (Pekerjaan Saudara 1)
**Type**: String (Optional)
**Example**: Dokter

### DC (107) - family.siblings.siblingsData[0].description
**Description**: Sibling 1 Description (Deskripsi Saudara 1)
**Type**: String (Optional)
**Example**: Dokter di RS Swasta

### DD (108) - family.siblings.siblingsData[1].name
**Description**: Sibling 2 Name (Nama Saudara 2)
**Type**: String (Optional)
**Example**: Rina Santoso

### DE (109) - family.siblings.siblingsData[1].gender
**Description**: Sibling 2 Gender (Jenis Kelamin Saudara 2)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Perempuan

### DF (110) - family.siblings.siblingsData[1].dateOfBirth
**Description**: Sibling 2 Date of Birth (Tanggal Lahir Saudara 2)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1992-07-20

### DG (111) - family.siblings.siblingsData[1].lastEducation
**Description**: Sibling 2 Last Education (Pendidikan Terakhir Saudara 2)
**Type**: String (Optional)
**Example**: D3

### DH (112) - family.siblings.siblingsData[1].occupation
**Description**: Sibling 2 Occupation (Pekerjaan Saudara 2)
**Type**: String (Optional)
**Example**: Perawat

### DI (113) - family.siblings.siblingsData[1].description
**Description**: Sibling 2 Description (Deskripsi Saudara 2)
**Type**: String (Optional)
**Example**: Perawat di Puskesmas

### DJ (114) - family.siblings.siblingsData[2].name
**Description**: Sibling 3 Name (Nama Saudara 3)
**Type**: String (Optional)
**Example**: Dedi Santoso

### DK (115) - family.siblings.siblingsData[2].gender
**Description**: Sibling 3 Gender (Jenis Kelamin Saudara 3)
**Type**: Enum String (Optional)
**Options**: Laki-laki, Perempuan
**Example**: Laki-laki

### DL (116) - family.siblings.siblingsData[2].dateOfBirth
**Description**: Sibling 3 Date of Birth (Tanggal Lahir Saudara 3)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1995-11-08

### DM (117) - family.siblings.siblingsData[2].lastEducation
**Description**: Sibling 3 Last Education (Pendidikan Terakhir Saudara 3)
**Type**: String (Optional)
**Example**: SMA

### DN (118) - family.siblings.siblingsData[2].occupation
**Description**: Sibling 3 Occupation (Pekerjaan Saudara 3)
**Type**: String (Optional)
**Example**: Mahasiswa

### DO (119) - family.siblings.siblingsData[2].description
**Description**: Sibling 3 Description (Deskripsi Saudara 3)
**Type**: String (Optional)
**Example**: Mahasiswa Teknik

---

## FAMILY INFORMATION - IN-LAWS FIELDS

### DP (120) - family.inLaws.fatherInLaw.name
**Description**: Father-in-Law Name (Nama Mertua Laki-laki)
**Type**: String (Optional)
**Example**: Hasan Wijaya

### DQ (121) - family.inLaws.fatherInLaw.dateOfBirth
**Description**: Father-in-Law Date of Birth (Tanggal Lahir Mertua Laki-laki)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1960-02-14

### DR (122) - family.inLaws.fatherInLaw.lastEducation
**Description**: Father-in-Law Last Education (Pendidikan Terakhir Mertua Laki-laki)
**Type**: String (Optional)
**Example**: S1

### DS (123) - family.inLaws.fatherInLaw.description
**Description**: Father-in-Law Description (Deskripsi Mertua Laki-laki)
**Type**: String (Optional)
**Example**: Pensiunan Guru

### DT (124) - family.inLaws.motherInLaw.name
**Description**: Mother-in-Law Name (Nama Mertua Perempuan)
**Type**: String (Optional)
**Example**: Fatimah Wijaya

### DU (125) - family.inLaws.motherInLaw.dateOfBirth
**Description**: Mother-in-Law Date of Birth (Tanggal Lahir Mertua Perempuan)
**Type**: Date (Optional)
**Format**: YYYY-MM-DD or DD/MM/YYYY
**Example**: 1963-09-25

### DV (126) - family.inLaws.motherInLaw.lastEducation
**Description**: Mother-in-Law Last Education (Pendidikan Terakhir Mertua Perempuan)
**Type**: String (Optional)
**Example**: SMA

### DW (127) - family.inLaws.motherInLaw.description
**Description**: Mother-in-Law Description (Deskripsi Mertua Perempuan)
**Type**: String (Optional)
**Example**: Ibu rumah tangga

---

## SYSTEM FIELDS

### DX (128) - isActive
**Description**: Active Status (Status Aktif)
**Type**: Boolean (Default: true)
**Example**: true

### DY (129) - createdAt
**Description**: Created Date (Tanggal Dibuat)
**Type**: Date (Auto-generated)
**Format**: YYYY-MM-DD HH:mm:ss
**Example**: 2024-01-15 10:30:00

### DZ (130) - updatedAt
**Description**: Updated Date (Tanggal Diperbarui)
**Type**: Date (Auto-generated)
**Format**: YYYY-MM-DD HH:mm:ss
**Example**: 2024-01-15 10:30:00

---

## SUMMARY

**Total Fields**: 130 columns
**Required Fields**: 37 fields
**Optional Fields**: 93 fields
**Reference Fields**: 11 fields (Division, Department, Position, Tags, Manager, DirectSupervisor, EmploymentType, RankCategory, RankGrade, RankSubgrade)
**Array Fields**: 3 fields (Tags, Children, Siblings)

## NOTES FOR IMPLEMENTATION

1. **Reference Field Mapping**: Fields with ObjectId references will need to be mapped from names to ObjectIds during import
2. **Date Format Handling**: Support both YYYY-MM-DD and DD/MM/YYYY formats
3. **Array Field Processing**: Handle comma-separated values for Tags and dynamic arrays for Children/Siblings
4. **Validation**: Implement comprehensive validation for required fields and enum values
5. **Error Handling**: Provide detailed error messages for invalid data
6. **Batch Processing**: Process imports in batches for better performance
7. **Progress Tracking**: Implement real-time progress updates during import
8. **Duplicate Prevention**: Check for existing Employee IDs before import

---

**Document Version**: 1.0
**Created**: 2024-12-07
**Purpose**: Employee Bulk Import System (Phase 3B) Implementation
**Project**: Bebang Information System (BIS) - PT. Prima Sarana Gemilang
