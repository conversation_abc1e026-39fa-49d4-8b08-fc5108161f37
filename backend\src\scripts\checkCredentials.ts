import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/psg-sisinfo';

async function connectDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function checkCredentials() {
  try {
    console.log('🔍 Checking login credentials status...\n');

    // Get database instance
    const db = mongoose.connection.db;
    
    // Check users collection
    const usersCollection = db.collection('users');
    const userCount = await usersCollection.countDocuments();
    console.log(`👤 Users in database: ${userCount}`);
    
    if (userCount > 0) {
      const users = await usersCollection.find({}).toArray();
      console.log('\n📋 Existing users:');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. Username: ${user.username || user.employeeId}`);
        console.log(`      Email: ${user.email}`);
        console.log(`      Role: ${user.role}`);
        console.log(`      Active: ${user.isActive}`);
        console.log('');
      });
    }
    
    // Check roles collection
    const rolesCollection = db.collection('roles');
    const roleCount = await rolesCollection.countDocuments();
    console.log(`🎭 Roles in database: ${roleCount}`);
    
    if (roleCount > 0) {
      const roles = await rolesCollection.find({}).toArray();
      console.log('\n📋 Existing roles:');
      roles.forEach((role, index) => {
        console.log(`   ${index + 1}. Name: ${role.name}`);
        console.log(`      Description: ${role.description}`);
        console.log('');
      });
    }
    
    // Summary
    console.log('📊 Summary:');
    if (userCount === 0 && roleCount === 0) {
      console.log('❌ NO LOGIN CREDENTIALS AVAILABLE');
      console.log('   You need to create admin user and roles to login');
      console.log('   Run: npm run seed to restore basic credentials');
    } else if (userCount > 0 && roleCount > 0) {
      console.log('✅ LOGIN CREDENTIALS AVAILABLE');
      console.log('   You can login with existing credentials');
    } else {
      console.log('⚠️  PARTIAL CREDENTIALS');
      console.log('   Some data missing - may need to reseed');
    }

  } catch (error) {
    console.error('❌ Error checking credentials:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await checkCredentials();
  } catch (error) {
    console.error('❌ Check failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the check
main();
