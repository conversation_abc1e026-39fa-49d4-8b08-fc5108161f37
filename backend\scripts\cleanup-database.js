const { MongoClient } = require('mongodb');

async function cleanupDatabase() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db('psg-sisinfo');
    const employeesCollection = db.collection('employees');
    
    // 1. Check current employees
    const allEmployees = await employeesCollection.find({}).toArray();
    console.log(`📊 Total employees found: ${allEmployees.length}`);
    
    // 2. Find employees with null employeeId
    const nullEmployeeId = await employeesCollection.find({ employeeId: null }).toArray();
    console.log(`🚨 Employees with null employeeId: ${nullEmployeeId.length}`);
    
    // 3. Find employees with empty personal.employeeId
    const emptyPersonalEmployeeId = await employeesCollection.find({ 
      $or: [
        { "personal.employeeId": null },
        { "personal.employeeId": "" },
        { "personal.employeeId": { $exists: false } }
      ]
    }).toArray();
    console.log(`🚨 Employees with empty personal.employeeId: ${emptyPersonalEmployeeId.length}`);
    
    // 4. List all indexes
    const indexes = await employeesCollection.indexes();
    console.log('📋 Current indexes:');
    indexes.forEach(index => {
      console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
    });
    
    // 5. Delete employees with null/empty employeeId (ghost data)
    const deleteResult1 = await employeesCollection.deleteMany({ employeeId: null });
    console.log(`🗑️ Deleted ${deleteResult1.deletedCount} employees with null employeeId`);
    
    const deleteResult2 = await employeesCollection.deleteMany({ 
      $or: [
        { "personal.employeeId": null },
        { "personal.employeeId": "" },
        { "personal.employeeId": { $exists: false } }
      ]
    });
    console.log(`🗑️ Deleted ${deleteResult2.deletedCount} employees with empty personal.employeeId`);
    
    // 6. Drop problematic index if exists
    try {
      await employeesCollection.dropIndex('employeeId_1');
      console.log('🗑️ Dropped employeeId_1 index');
    } catch (error) {
      console.log('ℹ️ employeeId_1 index not found or already dropped');
    }
    
    // 7. Create correct index for personal.employeeId
    try {
      await employeesCollection.createIndex({ "personal.employeeId": 1 }, { unique: true, sparse: true });
      console.log('✅ Created unique index for personal.employeeId');
    } catch (error) {
      console.log('ℹ️ Index for personal.employeeId already exists');
    }
    
    // 8. Final check
    const finalCount = await employeesCollection.countDocuments();
    console.log(`📊 Final employee count: ${finalCount}`);
    
    console.log('✅ Database cleanup completed!');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

cleanupDatabase();
