import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/psg-sisinfo';

async function connectDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function restoreCredentials() {
  try {
    console.log('🔐 Restoring basic login credentials...\n');

    const db = mongoose.connection.db;
    
    // 1. Create basic roles
    console.log('🎭 Creating basic roles...');
    
    const rolesCollection = db.collection('roles');
    const roles = [
      {
        name: 'admin',
        description: 'System Administrator - Full access to all modules',
        permissions: {
          hr: { create: true, read: true, update: true, delete: true, approve: true },
          inventory: { create: true, read: true, update: true, delete: true, approve: true },
          mess: { create: true, read: true, update: true, delete: true, approve: true },
          building: { create: true, read: true, update: true, delete: true, approve: true },
          userAccess: { create: true, read: true, update: true, delete: true, approve: true },
          chat: { create: true, read: true, update: true, delete: true, approve: true }
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'hr_manager',
        description: 'HR Manager - Full access to HR module',
        permissions: {
          hr: { create: true, read: true, update: true, delete: true, approve: true },
          inventory: { create: false, read: true, update: false, delete: false, approve: false },
          mess: { create: false, read: true, update: false, delete: false, approve: false },
          building: { create: false, read: true, update: false, delete: false, approve: false },
          userAccess: { create: false, read: false, update: false, delete: false, approve: false },
          chat: { create: true, read: true, update: true, delete: false, approve: false }
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'employee',
        description: 'Regular Employee - Limited access',
        permissions: {
          hr: { create: false, read: true, update: false, delete: false, approve: false },
          inventory: { create: false, read: true, update: false, delete: false, approve: false },
          mess: { create: false, read: true, update: false, delete: false, approve: false },
          building: { create: false, read: true, update: false, delete: false, approve: false },
          userAccess: { create: false, read: false, update: false, delete: false, approve: false },
          chat: { create: true, read: true, update: true, delete: false, approve: false }
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await rolesCollection.insertMany(roles);
    console.log(`✅ Created ${roles.length} roles`);

    // 2. Create admin user
    console.log('\n👤 Creating admin user...');
    
    const usersCollection = db.collection('users');
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    const adminUser = {
      username: 'ADM001',
      employeeId: 'ADM001',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      firstName: 'System',
      lastName: 'Administrator',
      isActive: true,
      isEmailVerified: true,
      lastLogin: null,
      loginAttempts: 0,
      lockUntil: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await usersCollection.insertOne(adminUser);
    console.log('✅ Created admin user');

    // 3. Create HR Manager user
    console.log('\n👤 Creating HR Manager user...');
    
    const hrPassword = await bcrypt.hash('hr123', 12);
    
    const hrUser = {
      username: 'HR001',
      employeeId: 'HR001',
      email: '<EMAIL>',
      password: hrPassword,
      role: 'hr_manager',
      firstName: 'HR',
      lastName: 'Manager',
      isActive: true,
      isEmailVerified: true,
      lastLogin: null,
      loginAttempts: 0,
      lockUntil: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await usersCollection.insertOne(hrUser);
    console.log('✅ Created HR Manager user');

    // 4. Verify creation
    console.log('\n📊 Verification:');
    const userCount = await usersCollection.countDocuments();
    const roleCount = await rolesCollection.countDocuments();
    
    console.log(`   Users created: ${userCount}`);
    console.log(`   Roles created: ${roleCount}`);

    console.log('\n🎉 Basic credentials restored successfully!');
    console.log('\n🔑 Login Credentials:');
    console.log('   👑 Admin:');
    console.log('      Username: ADM001');
    console.log('      Password: admin123');
    console.log('');
    console.log('   👤 HR Manager:');
    console.log('      Username: HR001');
    console.log('      Password: hr123');
    console.log('');
    console.log('🚀 You can now login and start testing with real data!');

  } catch (error) {
    console.error('❌ Error restoring credentials:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await restoreCredentials();
  } catch (error) {
    console.error('❌ Restore failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the restore
main();
