import { Request, Response, NextFunction } from "express";
import { Error as MongooseError } from "mongoose";
import { isDevelopment } from "../../config/environment";
import { ApiResponse } from "../types/common";

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  code?: string | number;
  path?: string;
  value?: any;
  keyValue?: Record<string, any>;
  errors?: Record<string, any>;
}

/**
 * Handle Mongoose CastError
 */
const handleCastErrorDB = (err: MongooseError.CastError): AppError => {
  const message = `Data tidak valid untuk field ${err.path}: ${err.value}`;
  const error = new Error(message) as AppError;
  error.statusCode = 400;
  error.isOperational = true;
  return error;
};

/**
 * Handle Mongoose Duplicate Key Error
 */
const handleDuplicateFieldsDB = (err: any): AppError => {
  const field = Object.keys(err.keyValue)[0];
  const value = err.keyValue[field];
  const message = `Data ${field} '${value}' sudah digunakan. Gunakan data yang lain.`;

  const error = new Error(message) as AppError;
  error.statusCode = 400;
  error.isOperational = true;
  return error;
};

/**
 * Handle Mongoose Validation Error
 */
const handleValidationErrorDB = (
  err: MongooseError.ValidationError
): AppError => {
  const errors = Object.values(err.errors).map((el) => el.message);
  const message = `Data tidak valid: ${errors.join(". ")}`;

  const error = new Error(message) as AppError;
  error.statusCode = 400;
  error.isOperational = true;
  return error;
};

/**
 * Handle JWT Error
 */
const handleJWTError = (): AppError => {
  const error = new Error(
    "Token tidak valid. Silakan login ulang."
  ) as AppError;
  error.statusCode = 401;
  error.isOperational = true;
  return error;
};

/**
 * Handle JWT Expired Error
 */
const handleJWTExpiredError = (): AppError => {
  const error = new Error(
    "Token telah kedaluwarsa. Silakan login ulang."
  ) as AppError;
  error.statusCode = 401;
  error.isOperational = true;
  return error;
};

/**
 * Send error response for development
 */
const sendErrorDev = (err: AppError, res: Response): void => {
  res.status(err.statusCode || 500).json({
    success: false,
    error: err.name || "DEVELOPMENT_ERROR",
    message: err.message,
    data: {
      stack: err.stack,
      details: err,
    },
    timestamp: new Date(),
  } as ApiResponse);
};

/**
 * Send error response for production
 */
const sendErrorProd = (err: AppError, res: Response): void => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    res.status(err.statusCode || 500).json({
      success: false,
      message: err.message,
      error: err.code || "OPERATIONAL_ERROR",
      timestamp: new Date(),
    } as ApiResponse);
  } else {
    // Programming or other unknown error: don't leak error details
    console.error("ERROR 💥", err);

    res.status(500).json({
      success: false,
      message: "Terjadi kesalahan pada server",
      error: "INTERNAL_SERVER_ERROR",
      timestamp: new Date(),
    } as ApiResponse);
  }
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  err: AppError,
  _req: Request,
  res: Response,
  _next: NextFunction
): void => {
  err.statusCode = err.statusCode || 500;
  err.isOperational = err.isOperational || false;

  if (isDevelopment) {
    sendErrorDev(err, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    // Handle specific error types
    if (error.name === "CastError") {
      error = handleCastErrorDB(error as MongooseError.CastError);
    }

    if (error.code === 11000) {
      error = handleDuplicateFieldsDB(error);
    }

    if (error.name === "ValidationError") {
      error = handleValidationErrorDB(error as MongooseError.ValidationError);
    }

    if (error.name === "JsonWebTokenError") {
      error = handleJWTError();
    }

    if (error.name === "TokenExpiredError") {
      error = handleJWTExpiredError();
    }

    sendErrorProd(error, res);
  }
};

/**
 * Async error wrapper
 */
export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req, res, next).catch(next);
  };
};

/**
 * Create operational error
 */
export const createError = (
  message: string,
  statusCode: number = 500,
  code?: string
): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.isOperational = true;
  if (code) {
    error.code = code;
  }
  return error;
};
