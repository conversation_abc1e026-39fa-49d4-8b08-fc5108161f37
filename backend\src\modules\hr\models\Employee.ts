import mongoose, { Schema, Document } from "mongoose";

export interface IEmployee extends Document {
  // Personal Information
  personal: {
    employeeId: string;
    fullName: string;
    gender: "<PERSON><PERSON>-laki" | "Perempuan";
    placeOfBirth: string;
    dateOfBirth: Date;
    email: string;
    phone: string;
    profilePhoto?: string;

    // Identification
    religion:
      | "Islam"
      | "Kristen"
      | "Katolik"
      | "Hindu"
      | "Buddha"
      | "Konghucu"
      | "Lainnya";
    bloodType: "A" | "B" | "AB" | "O";
    familyCardNumber: string; // KK
    idCardNumber: string; // KTP
    taxNumber?: string; // NPWP
    bpjsTkNumber?: string;
    nikKkNumber?: string;
    taxStatus:
      | "TK/0"
      | "TK/1"
      | "TK/2"
      | "TK/3"
      | "K/0"
      | "K/1"
      | "K/2"
      | "K/3";

    // Address - Domisili
    currentAddress: {
      street: string;
      city: string;
      province: string;
    };

    // Address - KTP
    idCardAddress: {
      street: string;
      city: string;
      province: string;
    };

    // Contact Information
    contact: {
      mobilePhone1: string;
      mobilePhone2?: string;
      homePhone1?: string;
      homePhone2?: string;
    };

    // Marital Status and Children
    maritalInfo: {
      status: "Belum Menikah" | "Menikah" | "Cerai" | "Janda/Duda";
      spouseName?: string;
      spouseJob?: string;
      numberOfChildren: number;
    };

    // Bank Account
    bankAccount: {
      accountNumber: string;
      accountHolder: string;
      bankName: string;
    };
  };

  // HR Information
  hr: {
    division: mongoose.Types.ObjectId;
    department: mongoose.Types.ObjectId;
    position: mongoose.Types.ObjectId;
    tags?: mongoose.Types.ObjectId[];
    companyEmail?: string; // Email perusahaan

    // Contract Information
    contract: {
      employmentType: mongoose.Types.ObjectId;
      hireDate: Date;
      contractDate?: Date;
      contractEndDate?: Date;
      permanentDate?: Date;
    };

    // Education
    education: {
      certificateLevel:
        | "SD"
        | "SMP"
        | "SMA"
        | "SMK"
        | "D1"
        | "D2"
        | "D3"
        | "S1"
        | "S2"
        | "S3";
      fieldOfStudy: string;
      schoolName: string;
      schoolCity: string;
      graduationStatus?: "Lulus" | "Tidak Lulus" | "Sedang Belajar";
      description?: string;
    };

    // Rank and Grade
    rank: {
      rankCategory?: mongoose.Types.ObjectId;
      rankGrade?: mongoose.Types.ObjectId;
      rankSubgrade?: mongoose.Types.ObjectId;
      pensionFundNumber?: string;
    };

    // Emergency Contact
    emergency: {
      contactName: string;
      contactPhone: string;
      contactPhone2?: string;
      relationship: string;
      address: string;
    };

    // POO/POH
    location: {
      pointOfOrigin: string; // POO
      pointOfHire: string; // POH
    };

    // Uniform and Work Shoes
    uniform: {
      workUniformSize: "XS" | "S" | "M" | "L" | "XL" | "XXL" | "XXXL";
      workShoesSize: string;
    };

    // Salary (keeping existing structure)
    salary: {
      basic: number;
      allowances: {
        transport: number;
        meal: number;
        communication: number;
        position: number;
        other: number;
      };
      total?: number;
    };

    workSchedule: "Regular" | "Shift" | "Flexible" | "Remote" | "Part Time";
    manager?: mongoose.Types.ObjectId; // Manager reference
    directSupervisor?: mongoose.Types.ObjectId; // Atasan Langsung reference
  };

  // Family Information
  family: {
    // Spouse and Children
    spouse: {
      name?: string;
      dateOfBirth?: Date;
      marriageDate?: Date;
      lastEducation?: string;
      occupation?: string;
      numberOfChildren: number;
    };

    // Children Identity (up to 4 children)
    children: Array<{
      name?: string;
      gender?: "Laki-laki" | "Perempuan";
      dateOfBirth?: Date;
    }>;

    // Parents Information
    parents: {
      father: {
        name?: string;
        dateOfBirth?: Date;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      };
      mother: {
        name?: string;
        dateOfBirth?: Date;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      };
    };

    // Siblings Information
    siblings: {
      childOrder: number; // anak ke-
      totalSiblings: number; // jumlah saudara kandung
      siblingsData: Array<{
        name?: string;
        gender?: "Laki-laki" | "Perempuan";
        dateOfBirth?: Date;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      }>;
    };

    // In-laws Information
    inLaws: {
      fatherInLaw?: {
        name?: string;
        dateOfBirth?: Date;
        lastEducation?: string;
        description?: string;
      };
      motherInLaw?: {
        name?: string;
        dateOfBirth?: Date;
        lastEducation?: string;
        description?: string;
      };
    };
  };

  // System fields
  status:
    | "Aktif"
    | "Probation"
    | "Cuti"
    | "Tidak Aktif"
    | "Notice Period"
    | "Resign"
    | "Terminated"
    | "Pension"
    | "Kontrak Habis";
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}

const employeeSchema = new Schema<IEmployee>(
  {
    // Personal Information
    personal: {
      employeeId: {
        type: String,
        required: [true, "Employee ID is required"],
        unique: true,
        trim: true,
        uppercase: true,
      },
      fullName: {
        type: String,
        required: [true, "Full name is required"],
        trim: true,
        maxlength: [100, "Full name cannot exceed 100 characters"],
      },
      gender: {
        type: String,
        enum: ["Laki-laki", "Perempuan"],
        required: [true, "Gender is required"],
      },
      placeOfBirth: {
        type: String,
        required: [true, "Place of birth is required"],
        trim: true,
      },
      dateOfBirth: {
        type: Date,
        required: [true, "Date of birth is required"],
      },
      email: {
        type: String,
        required: false, // Email tidak wajib diisi
        unique: true,
        sparse: true, // Allow multiple null/undefined values for unique field
        lowercase: true,
        trim: true,
        validate: {
          validator: function (v: string) {
            // Only validate if email is provided
            return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
          },
          message: "Email format is invalid",
        },
      },
      phone: {
        type: String,
        required: [true, "Phone number is required"],
        trim: true,
      },
      profilePhoto: String,

      // Identification
      religion: {
        type: String,
        enum: [
          "Islam",
          "Kristen",
          "Katolik",
          "Hindu",
          "Buddha",
          "Konghucu",
          "Lainnya",
        ],
        required: [true, "Religion is required"],
      },
      bloodType: {
        type: String,
        enum: ["A", "B", "AB", "O"],
        required: [true, "Blood type is required"],
      },
      familyCardNumber: {
        type: String,
        required: [true, "Family card number is required"],
        trim: true,
      },
      idCardNumber: {
        type: String,
        required: [true, "ID card number is required"],
        trim: true,
      },
      taxNumber: {
        type: String,
        trim: true,
      },
      bpjsTkNumber: {
        type: String,
        trim: true,
      },
      nikKkNumber: {
        type: String,
        trim: true,
      },
      taxStatus: {
        type: String,
        enum: ["TK/0", "TK/1", "TK/2", "TK/3", "K/0", "K/1", "K/2", "K/3"],
        required: [true, "Tax status is required"],
      },

      // Address - Domisili
      currentAddress: {
        street: {
          type: String,
          required: false,
        },
        city: {
          type: String,
          required: false,
        },
        province: {
          type: String,
          required: false,
        },
      },

      // Address - KTP
      idCardAddress: {
        street: {
          type: String,
          required: false,
        },
        city: {
          type: String,
          required: false,
        },
        province: {
          type: String,
          required: false,
        },
      },

      // Contact Information
      contact: {
        mobilePhone1: {
          type: String,
          required: false,
        },
        mobilePhone2: String,
        homePhone1: String,
        homePhone2: String,
      },

      // Marital Status and Children
      maritalInfo: {
        status: {
          type: String,
          enum: ["Belum Menikah", "Menikah", "Cerai", "Janda/Duda"],
          required: [true, "Marital status is required"],
        },
        spouseName: String,
        spouseJob: String,
        numberOfChildren: {
          type: Number,
          default: 0,
          min: [0, "Number of children cannot be negative"],
        },
      },

      // Bank Account
      bankAccount: {
        accountNumber: {
          type: String,
          required: false,
        },
        accountHolder: {
          type: String,
          required: false,
        },
        bankName: {
          type: String,
          required: false,
        },
      },
    },

    // HR Information
    hr: {
      division: {
        type: Schema.Types.ObjectId,
        ref: "Division",
        required: [true, "Division is required"],
      },
      department: {
        type: Schema.Types.ObjectId,
        ref: "Department",
        required: [true, "Department is required"],
      },
      position: {
        type: Schema.Types.ObjectId,
        ref: "Position",
        required: [true, "Position is required"],
      },
      tags: [
        {
          type: Schema.Types.ObjectId,
          ref: "Tag",
        },
      ],
      companyEmail: {
        type: String,
        required: false,
        unique: true,
        sparse: true, // Allow multiple null/undefined values for unique field
        lowercase: true,
        trim: true,
        validate: {
          validator: function (v: string) {
            // Only validate if email is provided
            return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
          },
          message: "Company email format is invalid",
        },
      },

      // Contract Information
      contract: {
        employmentType: {
          type: Schema.Types.ObjectId,
          ref: "EmploymentType",
          required: [true, "Employment type is required"],
        },
        hireDate: {
          type: Date,
          required: [true, "Hire date is required"],
        },
        contractDate: Date,
        contractEndDate: Date,
        permanentDate: Date,
      },

      // Education
      education: {
        certificateLevel: {
          type: String,
          enum: ["SD", "SMP", "SMA", "SMK", "D1", "D2", "D3", "S1", "S2", "S3"],
          required: false,
        },
        fieldOfStudy: {
          type: String,
          required: false,
        },
        schoolName: {
          type: String,
          required: false,
        },
        schoolCity: {
          type: String,
          required: false,
        },
        graduationStatus: {
          type: String,
          enum: ["Lulus", "Tidak Lulus", "Sedang Belajar"],
          required: false,
        },
        description: String,
      },

      // Rank and Grade
      rank: {
        rankCategory: {
          type: Schema.Types.ObjectId,
          ref: "RankCategory",
        },
        rankGrade: {
          type: Schema.Types.ObjectId,
          ref: "RankGrade",
        },
        rankSubgrade: {
          type: Schema.Types.ObjectId,
          ref: "RankSubgrade",
        },
        pensionFundNumber: String,
      },

      // Emergency Contact
      emergency: {
        contactName: {
          type: String,
          required: false,
        },
        contactPhone: {
          type: String,
          required: false,
        },
        contactPhone2: {
          type: String,
          required: false,
        },
        relationship: {
          type: String,
          required: [true, "Emergency contact relationship is required"],
        },
        address: {
          type: String,
          required: [true, "Emergency contact address is required"],
        },
      },

      // POO/POH
      location: {
        pointOfOrigin: {
          type: String,
          required: [true, "Point of origin is required"],
        },
        pointOfHire: {
          type: String,
          required: [true, "Point of hire is required"],
        },
      },

      // Uniform and Work Shoes
      uniform: {
        workUniformSize: {
          type: String,
          enum: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
          required: [true, "Work uniform size is required"],
        },
        workShoesSize: {
          type: String,
          required: [true, "Work shoes size is required"],
        },
      },

      // Salary (keeping existing structure)
      salary: {
        basic: {
          type: Number,
          required: false, // Gaji pokok tidak wajib diisi
          min: [0, "Basic salary cannot be negative"],
          default: 0,
        },
        allowances: {
          transport: {
            type: Number,
            default: 0,
            min: [0, "Transport allowance cannot be negative"],
          },
          meal: {
            type: Number,
            default: 0,
            min: [0, "Meal allowance cannot be negative"],
          },
          communication: {
            type: Number,
            default: 0,
            min: [0, "Communication allowance cannot be negative"],
          },
          position: {
            type: Number,
            default: 0,
            min: [0, "Position allowance cannot be negative"],
          },
          other: {
            type: Number,
            default: 0,
            min: [0, "Other allowance cannot be negative"],
          },
        },
        total: Number,
      },

      workSchedule: {
        type: String,
        enum: ["Regular", "Shift", "Flexible", "Remote", "Part Time"],
        default: "Regular",
      },
      manager: {
        type: Schema.Types.ObjectId,
        ref: "Employee",
      },
      directSupervisor: {
        type: Schema.Types.ObjectId,
        ref: "Employee",
      },
    },

    // Family Information
    family: {
      // Spouse and Children
      spouse: {
        name: String,
        dateOfBirth: Date,
        marriageDate: Date,
        lastEducation: String,
        occupation: String,
        numberOfChildren: {
          type: Number,
          default: 0,
          min: [0, "Number of children cannot be negative"],
        },
      },

      // Children Identity (up to 4 children)
      children: [
        {
          name: String,
          gender: {
            type: String,
            enum: ["Laki-laki", "Perempuan"],
          },
          dateOfBirth: Date,
        },
      ],

      // Parents Information
      parents: {
        father: {
          name: String,
          dateOfBirth: Date,
          lastEducation: String,
          occupation: String,
          description: String,
        },
        mother: {
          name: String,
          dateOfBirth: Date,
          lastEducation: String,
          occupation: String,
          description: String,
        },
      },

      // Siblings Information
      siblings: {
        childOrder: {
          type: Number,
          min: [1, "Child order must be at least 1"],
        },
        totalSiblings: {
          type: Number,
          default: 0,
          min: [0, "Total siblings cannot be negative"],
        },
        siblingsData: [
          {
            name: String,
            gender: {
              type: String,
              enum: ["Laki-laki", "Perempuan"],
            },
            dateOfBirth: Date,
            lastEducation: String,
            occupation: String,
            description: String,
          },
        ],
      },

      // In-laws Information
      inLaws: {
        fatherInLaw: {
          name: String,
          dateOfBirth: Date,
          lastEducation: String,
          description: String,
        },
        motherInLaw: {
          name: String,
          dateOfBirth: Date,
          lastEducation: String,
          description: String,
        },
      },
    },

    // System fields
    status: {
      type: String,
      enum: [
        "Aktif",
        "Probation",
        "Cuti",
        "Tidak Aktif",
        "Notice Period",
        "Resign",
        "Terminated",
        "Pension",
        "Kontrak Habis",
      ],
      default: "Aktif",
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Pre-save middleware to calculate total salary
employeeSchema.pre("save", function (next) {
  // Calculate total salary
  if (this.hr.salary) {
    const allowances = this.hr.salary.allowances;
    this.hr.salary.total =
      this.hr.salary.basic +
      (allowances.transport || 0) +
      (allowances.meal || 0) +
      (allowances.communication || 0) +
      (allowances.position || 0) +
      (allowances.other || 0);
  }

  next();
});

// Indexes for performance
employeeSchema.index({ "personal.employeeId": 1 });
employeeSchema.index({ "personal.email": 1 });
employeeSchema.index({ "hr.companyEmail": 1 });
employeeSchema.index({ "personal.fullName": 1 });
employeeSchema.index({ "hr.department": 1 });
employeeSchema.index({ "hr.position": 1 });
employeeSchema.index({ status: 1 });
employeeSchema.index({ isActive: 1 });
employeeSchema.index({ createdAt: -1 });

export const Employee = mongoose.model<IEmployee>("Employee", employeeSchema);
