@echo off
setlocal enabledelayedexpansion

:: Simple BIS Service Checker and Manager
set BACKEND_PORT=5000
set FRONTEND_PORT=3000

echo.
echo 🔧 BIS Service Manager
echo ======================

if "%1"=="start" goto :start_services
if "%1"=="stop" goto :stop_services  
if "%1"=="restart" goto :restart_services
if "%1"=="kill" goto :kill_all
if "%1"=="status" goto :show_status
if "%1"=="" goto :show_status

echo ❌ Parameter tidak valid: %1
echo Gunakan: start, stop, restart, kill, atau status
goto :eof

:show_status
echo.
echo 📊 STATUS SERVICE BIS
echo =====================

:: Check Backend
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":%BACKEND_PORT% " ^| findstr "LISTENING"') do (
    set BACKEND_PID=%%a
    echo 🟢 Backend: RUNNING ^(PID: !BACKEND_PID!, Port: %BACKEND_PORT%^)
    goto :check_frontend
)
echo 🔴 Backend: STOPPED ^(Port: %BACKEND_PORT%^)

:check_frontend
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":%FRONTEND_PORT% " ^| findstr "LISTENING"') do (
    set FRONTEND_PID=%%a
    echo 🟢 Frontend: RUNNING ^(PID: !FRONTEND_PID!, Port: %FRONTEND_PORT%^)
    goto :end_status
)
echo 🔴 Frontend: STOPPED ^(Port: %FRONTEND_PORT%^)

:end_status
echo.
goto :eof

:stop_services
echo.
echo 🔴 Menghentikan services...

:: Stop Backend
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":%BACKEND_PORT% " ^| findstr "LISTENING"') do (
    echo 🔴 Menghentikan Backend ^(PID: %%a^)...
    taskkill /F /PID %%a 2>nul
)

:: Stop Frontend  
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":%FRONTEND_PORT% " ^| findstr "LISTENING"') do (
    echo 🔴 Menghentikan Frontend ^(PID: %%a^)...
    taskkill /F /PID %%a 2>nul
)

timeout /t 2 /nobreak >nul
goto :show_status

:start_services
echo.
echo 🚀 Memulai services...

:: Check dan start Backend
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":%BACKEND_PORT% " ^| findstr "LISTENING"') do (
    echo ⚠️  Backend sudah berjalan di port %BACKEND_PORT%
    goto :start_frontend_check
)

echo 🚀 Memulai Backend Service...
cd backend
start "BIS Backend" cmd /k "npm run dev"
cd ..
echo ⏳ Menunggu backend service siap...
timeout /t 5 /nobreak >nul

:start_frontend_check
:: Check dan start Frontend
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":%FRONTEND_PORT% " ^| findstr "LISTENING"') do (
    echo ⚠️  Frontend sudah berjalan di port %FRONTEND_PORT%
    goto :start_done
)

echo 🚀 Memulai Frontend Service...
cd frontend  
start "BIS Frontend" cmd /k "npm run dev"
cd ..
echo ⏳ Menunggu frontend service siap...
timeout /t 8 /nobreak >nul

:start_done
goto :show_status

:restart_services
echo.
echo 🔄 Restart services...
call :stop_services
timeout /t 3 /nobreak >nul
call :start_services
goto :eof

:kill_all
echo.
echo 💀 Menghentikan semua Node.js processes...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM "next-server" 2>nul

:: Kill specific ports
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":3000 " ^| findstr "LISTENING"') do (
    taskkill /F /PID %%a 2>nul
)
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":3001 " ^| findstr "LISTENING"') do (
    taskkill /F /PID %%a 2>nul
)
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":5001 " ^| findstr "LISTENING"') do (
    taskkill /F /PID %%a 2>nul
)

echo ✅ Semua Node.js processes dihentikan
timeout /t 2 /nobreak >nul
goto :show_status
