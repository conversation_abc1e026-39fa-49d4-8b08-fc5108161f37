# Project Brief - Bebang Information System (BIS)

## Project Overview
**<PERSON><PERSON>**: Bebang Information System (BIS)  
**Perusahaan**: PT. Prima Sarana Gemilang - Site Taliabu  
**Database**: MongoDB (nama database: `psg-sisinfo`)  
**Bahasa Aplikasi**: Indonesia  
**Target Deployment**: Server Windows lokal (bisa scale up ke cloud)  

## Core Requirements

### Technology Stack
- **Frontend**: Next.js (React-based)
- **Backend**: Node.js/Express (terpisah dari frontend)
- **Database**: MongoDB
- **Architecture**: Modular, backend dan frontend terpisah
- **Language**: Bahasa Indonesia

### Key Features
- Dukungan QR Code
- Upload dan tampilan foto/dokumen
- UI profesional, modern, clean, dan cocok untuk penggunaan korporat
- Fitur **Chatting Internal** seperti Slack atau Odoo Discuss
- System approval dan audit trail
- Role-based Access Control (RBAC)

### Project Structure
```
project/
├── backend/
│   ├── modules/
│   │   ├── hr/
│   │   ├── inventory/
│   │   ├── mess/
│   │   ├── building/
│   │   ├── user-access/
│   │   └── chatting/
│   └── shared/
└── frontend/
    ├── modules/
    └── shared/
```

## Application Flow
1. **Login Page** - Authentikasi pengguna
2. **Welcome Page** - Dashboard dengan shortcut ke modul
3. **Modules** - Modul berdiri sendiri dengan navigasi independen

## Core Modules

### 1. Human Resources Management
- Data Karyawan, Mutasi, Cuti, Pelanggaran
- Dokumen Personal, Riwayat Aset, Alokasi Tempat Tinggal

### 2. Inventory Management  
- Pergerakan dan monitoring aset/barang
- Barang Masuk/Keluar, Transfer, QR Code

### 3. Mess Management
- Tempat tinggal karyawan, fasilitas mess
- Alokasi Kamar, Check-in/Check-out

### 4. Building Management
- Fasilitas dan aset gedung/kantor
- Pemetaan Aset, Monitoring Ruangan

### 5. User Access Management
- Role-based Access Control
- Dynamic Menu berdasarkan role

### 6. Chatting / Internal Communication
- Chat 1-on-1, Group Chat/Channel
- Real-time notifications, File attachments

## Success Criteria
- Modular architecture dengan modul independen
- UI responsif dan modern
- Konsistensi style di seluruh aplikasi
- Integrasi antar modul yang seamless
- Performance optimal untuk deployment Windows lokal
- Scalability untuk future cloud deployment
