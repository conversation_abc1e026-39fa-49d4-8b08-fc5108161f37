import { Request, Response } from "express";
import RankCategory from "../models/RankCategory";

export const getRankCategories = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { search, status } = req.query;

    // Build filter - show all records (active and inactive)
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const rankCategories = await RankCategory.find(filter).sort({
      createdAt: -1,
    });

    res.json({
      success: true,
      data: rankCategories,
      message: "Rank categories retrieved successfully",
      count: rankCategories.length,
    });
  } catch (error) {
    console.error("Error fetching rank categories:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const getRankCategoryById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const rankCategory = await RankCategory.findById(id);

    if (!rankCategory) {
      res.status(404).json({
        success: false,
        message: "Rank category not found",
      });
      return;
    }

    res.json({
      success: true,
      data: rankCategory,
      message: "Rank category retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching rank category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const createRankCategory = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { name, description, isActive = true } = req.body;

    // Check if rank category already exists (only check active ones)
    const existingRankCategory = await RankCategory.findOne({
      name,
      isActive: true,
    });
    if (existingRankCategory) {
      res.status(400).json({
        success: false,
        message: "Rank category already exists",
      });
      return;
    }

    const rankCategory = new RankCategory({
      name,
      description,
      isActive,
      // Skip createdBy/updatedBy for development
    });

    await rankCategory.save();

    res.status(201).json({
      success: true,
      data: rankCategory,
      message: "Rank category created successfully",
    });
  } catch (error) {
    console.error("Error creating rank category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const updateRankCategory = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description, isActive } = req.body;

    const rankCategory = await RankCategory.findById(id);
    if (!rankCategory) {
      res.status(404).json({
        success: false,
        message: "Rank category not found",
      });
      return;
    }

    // Update rank category
    rankCategory.name = name || rankCategory.name;
    rankCategory.description = description || rankCategory.description;
    rankCategory.isActive =
      isActive !== undefined ? isActive : rankCategory.isActive;
    // Skip updatedBy for development

    await rankCategory.save();

    res.json({
      success: true,
      data: rankCategory,
      message: "Rank category updated successfully",
    });
  } catch (error) {
    console.error("Error updating rank category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const deleteRankCategory = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const rankCategory = await RankCategory.findById(id);
    if (!rankCategory) {
      res.status(404).json({
        success: false,
        message: "Rank category not found",
      });
      return;
    }

    // Status-based delete (set isActive to false)
    await RankCategory.findByIdAndUpdate(id, { isActive: false });

    res.json({
      success: true,
      message: "Rank category deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting rank category:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
