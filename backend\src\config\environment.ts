import dotenv from "dotenv";

dotenv.config();

interface EnvironmentConfig {
  NODE_ENV: string;
  PORT: number;
  MONGODB_URI: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  JWT_REFRESH_SECRET: string;
  JWT_REFRESH_EXPIRES_IN: string;
  CORS_ORIGIN: string;
  UPLOAD_PATH: string;
  MAX_FILE_SIZE: number;
  ALLOWED_FILE_TYPES: string[];
  BCRYPT_ROUNDS: number;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  APP_NAME: string;
  APP_VERSION: string;
  COMPANY_NAME: string;
}

const getEnvironmentConfig = (): EnvironmentConfig => {
  const requiredEnvVars = ["JWT_SECRET", "MONGODB_URI"];

  // Check for required environment variables
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  return {
    NODE_ENV: process.env.NODE_ENV || "development",
    PORT: parseInt(process.env.PORT || "5000", 10),
    MONGODB_URI: process.env.MONGODB_URI!,
    JWT_SECRET: process.env.JWT_SECRET!,
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || "7d",
    JWT_REFRESH_SECRET:
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET!,
    JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || "30d",
    CORS_ORIGIN: process.env.CORS_ORIGIN || "http://localhost:3000",
    UPLOAD_PATH: process.env.UPLOAD_PATH || "./uploads",
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || "10485760", 10), // 10MB
    ALLOWED_FILE_TYPES: (
      process.env.ALLOWED_FILE_TYPES || "jpg,jpeg,png,pdf,doc,docx,xls,xlsx"
    ).split(","),
    BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || "12", 10),
    RATE_LIMIT_WINDOW_MS: parseInt(
      process.env.RATE_LIMIT_WINDOW_MS || "900000",
      10
    ), // 15 minutes
    RATE_LIMIT_MAX_REQUESTS: parseInt(
      process.env.RATE_LIMIT_MAX_REQUESTS || "100",
      10
    ),
    APP_NAME: process.env.APP_NAME || "Bebang Information System",
    APP_VERSION: process.env.APP_VERSION || "1.0.0",
    COMPANY_NAME: process.env.COMPANY_NAME || "PT. Prima Sarana Gemilang",
  };
};

export const config = getEnvironmentConfig();

export const isDevelopment = config.NODE_ENV === "development";
export const isProduction = config.NODE_ENV === "production";
export const isTest = config.NODE_ENV === "test";

export default config;
