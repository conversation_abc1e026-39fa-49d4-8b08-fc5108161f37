"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Search,
  Filter,
  X,
  Calendar,
  Building2,
  Users,
  Briefcase,
} from "lucide-react";

interface FilterState {
  search: string;
  department: string;
  position: string;
  employmentType: string;
  status: string;
  joinDateFrom: string;
  joinDateTo: string;
}

interface EmployeeFiltersProps {
  onFiltersChange: (filters: FilterState) => void;
  totalResults: number;
}

const EmployeeFilters: React.FC<EmployeeFiltersProps> = ({
  onFiltersChange,
  totalResults,
}) => {
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    department: "",
    position: "",
    employmentType: "",
    status: "",
    joinDateFrom: "",
    joinDateTo: "",
  });

  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const departments = [
    { value: "hr", label: "Human Resources" },
    { value: "it", label: "Information Technology" },
    { value: "finance", label: "Finance & Accounting" },
    { value: "operations", label: "Operations" },
    { value: "marketing", label: "Marketing & Sales" },
  ];

  const employmentTypes = [
    { value: "permanent", label: "Karyawan Tetap" },
    { value: "contract", label: "Karyawan Kontrak" },
    { value: "parttime", label: "Karyawan Paruh Waktu" },
    { value: "intern", label: "Magang" },
  ];

  const statuses = [
    { value: "Aktif", label: "Aktif" },
    { value: "Tidak Aktif", label: "Tidak Aktif" },
    { value: "Berhenti", label: "Berhenti" },
    { value: "Resign", label: "Resign" },
  ];

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    // Convert "all" to empty string for filtering logic
    const filterValue = value === "all" ? "" : value;
    const newFilters = { ...filters, [key]: filterValue };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    const emptyFilters: FilterState = {
      search: "",
      department: "",
      position: "",
      employmentType: "",
      status: "",
      joinDateFrom: "",
      joinDateTo: "",
    };
    setFilters(emptyFilters);
    onFiltersChange(emptyFilters);
    setIsFilterOpen(false);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter((value) => value !== "").length;
  };

  const getActiveFilterLabels = () => {
    const labels = [];

    if (filters.department && filters.department !== "all") {
      const dept = departments.find((d) => d.value === filters.department);
      labels.push({
        key: "department",
        label: dept?.label || filters.department,
      });
    }

    if (filters.employmentType && filters.employmentType !== "all") {
      const empType = employmentTypes.find(
        (e) => e.value === filters.employmentType
      );
      labels.push({
        key: "employmentType",
        label: empType?.label || filters.employmentType,
      });
    }

    if (filters.status && filters.status !== "all") {
      labels.push({ key: "status", label: filters.status });
    }

    if (filters.position) {
      labels.push({ key: "position", label: `Posisi: ${filters.position}` });
    }

    if (filters.joinDateFrom || filters.joinDateTo) {
      let dateLabel = "Tanggal Bergabung: ";
      if (filters.joinDateFrom && filters.joinDateTo) {
        dateLabel += `${filters.joinDateFrom} - ${filters.joinDateTo}`;
      } else if (filters.joinDateFrom) {
        dateLabel += `dari ${filters.joinDateFrom}`;
      } else {
        dateLabel += `sampai ${filters.joinDateTo}`;
      }
      labels.push({ key: "joinDate", label: dateLabel });
    }

    return labels;
  };

  const removeFilter = (key: string) => {
    if (key === "joinDate") {
      handleFilterChange("joinDateFrom", "");
      handleFilterChange("joinDateTo", "");
    } else {
      handleFilterChange(key as keyof FilterState, "");
    }
  };

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            {/* Search Input */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari karyawan berdasarkan nama, ID, email..."
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filter Button */}
            <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="relative">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                  {getActiveFiltersCount() > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center text-xs"
                    >
                      {getActiveFiltersCount()}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-96 p-6" align="end">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Filter Karyawan</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearAllFilters}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Hapus Semua
                    </Button>
                  </div>

                  {/* Department Filter */}
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <Building2 className="w-4 h-4" />
                      <span>Departemen</span>
                    </Label>
                    <Select
                      value={filters.department || "all"}
                      onValueChange={(value) =>
                        handleFilterChange("department", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih departemen" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Departemen</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept.value} value={dept.value}>
                            {dept.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Position Filter */}
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <Briefcase className="w-4 h-4" />
                      <span>Posisi</span>
                    </Label>
                    <Input
                      placeholder="Cari posisi..."
                      value={filters.position}
                      onChange={(e) =>
                        handleFilterChange("position", e.target.value)
                      }
                    />
                  </div>

                  {/* Employment Type Filter */}
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>Jenis Kepegawaian</span>
                    </Label>
                    <Select
                      value={filters.employmentType || "all"}
                      onValueChange={(value) =>
                        handleFilterChange("employmentType", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih jenis kepegawaian" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Jenis</SelectItem>
                        {employmentTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Status Filter */}
                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select
                      value={filters.status || "all"}
                      onValueChange={(value) =>
                        handleFilterChange("status", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Status</SelectItem>
                        {statuses.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Join Date Range Filter */}
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Tanggal Bergabung</span>
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label className="text-xs text-gray-500">Dari</Label>
                        <Input
                          type="date"
                          value={filters.joinDateFrom}
                          onChange={(e) =>
                            handleFilterChange("joinDateFrom", e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-gray-500">Sampai</Label>
                        <Input
                          type="date"
                          value={filters.joinDateTo}
                          onChange={(e) =>
                            handleFilterChange("joinDateTo", e.target.value)
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4 border-t">
                    <Button
                      variant="outline"
                      onClick={() => setIsFilterOpen(false)}
                    >
                      Tutup
                    </Button>
                    <Button onClick={() => setIsFilterOpen(false)}>
                      Terapkan Filter
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* Results Count */}
            <Badge variant="secondary" className="whitespace-nowrap">
              {totalResults} karyawan
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Active Filters */}
      {getActiveFilterLabels().length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-600">
                Filter aktif:
              </span>
              {getActiveFilterLabels().map((filter, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="flex items-center space-x-1"
                >
                  <span>{filter.label}</span>
                  <button
                    onClick={() => removeFilter(filter.key)}
                    className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-red-600 hover:text-red-700 h-6 px-2"
              >
                Hapus Semua
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EmployeeFilters;
