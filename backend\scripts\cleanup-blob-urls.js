const mongoose = require("mongoose");

// Connect to MongoDB
mongoose.connect("mongodb://localhost:27017/psg-sisinfo");

const employeeSchema = new mongoose.Schema({}, { strict: false });
const Employee = mongoose.model("Employee", employeeSchema, "employees");

async function cleanupBlobUrls() {
  try {
    console.log("🔍 Searching for employees with blob URLs...");

    // Find employees with blob URLs in profilePhoto
    const employeesWithBlobUrls = await Employee.find({
      "personal.profilePhoto": { $regex: /^blob:/ },
    });

    console.log(
      `📊 Found ${employeesWithBlobUrls.length} employees with blob URLs`
    );

    if (employeesWithBlobUrls.length === 0) {
      console.log("✅ No blob URLs found. Database is clean!");
      return;
    }

    // List employees with blob URLs
    employeesWithBlobUrls.forEach((emp, index) => {
      console.log(
        `${index + 1}. ${emp.personal?.fullName || "Unknown"} (${
          emp.personal?.employeeId || "No ID"
        })`
      );
      console.log(
        `   Photo: ${emp.personal?.profilePhoto?.substring(0, 50)}...`
      );
    });

    console.log("\n🧹 Cleaning up blob URLs...");

    // Update employees to remove blob URLs
    const result = await Employee.updateMany(
      { "personal.profilePhoto": { $regex: /^blob:/ } },
      { $unset: { "personal.profilePhoto": "" } }
    );

    console.log(`✅ Cleanup completed!`);
    console.log(`📝 Updated ${result.modifiedCount} employees`);
    console.log(
      `💡 These employees will now show initials instead of broken images`
    );
  } catch (error) {
    console.error("❌ Error during cleanup:", error);
  } finally {
    mongoose.connection.close();
    console.log("🔌 Database connection closed");
  }
}

// Run cleanup
cleanupBlobUrls();
