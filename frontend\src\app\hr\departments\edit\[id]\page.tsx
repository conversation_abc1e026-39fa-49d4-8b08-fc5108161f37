"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { toast } from "sonner";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { useRouter, useParams } from "next/navigation";
import { cn } from "@/lib/utils";

interface Division {
  _id?: string;
  id: string;
  name: string;
  isActive: boolean;
}

const EditDepartmentPage = () => {
  const router = useRouter();
  const params = useParams();
  const departmentId = params.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [loadingDivisions, setLoadingDivisions] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    manager: "",
    description: "",
    divisionId: "",
    isActive: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [divisions, setDivisions] = useState<Division[]>([]);

  // Fetch department data and divisions
  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
      fetchDivisions();
    }
  }, [departmentId]);

  // Refresh divisions when page becomes visible (handles data sync after editing divisions)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && departmentId) {
        fetchDivisions(true); // Force refresh when page becomes visible
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Also refresh when window gets focus
    const handleFocus = () => {
      if (departmentId) {
        fetchDivisions(true); // Force refresh when window gets focus
      }
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
    };
  }, [departmentId]);

  const fetchDivisions = async (forceRefresh = false) => {
    try {
      setLoadingDivisions(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      // Add cache busting parameter for force refresh
      const url = forceRefresh
        ? `http://localhost:5000/api/hr/divisions?_t=${Date.now()}`
        : "http://localhost:5000/api/hr/divisions";

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          ...(forceRefresh && {
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          }),
        },
      });

      const result = await response.json();

      if (result.success) {
        // Filter hanya divisi yang aktif
        const activeDivisions = result.data.filter(
          (division: Division) => division.isActive === true
        );
        // console.log("Fetched divisions:", activeDivisions);
        setDivisions(activeDivisions);

        // Remove the annoying toast notification for refresh
      } else {
        toast.error("Gagal memuat data divisi", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error fetching divisions:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data divisi",
      });
    } finally {
      setLoadingDivisions(false);
    }
  };

  const fetchDepartment = async () => {
    try {
      setIsLoadingData(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/departments/${departmentId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success) {
        setFormData({
          name: result.data.name || "",
          manager: result.data.manager || "",
          description: result.data.description || "",
          divisionId: result.data.divisionId || "",
          isActive:
            result.data.isActive !== undefined ? result.data.isActive : true,
        });
      } else {
        toast.error("Gagal memuat data department", {
          description: result.message || "Terjadi kesalahan pada server",
        });
        router.push("/hr/departments");
      }
    } catch (error) {
      console.error("Error fetching department:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
      router.push("/hr/departments");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const handleSwitchChange = (field: string, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSelectChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user selects
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nama department wajib diisi";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Nama department minimal 2 karakter";
    }

    if (!formData.divisionId) {
      newErrors.divisionId = "Divisi wajib dipilih";
    }

    // Manager is optional - only validate if provided
    if (formData.manager.trim() && formData.manager.trim().length < 2) {
      newErrors.manager = "Nama manager minimal 2 karakter";
    }

    if (formData.manager.trim().length > 100) {
      newErrors.manager = "Nama manager maksimal 100 karakter";
    }

    if (formData.description.trim().length > 500) {
      newErrors.description = "Deskripsi maksimal 500 karakter";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Periksa kembali form yang Anda isi");
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/departments/${departmentId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Department berhasil diperbarui!", {
          description: `${formData.name} telah diperbarui`,
          action: {
            label: "Lihat",
            onClick: () => router.push("/hr/departments"),
          },
        });

        // Delay navigation to show toast
        setTimeout(() => {
          router.push("/hr/departments");
        }, 1000);
      } else {
        if (result.data?.errors) {
          // Handle validation errors from backend
          const backendErrors: Record<string, string> = {};
          result.data.errors.forEach((error: any) => {
            backendErrors[error.field] = error.message;
          });
          setErrors(backendErrors);

          toast.error("Data tidak valid", {
            description: "Periksa kembali form yang Anda isi",
          });
        } else {
          toast.error("Gagal memperbarui department", {
            description: result.message || "Terjadi kesalahan pada server",
          });
        }
      }
    } catch (error) {
      console.error("Error updating department:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Memuat data department...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/hr/departments")}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Departemen
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Departemen</h1>
          <p className="text-gray-600 mt-1">Perbarui informasi departemen</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Departemen</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Department Name */}
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Departemen <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Masukkan nama departemen"
                  className={cn("h-11", errors.name ? "border-red-500" : "")}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              {/* Division */}
              <div className="space-y-2">
                <Label>
                  Divisi <span className="text-red-500">*</span>
                </Label>
                <SearchableSelect
                  value={formData.divisionId}
                  onValueChange={(value) => {
                    handleSelectChange("divisionId", value);
                  }}
                  placeholder={
                    loadingDivisions ? "Memuat divisi..." : "Pilih divisi"
                  }
                  searchPlaceholder="Cari divisi..."
                  emptyMessage="Tidak ada divisi aktif ditemukan."
                  disabled={loadingDivisions}
                  className={errors.divisionId ? "border-red-500" : ""}
                  options={divisions.map((division) => ({
                    value: division.id || division._id || "",
                    label: division.name,
                  }))}
                />
                {errors.divisionId && (
                  <p className="text-sm text-red-500">{errors.divisionId}</p>
                )}
              </div>
            </div>

            {/* Manager - Full width */}
            <div className="space-y-2">
              <Label htmlFor="manager">Nama Manager</Label>
              <Input
                id="manager"
                value={formData.manager}
                onChange={(e) => handleInputChange("manager", e.target.value)}
                placeholder="Masukkan nama manager (opsional)"
                className={cn("h-11", errors.manager ? "border-red-500" : "")}
              />
              {errors.manager && (
                <p className="text-sm text-red-500">{errors.manager}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Keterangan</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Masukkan keterangan departemen (opsional)"
                rows={4}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <div className="flex items-center space-x-3">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("isActive", checked)
                  }
                />
                <span className="text-sm text-gray-700">
                  {formData.isActive ? "Aktif" : "Tidak Aktif"}
                </span>
              </div>
              <p className="text-xs text-gray-500">
                Departemen aktif akan muncul dalam pilihan saat membuat data
                karyawan
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/hr/departments")}
                disabled={isLoading}
              >
                Batal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Perbarui Departemen
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditDepartmentPage;
