import { Request, Response } from "express";
import Division from "../models/Division";
import { validationResult } from "express-validator";

// Get all divisions
export const getDivisions = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { search, status } = req.query;

    // Build filter (show all divisions, both active and inactive)
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const divisions = await Division.find(filter).sort({ createdAt: -1 });

    res.json({
      success: true,
      data: divisions,
      message: "Divisions retrieved successfully",
      count: divisions.length,
    });
  } catch (error) {
    console.error("Error fetching divisions:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Get division by ID
export const getDivisionById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const division = await Division.findById(id);

    if (!division) {
      res.status(404).json({
        success: false,
        message: "Division not found",
      });
      return;
    }

    res.json({
      success: true,
      data: division,
      message: "Division retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching division:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Create new division
export const createDivision = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: "Validation failed",
        data: {
          errors: errors.array().map((error: any) => ({
            field: error.param,
            message: error.msg,
          })),
        },
      });
      return;
    }

    const { name, description, isActive = true } = req.body;

    // Check if division with same name already exists (case-insensitive)
    const existingDivision = await Division.findOne({
      name: { $regex: new RegExp(`^${name}$`, "i") },
    });

    if (existingDivision) {
      res.status(400).json({
        success: false,
        message: "Division with this name already exists",
      });
      return;
    }

    // Create new division
    const division = new Division({
      name,
      description,
      isActive,
    });

    await division.save();

    res.status(201).json({
      success: true,
      data: division,
      message: "Division created successfully",
    });
  } catch (error) {
    console.error("Error creating division:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Update division
export const updateDivision = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: "Validation failed",
        data: {
          errors: errors.array().map((error: any) => ({
            field: error.param,
            message: error.msg,
          })),
        },
      });
      return;
    }

    const { id } = req.params;
    const { name, description, isActive } = req.body;

    // Check if division exists
    const division = await Division.findById(id);
    if (!division) {
      res.status(404).json({
        success: false,
        message: "Division not found",
      });
      return;
    }

    // Check if another division with same name exists (case-insensitive)
    if (name && name !== division.name) {
      const existingDivision = await Division.findOne({
        name: { $regex: new RegExp(`^${name}$`, "i") },
        _id: { $ne: id },
      });

      if (existingDivision) {
        res.status(400).json({
          success: false,
          message: "Division with this name already exists",
        });
        return;
      }
    }

    // Update division
    const updatedDivision = await Division.findByIdAndUpdate(
      id,
      {
        name: name || division.name,
        description:
          description !== undefined ? description : division.description,
        isActive: isActive !== undefined ? isActive : division.isActive,
      },
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      data: updatedDivision,
      message: "Division updated successfully",
    });
  } catch (error) {
    console.error("Error updating division:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Delete division (status-based delete)
export const deleteDivision = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const division = await Division.findById(id);
    if (!division) {
      res.status(404).json({
        success: false,
        message: "Division not found",
      });
      return;
    }

    // Status-based delete (set isActive to false)
    const updatedDivision = await Division.findByIdAndUpdate(
      id,
      { isActive: false },
      { new: true }
    );

    res.json({
      success: true,
      data: updatedDivision,
      message: "Division deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting division:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Get active divisions only
export const getActiveDivisions = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    const divisions = await Division.find({ isActive: true })
      .select("name description")
      .sort({ name: 1 });

    res.json({
      success: true,
      message: "Active divisions retrieved successfully",
      data: divisions,
      count: divisions.length,
    });
  } catch (error) {
    console.error("Error getting active divisions:", error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve active divisions",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
