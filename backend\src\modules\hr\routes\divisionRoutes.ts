import express from "express";
import { body } from "express-validator";
import {
  getDivisions,
  getDivisionById,
  createDivision,
  updateDivision,
  deleteDivision,
  getActiveDivisions,
} from "../controllers/divisionController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const divisionValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Nama divisi wajib diisi")
    .isLength({ min: 2, max: 100 })
    .withMessage("Nama divisi harus antara 2-100 karakter"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Keterangan maksimal 500 karakter"),
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("Status aktif harus berupa boolean"),
];

// Routes
router.get("/", devBypass, getDivisions);
router.get("/active", devBypass, getActiveDivisions);
router.get("/:id", devBypass, getDivisionById);
router.post("/", devBypass, divisionValidation, createDivision);
router.put("/:id", devBypass, divisionValidation, updateDivision);
router.delete("/:id", devBypass, deleteDivision);

export default router;
