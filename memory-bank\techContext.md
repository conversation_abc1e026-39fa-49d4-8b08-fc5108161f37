# Technical Context - Bebang Information System (BIS)

## Technology Stack

### Frontend

- **Framework**: Next.js 14+ (React-based)
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand atau React Query
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts atau Chart.js
- **QR Code**: qrcode.js + qr-scanner
- **File Upload**: react-dropzone
- **Real-time**: Socket.io-client

### Backend

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MongoDB dengan Mongoose ODM
- **Authentication**: JWT + bcrypt
- **File Storage**: Multer + local storage (scalable ke cloud)
- **Real-time**: Socket.io
- **Validation**: <PERSON><PERSON> atau Zod
- **API Documentation**: Swagger/OpenAPI

### Database

- **Primary**: MongoDB 6.0+
- **Database Name**: `psg-sisinfo`
- **ODM**: Mongoose untuk schema validation
- **Data Policy**: ✅ **NO STATIC DATA** - All data stored in database
- **Migration Status**: ✅ **COMPLETE** - All master data migrated to MongoDB
- **Persistence**: ✅ **VERIFIED** - Data survives backend restarts
- **Indexing**: Compound indexes untuk performance
- **Backup**: MongoDB Atlas backup atau mongodump

### Development Tools

- **Package Manager**: npm atau yarn
- **Code Quality**: ESLint + Prettier
- **Testing**: Jest + React Testing Library
- **Build**: Next.js build system
- **Development**: Concurrent development dengan nodemon

## Development Setup

### Project Structure

```
pt-psg-taliabu/
├── backend/
│   ├── src/
│   │   ├── modules/
│   │   │   ├── hr/
│   │   │   ├── inventory/
│   │   │   ├── mess/
│   │   │   ├── building/
│   │   │   ├── user-access/
│   │   │   └── chatting/
│   │   ├── shared/
│   │   │   ├── middleware/
│   │   │   ├── services/
│   │   │   ├── utils/
│   │   │   └── types/
│   │   ├── config/
│   │   └── app.ts
│   ├── uploads/
│   ├── package.json
│   └── tsconfig.json
├── frontend/
│   ├── src/
│   │   ├── app/
│   │   ├── components/
│   │   ├── modules/
│   │   ├── shared/
│   │   ├── lib/
│   │   └── types/
│   ├── public/
│   ├── package.json
│   └── next.config.js
└── memory-bank/
```

### Environment Configuration

```bash
# Backend (.env) - CURRENT WORKING CONFIGURATION
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/psg-sisinfo
JWT_SECRET=bebang-information-system-super-secret-key-2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=bebang-refresh-token-secret-key-2024
JWT_REFRESH_EXPIRES_IN=30d
CORS_ORIGIN=http://localhost:3000
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
APP_NAME=Bebang Information System
APP_VERSION=1.0.0
COMPANY_NAME=PT. Prima Sarana Gemilang

# Frontend (.env.local) - CURRENT WORKING CONFIGURATION
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_WS_URL=http://localhost:5000
NEXT_PUBLIC_APP_NAME=Bebang Information System
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_COMPANY_NAME=PT. Prima Sarana Gemilang
NEXT_PUBLIC_MAX_FILE_SIZE=********
NEXT_PUBLIC_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_LOG_LEVEL=info
NEXT_PUBLIC_ENABLE_CHAT=true
NEXT_PUBLIC_ENABLE_QR_SCANNER=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
```

## Technical Constraints

### Deployment Environment

- **Target**: Windows Server lokal
- **Node.js**: Version 18+ required
- **MongoDB**: Local installation atau MongoDB Atlas
- **Web Server**: IIS dengan iisnode atau standalone Node.js
- **File Storage**: Local file system (dapat di-upgrade ke cloud storage)

### Performance Requirements

- **Response Time**: < 2 detik untuk operasi normal
- **Concurrent Users**: Support 50-100 concurrent users
- **File Upload**: Max 10MB per file
- **Database**: Optimized queries dengan proper indexing

### Security Requirements

- **Authentication**: JWT dengan refresh token
- **Authorization**: Role-based access control
- **Data Validation**: Input sanitization dan validation
- **File Upload**: Type validation dan virus scanning
- **HTTPS**: SSL certificate untuk production

## Dependencies

### Backend Core Dependencies

```json
{
  "express": "^4.18.0",
  "mongoose": "^7.0.0",
  "jsonwebtoken": "^9.0.0",
  "bcryptjs": "^2.4.3",
  "multer": "^1.4.5",
  "socket.io": "^4.7.0",
  "joi": "^17.9.0",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "morgan": "^1.10.0"
}
```

### Frontend Core Dependencies

```json
{
  "next": "^14.0.0",
  "react": "^18.0.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^3.3.0",
  "@radix-ui/react-*": "latest",
  "zustand": "^4.4.0",
  "react-hook-form": "^7.45.0",
  "zod": "^3.22.0",
  "socket.io-client": "^4.7.0",
  "qrcode": "^1.5.3",
  "react-dropzone": "^14.2.0"
}
```

## Tool Usage Patterns

### Development Workflow

1. **Backend Development**:

   - Start MongoDB service
   - Run `npm run dev` untuk auto-reload dengan nodemon
   - API testing dengan Postman atau Thunder Client

2. **Frontend Development**:

   - Run `npm run dev` untuk Next.js development server
   - Hot reload untuk instant feedback
   - Component development dengan Storybook (optional)

3. **Database Management**:
   - MongoDB Compass untuk GUI management
   - Mongoose schema untuk data validation
   - Database seeding untuk development data

### Code Quality

- **Linting**: ESLint dengan TypeScript rules
- **Formatting**: Prettier untuk consistent code style
- **Type Safety**: Strict TypeScript configuration
- **Testing**: Unit tests untuk critical business logic

### Deployment Strategy

- **Development**: Local development dengan hot reload
- **Staging**: Docker containers untuk testing
- **Production**: Windows Server dengan PM2 process manager
- **Database**: MongoDB replica set untuk high availability
