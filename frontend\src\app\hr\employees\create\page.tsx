"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Import components
import CreateEmployeeHeader from "@/components/hr/CreateEmployeeHeader";
import CreateEmployeePersonalInfo from "@/components/hr/CreateEmployeePersonalInfo";
import CreateEmployeeHRInfo from "@/components/hr/CreateEmployeeHRInfo";
import CreateEmployeeFamilyInfo from "@/components/hr/CreateEmployeeFamilyInfo";

// Import services
import {
  employeeService,
  CreateEmployeeData,
} from "@/services/employeeService";
import { masterDataService } from "@/services/masterDataService";

export default function CreateEmployeePage() {
  const router = useRouter();

  // State management
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("personal");

  // Form data state
  const [formData, setFormData] = useState<CreateEmployeeData>({
    personal: {
      employeeId: "",
      fullName: "",
      gender: "Laki-laki",
      placeOfBirth: "",
      dateOfBirth: "",
      email: "",
      phone: "",

      // Identification
      religion: "Islam",
      bloodType: "A",
      familyCardNumber: "",
      idCardNumber: "",
      taxNumber: "",
      bpjsTkNumber: "",
      nikKkNumber: "",
      taxStatus: "TK/0",

      // Address - Domisili
      currentAddress: {
        street: "",
        city: "",
        province: "",
      },

      // Address - KTP
      idCardAddress: {
        street: "",
        city: "",
        province: "",
      },

      // Contact Information
      contact: {
        mobilePhone1: "",
        mobilePhone2: "",
        homePhone1: "",
        homePhone2: "",
      },

      // Marital Status and Children
      maritalInfo: {
        status: "Belum Menikah",
        spouseName: "",
        spouseJob: "",
        numberOfChildren: 0,
      },

      // Bank Account
      bankAccount: {
        accountNumber: "",
        accountHolder: "",
        bankName: "",
      },
    },
    hr: {
      // Employee Information
      position: "",
      division: "",
      department: "",
      companyEmail: "",
      manager: "",
      directSupervisor: "",
      status: "Aktif",
      tags: [],

      // Contract Information
      contract: {
        employmentType: "",
        hireDate: "",
        contractDate: "",
        contractEndDate: "",
        permanentDate: "",
        exitDate: "",
      },

      // Education
      education: {
        certificateLevel: "SMA",
        fieldOfStudy: "",
        schoolName: "",
        schoolCity: "",
        graduationStatus: "",
        description: "",
      },

      // Rank and Grade
      rank: {
        rankCategory: "",
        rankGrade: "",
        rankSubgrade: "",
        pensionFundNumber: "",
      },

      // Emergency Contact
      emergency: {
        contactName: "",
        contactPhone: "",
        relationship: "",
        address: "",
      },

      // POO/POH
      location: {
        pointOfOrigin: "",
        pointOfHire: "",
      },

      // Uniform and Work Shoes
      uniform: {
        workUniformSize: "M",
        workShoesSize: "",
      },

      // Salary
      salary: {
        basic: 0,
        allowances: {
          transport: 0,
          meal: 0,
          communication: 0,
          position: 0,
          other: 0,
        },
      },

      workSchedule: "Regular",
    },
    family: {
      // Spouse and Children
      spouse: {
        name: "",
        dateOfBirth: "",
        marriageDate: "",
        lastEducation: "",
        occupation: "",
        numberOfChildren: 0,
      },

      // Children Identity (up to 4 children)
      children: [],

      // Parents Information
      parents: {
        father: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          occupation: "",
          description: "",
        },
        mother: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          occupation: "",
          description: "",
        },
      },

      // Siblings Information
      siblings: {
        childOrder: 1,
        totalSiblings: 0,
        siblingsData: [],
      },

      // In-laws Information
      inLaws: {
        fatherInLaw: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          description: "",
        },
        motherInLaw: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          description: "",
        },
      },
    },
  });

  // Master data state
  const [masterData, setMasterData] = useState({
    divisions: [],
    departments: [],
    positions: [],
    tags: [],
    employmentTypes: [],
    rankCategories: [],
    rankGrades: [],
    rankSubgrades: [],
    employees: [], // For manager and direct supervisor selection
  });

  // Fetch master data from API
  const fetchMasterData = async () => {
    try {
      const masterData = await masterDataService.getAllMasterData();
      setMasterData(masterData);
    } catch (error) {
      console.error("Error fetching master data:", error);
      toast.error("Gagal memuat master data", {
        description: "Silakan coba lagi atau hubungi administrator",
      });

      // No fallback data - force user to fix the API issue
      setMasterData({
        divisions: [],
        departments: [],
        positions: [],
        tags: [],
        employmentTypes: [],
        rankCategories: [],
        rankGrades: [],
        rankSubgrades: [],
        employees: [],
      });
    }
  };

  // Load master data on component mount
  useEffect(() => {
    // Set mock token if not exists (for development)
    if (typeof window !== "undefined" && !localStorage.getItem("accessToken")) {
      localStorage.setItem("accessToken", "mock-token-for-development");
      console.log("Mock auth token set for development");
    }
    fetchMasterData();
  }, []);

  // Update form data - WITH PARTIAL UPDATE SUPPORT
  const updateFormData = (section: keyof CreateEmployeeData, data: any) => {
    setFormData((prev) => {
      const newFormData = {
        ...prev,
        [section]: { ...prev[section], ...data },
      };

      // PHONE SYNC - BIDIRECTIONAL SYNC LOGIC
      if (section === "personal") {
        // Sync from HP 1 (contact.mobilePhone1) to Nomor Handphone (phone)
        if (data.contact?.mobilePhone1 !== undefined) {
          newFormData.personal = {
            ...newFormData.personal,
            phone: data.contact.mobilePhone1,
          };
        }
        // Sync from Nomor Handphone (phone) to HP 1 (contact.mobilePhone1)
        if (data.phone !== undefined && !data.contact?.mobilePhone1) {
          newFormData.personal = {
            ...newFormData.personal,
            contact: {
              ...newFormData.personal.contact,
              mobilePhone1: data.phone,
            },
          };
        }

        // Synchronize spouse fields from personal to family
        if (data.maritalInfo) {
          const maritalInfo = data.maritalInfo;
          const updates: any = {};

          if (maritalInfo.spouseName !== undefined) {
            updates.name = maritalInfo.spouseName;
          }
          if (maritalInfo.spouseJob !== undefined) {
            updates.occupation = maritalInfo.spouseJob;
          }
          if (maritalInfo.numberOfChildren !== undefined) {
            updates.numberOfChildren = maritalInfo.numberOfChildren;
          }

          if (Object.keys(updates).length > 0) {
            newFormData.family = {
              ...newFormData.family,
              spouse: {
                ...newFormData.family.spouse,
                ...updates,
              },
            };
          }
        }
      }

      // Synchronize spouse fields from family to personal
      if (section === "family") {
        if (data.spouse) {
          const spouse = data.spouse;
          const updates: any = {};

          if (spouse.name !== undefined) {
            updates.spouseName = spouse.name;
          }
          if (spouse.occupation !== undefined) {
            updates.spouseJob = spouse.occupation;
          }
          if (spouse.numberOfChildren !== undefined) {
            updates.numberOfChildren = spouse.numberOfChildren;
          }

          if (Object.keys(updates).length > 0) {
            newFormData.personal = {
              ...newFormData.personal,
              maritalInfo: {
                ...newFormData.personal.maritalInfo,
                ...updates,
              },
            };
          }
        }
      }

      return newFormData;
    });
  };

  // Validate form data
  const validateForm = (): boolean => {
    // Basic validation for required fields
    if (!formData.personal.fullName.trim()) {
      toast.error("Nama lengkap harus diisi");
      // Header fields are always visible, no need to change tab
      return false;
    }

    if (!formData.personal.employeeId.trim()) {
      toast.error("Nomor induk karyawan harus diisi");
      // Header fields are always visible, no need to change tab
      return false;
    }

    // Email is optional - no validation needed

    if (!formData.personal.phone.trim()) {
      toast.error("Nomor handphone harus diisi");
      // Header fields are always visible, no need to change tab
      return false;
    }

    if (!formData.hr.division) {
      toast.error("Divisi harus dipilih");
      // Header fields are always visible, no need to change tab
      return false;
    }

    if (!formData.hr.department) {
      toast.error("Departemen harus dipilih");
      // Header fields are always visible, no need to change tab
      return false;
    }

    if (!formData.hr.position) {
      toast.error("Posisi jabatan harus dipilih");
      // Header fields are always visible, no need to change tab
      return false;
    }

    // Validate emergency contact address (required field)
    if (!formData.hr.emergency.address.trim()) {
      toast.error("Alamat kontak darurat harus diisi");
      setActiveTab("hr"); // Switch to HR tab
      return false;
    }

    // Add more validation as needed
    return true;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      await employeeService.createEmployee(formData);

      toast.success("Karyawan berhasil ditambahkan", {
        description: "Data karyawan telah disimpan ke sistem",
      });

      router.push("/hr/employees");
    } catch (error) {
      console.error("Error creating employee:", error);
      // Error handling is already done in the service
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4 max-w-6xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push("/hr/employees")}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Tambah Karyawan Baru</h1>
                <p className="text-gray-600">
                  Lengkapi informasi karyawan baru
                </p>
              </div>
            </div>

            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="min-w-[120px]"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Simpan
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 max-w-6xl">
        {/* Employee Header Section - Always Visible */}
        <div className="mb-6">
          <CreateEmployeeHeader
            data={formData}
            onUpdate={(data) => {
              updateFormData("personal", data.personal);
              updateFormData("hr", data.hr);
            }}
            divisions={masterData.divisions}
            departments={masterData.departments}
            positions={masterData.positions}
            tags={masterData.tags}
            employees={masterData.employees}
          />
        </div>

        {/* Tabs for additional information */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-3 bg-white">
            <TabsTrigger
              value="personal"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              Informasi Personal
            </TabsTrigger>
            <TabsTrigger
              value="hr"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              Informasi HR
            </TabsTrigger>
            <TabsTrigger
              value="family"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              Informasi Keluarga
            </TabsTrigger>
          </TabsList>

          <TabsContent value="personal">
            <CreateEmployeePersonalInfo
              data={formData.personal}
              onUpdate={(data) => updateFormData("personal", data)}
            />
          </TabsContent>

          <TabsContent value="hr">
            <CreateEmployeeHRInfo
              data={formData.hr}
              onUpdate={(data) => updateFormData("hr", data)}
              divisions={masterData.divisions}
              departments={masterData.departments}
              positions={masterData.positions}
              employmentTypes={masterData.employmentTypes}
              rankCategories={masterData.rankCategories}
              rankGrades={masterData.rankGrades}
              rankSubgrades={masterData.rankSubgrades}
              employees={masterData.employees}
            />
          </TabsContent>

          <TabsContent value="family">
            <CreateEmployeeFamilyInfo
              data={formData.family}
              onUpdate={(data) => updateFormData("family", data)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
