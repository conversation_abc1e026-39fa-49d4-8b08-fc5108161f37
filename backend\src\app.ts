import express, { Application, Request, Response } from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { config, isDevelopment } from "./config/environment";
import { connectDatabase } from "./config/database";

// Import routes
import authRoutes from "./modules/user-access/routes/authRoutes";
import hrRoutes from "./modules/hr/routes";

// Import models to register schemas
import "./modules/user-access/models/User";
import "./modules/user-access/models/Role";
import "./modules/hr/models";

// Import middleware
import { errorHandler } from "./shared/middleware/errorHandler";
import { notFoundHandler } from "./shared/middleware/notFoundHandler";

class App {
  public app: Application;

  constructor() {
    this.app = express();
    this.initializeDatabase();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await connectDatabase();
    } catch (error) {
      console.error("Failed to connect to database:", error);
      process.exit(1);
    }
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(
      helmet({
        crossOriginEmbedderPolicy: false,
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
          },
        },
      })
    );

    // CORS configuration
    this.app.use(
      cors({
        origin: isDevelopment
          ? [
              "http://localhost:3000",
              "http://localhost:3001",
              "http://localhost:3002",
              "http://localhost:3003",
              config.CORS_ORIGIN,
            ]
          : config.CORS_ORIGIN,
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allowedHeaders: [
          "Content-Type",
          "Authorization",
          "X-Requested-With",
          "Cache-Control",
          "Pragma",
          "Expires",
          "If-Modified-Since",
          "If-None-Match",
        ],
      })
    );

    // Rate limiting (disabled in development)
    if (!isDevelopment) {
      const limiter = rateLimit({
        windowMs: config.RATE_LIMIT_WINDOW_MS,
        max: config.RATE_LIMIT_MAX_REQUESTS,
        message: {
          success: false,
          message: "Terlalu banyak request, coba lagi nanti",
          error: "RATE_LIMIT_EXCEEDED",
          timestamp: new Date(),
        },
        standardHeaders: true,
        legacyHeaders: false,
      });
      this.app.use("/api/", limiter);
    } else {
      console.log("⚠️ Rate limiting disabled in development mode");
    }

    // Compression
    this.app.use(compression());

    // Body parsing middleware
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging middleware
    if (isDevelopment) {
      this.app.use(morgan("dev"));
    } else {
      this.app.use(morgan("combined"));
    }

    // Trust proxy for accurate IP addresses
    this.app.set("trust proxy", 1);

    // Health check endpoint
    this.app.get("/health", (_req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: "Server is running",
        data: {
          status: "healthy",
          timestamp: new Date(),
          uptime: process.uptime(),
          environment: config.NODE_ENV,
          version: config.APP_VERSION,
        },
      });
    });

    // API info endpoint
    this.app.get("/api", (_req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: "Bebang Information System API",
        data: {
          name: config.APP_NAME,
          version: config.APP_VERSION,
          company: config.COMPANY_NAME,
          environment: config.NODE_ENV,
          timestamp: new Date(),
          endpoints: {
            auth: "/api/auth",
            health: "/health",
          },
        },
      });
    });
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use("/api/auth", authRoutes);
    this.app.use("/api/hr", hrRoutes);

    // Serve uploaded files
    this.app.use("/uploads", express.static(config.UPLOAD_PATH));

    // API documentation (in development)
    if (isDevelopment) {
      this.app.get("/api/docs", (_req: Request, res: Response) => {
        res.status(200).json({
          success: true,
          message: "API Documentation",
          data: {
            endpoints: {
              "POST /api/auth/login": "User login",
              "POST /api/auth/refresh": "Refresh access token",
              "POST /api/auth/logout": "User logout",
              "GET /api/auth/profile": "Get user profile",
              "GET /api/auth/verify": "Verify token",
              "GET /health": "Health check",
              "GET /api": "API information",
            },
          },
        });
      });
    }
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  public listen(): void {
    this.app.listen(config.PORT, () => {
      console.log("🚀 Server started successfully!");
      console.log(`📍 Environment: ${config.NODE_ENV}`);
      console.log(`🌐 Server running on port ${config.PORT}`);
      console.log(`🔗 API URL: http://localhost:${config.PORT}/api`);
      console.log(`💚 Health check: http://localhost:${config.PORT}/health`);

      if (isDevelopment) {
        console.log(`📚 API docs: http://localhost:${config.PORT}/api/docs`);
      }

      console.log("-----------------------------------");
    });
  }
}

export default App;
