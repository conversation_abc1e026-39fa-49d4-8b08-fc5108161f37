"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Camera, Upload, X, ZoomIn } from "lucide-react";
import { toast } from "sonner";

interface EmployeeProfileHeaderProps {
  employee?: {
    personal: {
      fullName: string;
      employeeId: string;
      email: string;
      phone: string;
      profilePhoto?: string;
    };
    hr: {
      division?: { _id: string; name: string };
      department: { _id: string; name: string };
      position: { _id: string; name: string };
      tags?: Array<{ _id: string; name: string; color: string }>;
      companyEmail?: string; // Email perusa<PERSON>an
    };
  };
  divisions: Array<{
    _id: string;
    id?: string;
    name: string;
    isActive?: boolean;
  }>;
  departments: Array<{
    _id: string;
    id?: string;
    name: string;
    isActive?: boolean;
  }>;
  positions: Array<{
    _id?: string;
    id?: string;
    name: string;
    isActive?: boolean;
  }>;
  tags: Array<{ _id?: string; id?: string; name: string; color: string }>;
  onSave: (data: any) => void;
  isEditing: boolean;
  onEditToggle: () => void;
}

export default function EmployeeProfileHeader({
  employee,
  divisions,
  departments,
  positions,
  tags,
  onSave,
  isEditing,
  onEditToggle,
}: EmployeeProfileHeaderProps) {
  const [formData, setFormData] = useState({
    fullName: employee?.personal.fullName || "",
    employeeId: employee?.personal.employeeId || "",
    email: employee?.hr.companyEmail || "", // Menggunakan email perusahaan
    phone: employee?.personal.phone || "",
    division: employee?.hr.division?._id || "",
    department: employee?.hr.department?._id || "",
    position: employee?.hr.position?._id || "",
    selectedTags: employee?.hr.tags?.map((tag) => tag._id) || [],
    profilePhoto: employee?.personal.profilePhoto || "",
  });

  // Sinkronisasi data ketika employee berubah
  useEffect(() => {
    if (employee) {
      setFormData({
        fullName: employee.personal.fullName || "",
        employeeId: employee.personal.employeeId || "",
        email: employee.hr.companyEmail || "", // Menggunakan email perusahaan
        phone: employee.personal.phone || "",
        division: employee.hr.division?._id || "",
        department: employee.hr.department?._id || "",
        position: employee.hr.position?._id || "",
        selectedTags: employee.hr.tags?.map((tag) => tag._id) || [],
        profilePhoto: employee.personal.profilePhoto || "",
      });
    }
  }, [employee]);

  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [photoPreviewOpen, setPhotoPreviewOpen] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTagToggle = (tagId: string) => {
    setFormData((prev) => ({
      ...prev,
      selectedTags: prev.selectedTags.includes(tagId)
        ? prev.selectedTags.filter((id) => id !== tagId)
        : [...prev.selectedTags, tagId],
    }));
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        toast.error("Ukuran file terlalu besar", {
          description: "Maksimal ukuran file adalah 5MB",
        });
        return;
      }

      if (!file.type.startsWith("image/")) {
        toast.error("Format file tidak didukung", {
          description: "Hanya file gambar yang diperbolehkan",
        });
        return;
      }

      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handlePhotoUpload = async () => {
    if (!selectedFile) return;

    try {
      // Here you would implement the actual file upload logic
      // For now, we'll just simulate it
      const formData = new FormData();
      formData.append("photo", selectedFile);

      // Simulate upload
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setFormData((prev) => ({
        ...prev,
        profilePhoto: previewUrl,
      }));

      setPhotoDialogOpen(false);
      setSelectedFile(null);
      setPreviewUrl("");

      toast.success("Foto profil berhasil diupload");
    } catch (error) {
      toast.error("Gagal mengupload foto profil");
    }
  };

  const handleSave = () => {
    // Validate required fields
    if (!formData.fullName.trim()) {
      toast.error("Nama lengkap harus diisi");
      return;
    }

    if (!formData.employeeId.trim()) {
      toast.error("Nomor induk karyawan harus diisi");
      return;
    }

    // Email is optional - no validation needed

    if (!formData.phone.trim()) {
      toast.error("Nomor handphone harus diisi");
      return;
    }

    onSave(formData);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="flex items-start gap-6">
          {/* Profile Photo - Square Format, Clickable for Preview */}
          <div className="relative group">
            <div
              className="w-32 h-32 border-3 border-white shadow-lg rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-105"
              onClick={() =>
                formData.profilePhoto &&
                formData.profilePhoto.trim() !== "" &&
                setPhotoPreviewOpen(true)
              }
            >
              {formData.profilePhoto && formData.profilePhoto.trim() !== "" ? (
                <img
                  src={formData.profilePhoto}
                  alt={formData.fullName}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    console.log("Image failed to load:", formData.profilePhoto);
                    // Hide the image and show initials instead
                    e.currentTarget.style.display = "none";
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      parent.innerHTML = `<span class="text-2xl font-semibold text-white">${getInitials(
                        formData.fullName || "NN"
                      )}</span>`;
                    }
                  }}
                  onLoad={() => {
                    console.log(
                      "Image loaded successfully:",
                      formData.profilePhoto
                    );
                  }}
                />
              ) : (
                <span className="text-2xl font-semibold text-white">
                  {getInitials(formData.fullName || "NN")}
                </span>
              )}
            </div>

            {/* Hover overlay with zoom icon - only show if there's an image */}
            {formData.profilePhoto && formData.profilePhoto.trim() !== "" && (
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                <ZoomIn className="w-8 h-8 text-white" />
              </div>
            )}

            {isEditing && (
              <Dialog open={photoDialogOpen} onOpenChange={setPhotoDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    size="sm"
                    className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                  >
                    <Camera className="w-4 h-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Upload Foto Profil</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="flex flex-col items-center gap-4">
                      {previewUrl && (
                        <div className="relative">
                          <img
                            src={previewUrl}
                            alt="Preview"
                            className="w-40 h-40 rounded-lg object-cover border-2 border-gray-200"
                          />
                          <Button
                            size="sm"
                            variant="destructive"
                            className="absolute -top-2 -right-2 rounded-full w-6 h-6 p-0"
                            onClick={() => {
                              setPreviewUrl("");
                              setSelectedFile(null);
                            }}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      )}

                      <div className="flex flex-col items-center gap-2">
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleFileSelect}
                          className="hidden"
                          id="photo-upload"
                        />
                        <Label
                          htmlFor="photo-upload"
                          className="cursor-pointer flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                          <Upload className="w-4 h-4" />
                          Pilih Foto
                        </Label>
                        <p className="text-sm text-gray-500">
                          Maksimal 5MB, format JPG/PNG
                        </p>
                      </div>
                    </div>

                    <div className="flex gap-2 justify-end">
                      <Button
                        variant="outline"
                        onClick={() => setPhotoDialogOpen(false)}
                      >
                        Batal
                      </Button>
                      <Button
                        onClick={handlePhotoUpload}
                        disabled={!selectedFile}
                      >
                        Upload
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* Employee Information */}
          <div className="flex-1 space-y-4">
            {/* First Row - Full Name (spans full width) */}
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="fullName">Nama Lengkap *</Label>
                {isEditing ? (
                  <Input
                    id="fullName"
                    value={formData.fullName}
                    onChange={(e) =>
                      handleInputChange("fullName", e.target.value)
                    }
                    placeholder="Masukkan nama lengkap"
                  />
                ) : (
                  <p className="text-lg font-semibold">{formData.fullName}</p>
                )}
              </div>
            </div>

            {/* Second Row - Compact 3-column layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {/* Employee ID */}
              <div className="min-w-0">
                <Label
                  htmlFor="employeeId"
                  className="text-xs font-medium text-gray-700"
                >
                  Nomor Induk Karyawan *
                </Label>
                {isEditing ? (
                  <Input
                    id="employeeId"
                    value={formData.employeeId}
                    onChange={(e) =>
                      handleInputChange("employeeId", e.target.value)
                    }
                    placeholder="Masukkan NIK"
                    className="text-sm h-9 mt-1"
                  />
                ) : (
                  <p
                    className="font-medium text-sm mt-1 truncate"
                    title={formData.employeeId}
                  >
                    {formData.employeeId}
                  </p>
                )}
              </div>

              {/* Phone */}
              <div className="min-w-0">
                <Label
                  htmlFor="phone"
                  className="text-xs font-medium text-gray-700"
                >
                  Nomor Handphone *
                </Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    placeholder="Masukkan no. HP"
                    className="text-sm h-9 mt-1"
                  />
                ) : (
                  <p
                    className="font-medium text-sm mt-1 truncate"
                    title={formData.phone}
                  >
                    {formData.phone}
                  </p>
                )}
              </div>

              {/* Email Perusahaan */}
              <div className="min-w-0 sm:col-span-2 lg:col-span-1">
                <Label
                  htmlFor="email"
                  className="text-xs font-medium text-gray-700"
                >
                  Email Perusahaan
                </Label>
                {isEditing ? (
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="<EMAIL>"
                    className="text-sm h-9 mt-1"
                  />
                ) : (
                  <p
                    className="font-medium text-sm mt-1 truncate"
                    title={formData.email}
                  >
                    {formData.email || "-"}
                  </p>
                )}
              </div>
            </div>

            {/* Third Row - Division, Department, Position (3-column) */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {/* Division */}
              <div className="min-w-0">
                <Label
                  htmlFor="division"
                  className="text-xs font-medium text-gray-700"
                >
                  Divisi *
                </Label>
                {isEditing ? (
                  <SearchableSelect
                    value={formData.division}
                    onValueChange={(value) =>
                      handleInputChange("division", value)
                    }
                    placeholder="Pilih divisi"
                    searchPlaceholder="Cari divisi..."
                    emptyMessage="Tidak ada divisi ditemukan."
                    className="text-sm h-9 mt-1"
                    options={divisions
                      .filter(
                        (div) =>
                          (div._id || div.id) &&
                          div.name &&
                          div.isActive === true
                      )
                      .map((div) => ({
                        value: div._id || div.id,
                        label: div.name,
                      }))}
                  />
                ) : (
                  <p
                    className="font-medium text-sm mt-1 truncate"
                    title={employee?.hr.division?.name}
                  >
                    {employee?.hr.division?.name || "-"}
                  </p>
                )}
              </div>

              {/* Department */}
              <div className="min-w-0">
                <Label
                  htmlFor="department"
                  className="text-xs font-medium text-gray-700"
                >
                  Departemen *
                </Label>
                {isEditing ? (
                  <SearchableSelect
                    value={formData.department}
                    onValueChange={(value) =>
                      handleInputChange("department", value)
                    }
                    placeholder="Pilih departemen"
                    searchPlaceholder="Cari departemen..."
                    emptyMessage="Tidak ada departemen ditemukan."
                    className="text-sm h-9 mt-1"
                    options={departments
                      .filter(
                        (dept) =>
                          dept._id && dept.name && dept.isActive === true
                      )
                      .map((dept) => ({
                        value: dept._id,
                        label: dept.name,
                      }))}
                  />
                ) : (
                  <p
                    className="font-medium text-sm mt-1 truncate"
                    title={employee?.hr.department?.name}
                  >
                    {employee?.hr.department?.name}
                  </p>
                )}
              </div>

              {/* Position */}
              <div className="min-w-0 sm:col-span-2 lg:col-span-1">
                <Label
                  htmlFor="position"
                  className="text-xs font-medium text-gray-700"
                >
                  Posisi Jabatan *
                </Label>
                {isEditing ? (
                  <SearchableSelect
                    value={formData.position}
                    onValueChange={(value) =>
                      handleInputChange("position", value)
                    }
                    placeholder="Pilih posisi"
                    searchPlaceholder="Cari posisi jabatan..."
                    emptyMessage="Tidak ada posisi jabatan ditemukan."
                    className="text-sm h-9 mt-1"
                    options={positions
                      .filter(
                        (position) =>
                          (position.id || position._id) &&
                          position.name &&
                          position.isActive !== false
                      )
                      .map((position) => ({
                        value: position.id || position._id,
                        label: position.name,
                      }))}
                  />
                ) : (
                  <p
                    className="font-medium text-sm mt-1 truncate"
                    title={employee?.hr.position?.name}
                  >
                    {employee?.hr.position?.name}
                  </p>
                )}
              </div>
            </div>

            {/* Tags */}
            <div>
              <Label>Tags</Label>
              {isEditing ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {tags
                    .filter(
                      (tag) =>
                        (tag.id || tag._id) &&
                        tag.name &&
                        tag.color &&
                        tag.isActive === true // Hanya tags aktif
                    )
                    .map((tag) => {
                      const tagId = tag.id || tag._id;
                      return (
                        <Badge
                          key={tagId}
                          variant={
                            formData.selectedTags.includes(tagId)
                              ? "default"
                              : "outline"
                          }
                          className="cursor-pointer"
                          style={{
                            backgroundColor: formData.selectedTags.includes(
                              tagId
                            )
                              ? tag.color
                              : "transparent",
                            borderColor: tag.color,
                            color: formData.selectedTags.includes(tagId)
                              ? "white"
                              : tag.color,
                          }}
                          onClick={() => handleTagToggle(tagId)}
                        >
                          {tag.name}
                        </Badge>
                      );
                    })}
                </div>
              ) : (
                <div className="flex flex-wrap gap-2 mt-2">
                  {employee?.hr.tags?.map((tag) => (
                    <Badge
                      key={tag._id}
                      style={{
                        backgroundColor: tag.color,
                        color: "white",
                      }}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              {isEditing ? (
                <>
                  <Button onClick={handleSave}>Simpan Perubahan</Button>
                  <Button variant="outline" onClick={onEditToggle}>
                    Batal
                  </Button>
                </>
              ) : (
                <Button onClick={onEditToggle}>Edit Profil</Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      {/* Photo Preview Dialog */}
      <Dialog open={photoPreviewOpen} onOpenChange={setPhotoPreviewOpen}>
        <DialogContent className="max-w-4xl w-full p-0 bg-black/95">
          <DialogHeader className="absolute top-4 left-4 z-10">
            <DialogTitle className="text-white text-lg">
              Foto Profil - {formData.fullName || "Karyawan"}
            </DialogTitle>
          </DialogHeader>

          {/* Close button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
            onClick={() => setPhotoPreviewOpen(false)}
          >
            <X className="w-6 h-6" />
          </Button>

          {/* Large photo preview */}
          <div className="flex items-center justify-center min-h-[70vh] p-8">
            {formData.profilePhoto && formData.profilePhoto.trim() !== "" ? (
              <img
                src={formData.profilePhoto}
                alt={formData.fullName}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                style={{ maxHeight: "80vh", maxWidth: "90vw" }}
              />
            ) : (
              <div className="w-96 h-96 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-6xl font-semibold text-white">
                  {getInitials(formData.fullName || "NN")}
                </span>
              </div>
            )}
          </div>

          {/* Employee info overlay */}
          <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between text-white">
              <div>
                <h3 className="text-xl font-semibold">{formData.fullName}</h3>
                <p className="text-gray-300">
                  {employee?.hr.position?.name} •{" "}
                  {employee?.hr.department?.name}
                </p>
                <p className="text-gray-400 text-sm">
                  ID: {formData.employeeId}
                </p>
              </div>
              <Badge className="bg-white/20 text-white border-white/30">
                {employee?.hr.status}
              </Badge>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
