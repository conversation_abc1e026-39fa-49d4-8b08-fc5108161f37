{"name": "bis-backend", "version": "1.0.0", "description": "Bebang Information System - Backend API", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "seed": "ts-node scripts/seed-data.ts", "seed:hr": "ts-node scripts/seed-hr-data.ts", "clean:hr": "ts-node scripts/clean-hr-data.ts", "cleanup": "ts-node src/scripts/simpleCleanup.ts", "check-credentials": "ts-node src/scripts/checkCredentials.ts", "restore-credentials": "ts-node src/scripts/restoreCredentials.ts", "debug-login": "ts-node src/scripts/debugLogin.ts", "fix-user-roles": "ts-node src/scripts/fixUserRoles.ts"}, "keywords": ["nodejs", "express", "mongodb", "typescript", "bis", "psg"], "author": "PT. Prima Sarana Gemilang", "license": "ISC", "dependencies": {"@types/exceljs": "^0.5.3", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^7.6.3", "morgan": "^1.10.0", "multer": "^1.4.4", "qrcode": "^1.5.3", "socket.io": "^4.7.4", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/moment": "^2.13.0", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.8.10", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}