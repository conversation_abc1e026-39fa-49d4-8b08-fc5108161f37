"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ArrowLeft,
  Save,
  Upload,
  User,
  Briefcase,
  Users,
  Trash2,
  Camera,
  X,
} from "lucide-react";
import { useRouter, useParams } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import EditEmployeePersonalInfo from "@/components/hr/EditEmployeePersonalInfo";
import EditEmployeeHRInfo from "@/components/hr/EditEmployeeHRInfo";
import EditEmployeeFamilyInfo from "@/components/hr/EditEmployeeFamilyInfo";
import EditEmployeeHeader from "@/components/hr/EditEmployeeHeader";

const EditEmployeePage = () => {
  const router = useRouter();
  const params = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [profilePhoto, setProfilePhoto] = useState<string | null>(null);
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);

  // Master data state - Enhanced to match CreateEmployee
  const [masterData, setMasterData] = useState({
    divisions: [] as any[],
    departments: [] as any[],
    positions: [] as any[],
    employmentTypes: [] as any[],
    tags: [] as any[],
    employees: [] as any[],
    rankCategories: [] as any[],
    rankGrades: [] as any[],
    rankSubgrades: [] as any[],
  });

  // Form state - Enhanced to match CreateEmployee structure
  const [formData, setFormData] = useState({
    personal: {
      employeeId: "",
      fullName: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",

      // Enhanced personal fields to match CreateEmployee
      gender: "Laki-laki",
      placeOfBirth: "",
      dateOfBirth: "",

      // Identification
      religion: "Islam",
      bloodType: "A",
      familyCardNumber: "",
      idCardNumber: "",
      taxNumber: "",
      bpjsTkNumber: "",
      nikKkNumber: "",
      taxStatus: "TK/0",

      // Address - Current/Domisili
      currentAddress: {
        street: "",
        city: "",
        province: "",
      },

      // Address - ID Card/KTP
      idCardAddress: {
        street: "",
        city: "",
        province: "",
      },

      // Contact Information
      contact: {
        mobilePhone1: "",
        mobilePhone2: "",
        homePhone1: "",
        homePhone2: "",
      },

      // Marital Status and Children
      maritalInfo: {
        status: "Belum Menikah",
        spouseName: "",
        spouseJob: "",
        numberOfChildren: 0,
      },

      // Bank Account
      bankAccount: {
        accountNumber: "",
        accountHolder: "",
        bankName: "",
      },

      // Legacy fields for backward compatibility
      nationality: "Indonesia",
      profilePhoto: "",
      address: {
        street: "",
        city: "",
        state: "",
        postalCode: "",
        country: "Indonesia",
      },
    },
    hr: {
      division: "",
      department: "",
      position: "",
      employmentType: "",
      hireDate: "",
      probationEndDate: "",
      contractEndDate: "",

      // Enhanced HR fields to match CreateEmployee
      contract: {
        employmentType: "",
        hireDate: "",
        contractDate: "",
        contractEndDate: "",
        permanentDate: "",
        exitDate: "",
      },

      // Education
      education: {
        certificateLevel: "SMA",
        fieldOfStudy: "",
        schoolName: "",
        schoolCity: "",
        graduationStatus: "",
        description: "",
      },

      // Rank and Grade
      rank: {
        rankCategory: "",
        rankGrade: "",
        rankSubgrade: "",
        pensionFundNumber: "",
      },

      // Emergency Contact
      emergency: {
        contactName: "",
        contactPhone: "",
        contactPhone2: "",
        relationship: "",
        address: "",
        // Second Emergency Contact
        contactName2: "",
        contactPhone3: "",
        relationship2: "",
        address2: "",
      },

      // POO/POH
      location: {
        pointOfOrigin: "",
        pointOfHire: "",
      },

      // Uniform and Work Shoes
      uniform: {
        workUniformSize: "M",
        workShoesSize: "",
      },

      salary: {
        basic: "",
        allowances: {
          transport: "",
          meal: "",
          communication: "",
          position: "",
          other: "",
        },
      },
      workSchedule: "Regular",
      supervisor: "",
      manager: "",
      directSupervisor: "",
      tags: [] as string[],
      status: "Aktif",
    },
    family: {
      // Enhanced family structure to match CreateEmployee
      spouse: {
        name: "",
        dateOfBirth: "",
        marriageDate: "",
        lastEducation: "",
        occupation: "",
        numberOfChildren: 0,
      },

      // Children Identity
      children: [],

      // Parents Information
      parents: {
        father: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          occupation: "",
          description: "",
        },
        mother: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          occupation: "",
          description: "",
        },
      },

      // Siblings Information
      siblings: {
        childOrder: 1,
        totalSiblings: 0,
        siblingsData: [],
      },

      // In-laws Information
      inLaws: {
        fatherInLaw: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          description: "",
        },
        motherInLaw: {
          name: "",
          dateOfBirth: "",
          lastEducation: "",
          description: "",
        },
      },

      // Legacy emergency contact for backward compatibility
      emergencyContact: {
        name: "",
        relationship: "",
        phone: "",
        address: "",
      },
    },
  });

  // Fetch master data
  const fetchMasterData = async () => {
    try {
      const token = localStorage.getItem("accessToken");
      if (!token) {
        router.push("/login");
        return;
      }

      const [
        divisionsRes,
        departmentsRes,
        positionsRes,
        employmentTypesRes,
        tagsRes,
        employeesRes,
        rankCategoriesRes,
        rankGradesRes,
        rankSubgradesRes,
      ] = await Promise.all([
        fetch("http://localhost:5000/api/hr/divisions", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/departments", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/positions", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/employment-types", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/tags", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/employees", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/rank-categories", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/rank-grades", {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch("http://localhost:5000/api/hr/rank-subgrades", {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);

      const [
        divisionsData,
        departmentsData,
        positionsData,
        employmentTypesData,
        tagsData,
        employeesData,
        rankCategoriesData,
        rankGradesData,
        rankSubgradesData,
      ] = await Promise.all([
        divisionsRes.json(),
        departmentsRes.json(),
        positionsRes.json(),
        employmentTypesRes.json(),
        tagsRes.json(),
        employeesRes.json(),
        rankCategoriesRes.json(),
        rankGradesRes.json(),
        rankSubgradesRes.json(),
      ]);

      setMasterData({
        divisions: divisionsData.success ? divisionsData.data : [],
        departments: departmentsData.success ? departmentsData.data : [],
        positions: positionsData.success ? positionsData.data : [],
        employmentTypes: employmentTypesData.success
          ? employmentTypesData.data
          : [],
        tags: tagsData.success ? tagsData.data : [],
        employees: employeesData.success
          ? employeesData.data.map((emp: any) => ({
              id: emp._id || emp.id,
              _id: emp._id || emp.id,
              personal: {
                fullName: emp.personal?.fullName || emp.fullName || "Unknown",
                employeeId:
                  emp.personal?.employeeId || emp.employeeId || "Unknown",
              },
              position: emp.hr?.position || emp.position,
              department: emp.hr?.department || emp.department,
              isActive: emp.isActive !== false,
            }))
          : [],
        rankCategories: rankCategoriesData.success
          ? rankCategoriesData.data
          : [],
        rankGrades: rankGradesData.success ? rankGradesData.data : [],
        rankSubgrades: rankSubgradesData.success ? rankSubgradesData.data : [],
      });
    } catch (error) {
      console.error("Error fetching master data:", error);
      toast.error("Gagal memuat master data", {
        description: "Silakan coba lagi atau hubungi administrator",
      });
    }
  };

  useEffect(() => {
    const fetchEmployee = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("accessToken");
        if (!token) {
          router.push("/login");
          return;
        }

        const response = await fetch(
          `http://localhost:5000/api/hr/employees/${params.id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch employee");
        }

        const result = await response.json();
        if (result.success && result.data) {
          console.log("Raw employee data from backend:", result.data);
          console.log(
            "Profile photo from backend:",
            result.data.personal?.profilePhoto
          );

          // Helper function to extract ID from populated object
          const extractId = (field: any) => {
            console.log("extractId called with:", field);
            if (!field) return "";
            if (typeof field === "string") return field;
            if (typeof field === "object") {
              const id = field._id || field.id;
              console.log("Extracted ID:", id);
              return id || "";
            }
            return "";
          };

          // Helper function to extract array of IDs from populated array
          const extractIds = (array: any[]) => {
            if (!Array.isArray(array)) return [];
            return array
              .map((item) => extractId(item))
              .filter((id) => id !== "");
          };

          // Ensure complete data structure exists
          const employeeData = {
            personal: {
              employeeId: "",
              fullName: "",
              firstName: "",
              lastName: "",
              email: "",
              phone: "",
              dateOfBirth: "",
              gender: "",
              maritalStatus: "",
              nationality: "Indonesia",
              religion: "",
              idNumber: "",
              profilePhoto: "",
              address: {
                street: "",
                city: "",
                state: "",
                postalCode: "",
                country: "Indonesia",
              },
              ...result.data.personal,
              // Ensure profilePhoto is properly mapped
              profilePhoto: result.data.personal?.profilePhoto || "",
            },
            hr: {
              // Preserve all original HR data first
              ...result.data.hr,
              // Extract IDs for populated reference fields
              division:
                extractId(result.data.hr?.division) ||
                result.data.hr?.division ||
                "",
              department:
                extractId(result.data.hr?.department) ||
                result.data.hr?.department ||
                "",
              position:
                extractId(result.data.hr?.position) ||
                result.data.hr?.position ||
                "",
              manager:
                extractId(result.data.hr?.manager) ||
                result.data.hr?.manager ||
                "",
              directSupervisor:
                extractId(result.data.hr?.directSupervisor) ||
                result.data.hr?.directSupervisor ||
                "",
              tags: extractIds(result.data.hr?.tags || []),
              // Handle contract object properly - preserve all contract data
              contract: {
                // First spread all contract data
                ...result.data.hr?.contract,
                // Then override specific fields with proper extraction
                hireDate:
                  result.data.hr?.contract?.hireDate ||
                  result.data.hr?.hireDate ||
                  "",
                contractDate: result.data.hr?.contract?.contractDate || "",
                contractEndDate:
                  result.data.hr?.contract?.contractEndDate || "",
                permanentDate: result.data.hr?.contract?.permanentDate || "",
                exitDate: result.data.hr?.contract?.exitDate || "",
                employmentType: extractId(
                  result.data.hr?.contract?.employmentType
                ),
              },
              // Handle rank fields properly
              rank: {
                // First spread all rank data
                ...result.data.hr?.rank,
                // Then override specific fields with proper extraction
                rankCategory: extractId(result.data.hr?.rank?.rankCategory),
                rankGrade: extractId(result.data.hr?.rank?.rankGrade),
                rankSubgrade: extractId(result.data.hr?.rank?.rankSubgrade),
                pensionFundNumber:
                  result.data.hr?.rank?.pensionFundNumber || "",
              },
              // Handle education fields properly
              education: {
                certificateLevel:
                  result.data.hr?.education?.certificateLevel || "SMA",
                fieldOfStudy: result.data.hr?.education?.fieldOfStudy || "",
                schoolName: result.data.hr?.education?.schoolName || "",
                schoolCity: result.data.hr?.education?.schoolCity || "",
                graduationStatus:
                  result.data.hr?.education?.graduationStatus || "",
                description: result.data.hr?.education?.description || "",
                ...result.data.hr?.education,
              },
              // Handle salary properly
              salary: {
                basic: result.data.hr?.salary?.basic || 0,
                allowances: {
                  transport: result.data.hr?.salary?.allowances?.transport || 0,
                  meal: result.data.hr?.salary?.allowances?.meal || 0,
                  communication:
                    result.data.hr?.salary?.allowances?.communication || 0,
                  position: result.data.hr?.salary?.allowances?.position || 0,
                  other: result.data.hr?.salary?.allowances?.other || 0,
                  ...result.data.hr?.salary?.allowances,
                },
                total: result.data.hr?.salary?.total || 0,
                ...result.data.hr?.salary,
              },
              // Ensure critical fields are preserved
              employeeId:
                result.data.personal?.employeeId ||
                result.data.hr?.employeeId ||
                "",
              // Sync hireDate between contract and root level for backward compatibility
              hireDate:
                result.data.hr?.contract?.hireDate ||
                result.data.hr?.hireDate ||
                "",
              workSchedule: result.data.hr?.workSchedule || "Regular",
              status: result.data.hr?.status || "Aktif",
              companyEmail: result.data.hr?.companyEmail || "",
              // Also add employmentType at root level for backward compatibility
              employmentType: extractId(
                result.data.hr?.contract?.employmentType
              ),
            },
            family: {
              emergencyContact: {
                name: "",
                relationship: "",
                phone: "",
                address: "",
                ...result.data.family?.emergencyContact,
              },
              ...result.data.family,
            },
          };
          // COMPREHENSIVE DATA VALIDATION AND LOGGING
          console.log("=== COMPREHENSIVE DATA CHECK ===");
          console.log("Raw backend data:", result.data);
          console.log("Mapped employee data:", employeeData);

          // Log specific HR data from backend
          console.log("=== RAW HR DATA FROM BACKEND ===");
          console.log("HR Contract:", result.data.hr?.contract);
          console.log("HR Education:", result.data.hr?.education);
          console.log("HR Rank:", result.data.hr?.rank);
          console.log(
            "HR Employment Type (root):",
            result.data.hr?.employmentType
          );

          // Check all ObjectId fields
          console.log("ObjectId Fields Validation:", {
            division: {
              raw: result.data.hr?.division,
              extracted: employeeData.hr.division,
              isValid:
                employeeData.hr.division !== "" &&
                employeeData.hr.division !== null,
            },
            department: {
              raw: result.data.hr?.department,
              extracted: employeeData.hr.department,
              isValid:
                employeeData.hr.department !== "" &&
                employeeData.hr.department !== null,
            },
            position: {
              raw: result.data.hr?.position,
              extracted: employeeData.hr.position,
              isValid:
                employeeData.hr.position !== "" &&
                employeeData.hr.position !== null,
            },
            manager: {
              raw: result.data.hr?.manager,
              extracted: employeeData.hr.manager,
              isValid:
                employeeData.hr.manager !== "" &&
                employeeData.hr.manager !== null,
            },
            directSupervisor: {
              raw: result.data.hr?.directSupervisor,
              extracted: employeeData.hr.directSupervisor,
              isValid:
                employeeData.hr.directSupervisor !== "" &&
                employeeData.hr.directSupervisor !== null,
            },
            employmentType: {
              raw: result.data.hr?.contract?.employmentType,
              extracted: employeeData.hr.contract?.employmentType,
              isValid:
                employeeData.hr.contract?.employmentType !== "" &&
                employeeData.hr.contract?.employmentType !== null,
            },
          });

          // Check rank fields specifically
          console.log("Rank Fields Validation:", {
            rankCategory: {
              raw: result.data.hr?.rank?.rankCategory,
              extracted: employeeData.hr.rank?.rankCategory,
              isValid:
                employeeData.hr.rank?.rankCategory !== "" &&
                employeeData.hr.rank?.rankCategory !== null,
            },
            rankGrade: {
              raw: result.data.hr?.rank?.rankGrade,
              extracted: employeeData.hr.rank?.rankGrade,
              isValid:
                employeeData.hr.rank?.rankGrade !== "" &&
                employeeData.hr.rank?.rankGrade !== null,
            },
            rankSubgrade: {
              raw: result.data.hr?.rank?.rankSubgrade,
              extracted: employeeData.hr.rank?.rankSubgrade,
              isValid:
                employeeData.hr.rank?.rankSubgrade !== "" &&
                employeeData.hr.rank?.rankSubgrade !== null,
            },
          });

          // Check education fields specifically
          console.log("Education Fields Validation:", {
            graduationStatus: {
              raw: result.data.hr?.education?.graduationStatus,
              extracted: employeeData.hr.education?.graduationStatus,
              isValid:
                employeeData.hr.education?.graduationStatus !== "" &&
                employeeData.hr.education?.graduationStatus !== null,
            },
            certificateLevel: {
              raw: result.data.hr?.education?.certificateLevel,
              extracted: employeeData.hr.education?.certificateLevel,
            },
            fieldOfStudy: {
              raw: result.data.hr?.education?.fieldOfStudy,
              extracted: employeeData.hr.education?.fieldOfStudy,
            },
          });

          // Check contract fields specifically
          console.log("Contract Fields Validation:", {
            employmentType: {
              raw: result.data.hr?.contract?.employmentType,
              extracted: employeeData.hr.contract?.employmentType,
              isValid:
                employeeData.hr.contract?.employmentType !== "" &&
                employeeData.hr.contract?.employmentType !== null,
            },
            permanentDate: {
              raw: result.data.hr?.contract?.permanentDate,
              extracted: employeeData.hr.contract?.permanentDate,
              isValid:
                employeeData.hr.contract?.permanentDate !== "" &&
                employeeData.hr.contract?.permanentDate !== null,
            },
            hireDate: {
              raw: result.data.hr?.contract?.hireDate,
              extracted: employeeData.hr.contract?.hireDate,
            },
          });

          // Check all date fields
          console.log("Date Fields Validation:", {
            hireDate: {
              raw: result.data.hr?.hireDate,
              mapped: employeeData.hr.hireDate,
              isValid:
                employeeData.hr.hireDate !== "" &&
                employeeData.hr.hireDate !== null,
            },
            contractEndDate: {
              raw: result.data.hr?.contractEndDate,
              mapped: employeeData.hr.contractEndDate,
              isValid:
                employeeData.hr.contractEndDate !== "" &&
                employeeData.hr.contractEndDate !== null,
            },
            probationEndDate: {
              raw: result.data.hr?.probationEndDate,
              mapped: employeeData.hr.probationEndDate,
              isValid:
                employeeData.hr.probationEndDate !== "" &&
                employeeData.hr.probationEndDate !== null,
            },
          });

          // Check other important fields
          console.log("Other Important Fields:", {
            workSchedule: employeeData.hr.workSchedule,
            status: employeeData.hr.status,
            companyEmail: employeeData.hr.companyEmail,
            tags: employeeData.hr.tags,
            salary: employeeData.hr.salary,
          });
          console.log(
            "Final profilePhoto:",
            employeeData.personal.profilePhoto
          );
          setFormData(employeeData);
        } else {
          throw new Error(result.message || "Failed to load employee data");
        }
      } catch (error) {
        console.error("Error fetching employee:", error);
        toast.error("Gagal memuat data karyawan", {
          description: "Silakan coba lagi atau hubungi administrator",
        });
        router.push("/hr/employees");
      } finally {
        setIsLoading(false);
      }
    };

    // Set mock token if not exists (for development)
    if (typeof window !== "undefined" && !localStorage.getItem("accessToken")) {
      localStorage.setItem("accessToken", "mock-token-for-development");
    }

    fetchMasterData();
    fetchEmployee();
  }, [params.id, router]);

  const handleInputChange = (section: string, field: string, value: string) => {
    setFormData((prev) => {
      const newFormData = {
        ...prev,
        [section]: {
          ...prev[section as keyof typeof prev],
          [field]: value,
        },
      };

      // PHONE SYNC - BIDIRECTIONAL SYNC LOGIC (same as Create form)
      if (section === "personal") {
        // Sync from Nomor Handphone (phone) to HP 1 (contact.mobilePhone1)
        if (field === "phone" && value !== undefined) {
          newFormData.personal = {
            ...newFormData.personal,
            contact: {
              ...newFormData.personal.contact,
              mobilePhone1: value,
            },
          };
        }

        // EMPLOYEE ID SYNC - Sync from personal.employeeId to hr.employeeId
        if (field === "employeeId" && value !== undefined) {
          newFormData.hr = {
            ...newFormData.hr,
            employeeId: value,
          };
        }
      }

      // EMPLOYEE ID SYNC - Sync from hr.employeeId to personal.employeeId
      if (section === "hr" && field === "employeeId" && value !== undefined) {
        newFormData.personal = {
          ...newFormData.personal,
          employeeId: value,
        };
      }

      return newFormData;
    });
  };

  const handleNestedInputChange = (
    section: string,
    subsection: string,
    field: string,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [subsection]: {
          ...(prev[section as keyof typeof prev] as any)[subsection],
          [field]: value,
        },
      },
    }));
  };

  const handleTagToggle = (tagId: string) => {
    const currentTags = formData.hr.tags || [];
    const updatedTags = currentTags.includes(tagId)
      ? currentTags.filter((id) => id !== tagId)
      : [...currentTags, tagId];

    handleInputChange("hr", "tags", updatedTags as any);
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePhoto(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const token = localStorage.getItem("accessToken");
      if (!token) {
        router.push("/login");
        return;
      }

      // Clean and prepare data for submission
      const submitData = {
        personal: {
          ...formData.personal,
          // Ensure required fields are not empty
          employeeId: formData.personal?.employeeId || "",
          fullName: formData.personal?.fullName || "",
          email: formData.personal?.email || "",
          phone: formData.personal?.phone || "",
          // Include profile photo if uploaded
          profilePhoto: profilePhoto || formData.personal?.profilePhoto || "",
        },
        hr: {
          ...formData.hr,
          // Ensure required fields are not empty
          status: formData.hr?.status || "Aktif",
          // Preserve all HR fields including ObjectId references
          division: formData.hr?.division || "",
          department: formData.hr?.department || "",
          position: formData.hr?.position || "",
          manager: formData.hr?.manager || "",
          directSupervisor: formData.hr?.directSupervisor || "",
          // Handle contract properly - preserve all contract fields
          contract: {
            ...formData.hr?.contract,
            hireDate:
              formData.hr?.contract?.hireDate || formData.hr?.hireDate || "",
            employmentType:
              formData.hr?.contract?.employmentType ||
              formData.hr?.employmentType ||
              "",
          },
          // Sync fields for backward compatibility
          hireDate:
            formData.hr?.contract?.hireDate || formData.hr?.hireDate || "",
          employmentType:
            formData.hr?.contract?.employmentType ||
            formData.hr?.employmentType ||
            "",
          // Handle rank properly - preserve all rank fields
          rank: {
            ...formData.hr?.rank,
            rankCategory: formData.hr?.rank?.rankCategory || "",
            rankGrade: formData.hr?.rank?.rankGrade || "",
            rankSubgrade: formData.hr?.rank?.rankSubgrade || "",
          },
        },
        family: formData.family || {},
      };

      // COMPREHENSIVE SUBMIT DATA VALIDATION
      console.log("=== SUBMIT DATA VALIDATION ===");
      console.log("Complete submit data:", submitData);

      // Validate critical fields before submission
      const criticalFields = {
        "personal.employeeId": submitData.personal.employeeId,
        "personal.fullName": submitData.personal.fullName,
        "hr.division": submitData.hr.division,
        "hr.department": submitData.hr.department,
        "hr.position": submitData.hr.position,
        "hr.status": submitData.hr.status,
        "hr.hireDate": submitData.hr.hireDate,
        "hr.contract.employmentType": submitData.hr.contract?.employmentType,
        "hr.contractEndDate": submitData.hr.contractEndDate,
        "hr.probationEndDate": submitData.hr.probationEndDate,
      };

      console.log("Critical fields check:", criticalFields);

      // Check rank fields specifically
      const rankFields = {
        "hr.rank.rankCategory": submitData.hr.rank?.rankCategory,
        "hr.rank.rankGrade": submitData.hr.rank?.rankGrade,
        "hr.rank.rankSubgrade": submitData.hr.rank?.rankSubgrade,
        "hr.rank.pensionFundNumber": submitData.hr.rank?.pensionFundNumber,
      };

      console.log("Rank fields check:", rankFields);

      // Check for any undefined or null critical fields
      const missingFields = Object.entries(criticalFields)
        .filter(([key, value]) => value === undefined || value === null)
        .map(([key]) => key);

      if (missingFields.length > 0) {
        console.warn("⚠️ Missing critical fields:", missingFields);
      }

      // Log field counts for verification
      console.log("Field counts:", {
        personal: Object.keys(submitData.personal).length,
        hr: Object.keys(submitData.hr).length,
        family: Object.keys(submitData.family).length,
        totalFields:
          Object.keys(submitData.personal).length +
          Object.keys(submitData.hr).length +
          Object.keys(submitData.family).length,
      });

      // NOTE: We keep all fields (including empty ones) to maintain data structure
      // Empty fields will be preserved in the database to avoid data loss on subsequent edits

      console.log("Submitting to backend:", submitData);

      // Detailed logging for problematic fields
      console.log("=== DETAILED FIELD LOGGING ===");
      console.log("Employment Type:", {
        fromFormData: formData.hr?.contract?.employmentType,
        fromSubmitData: submitData.hr.contract?.employmentType,
        rootLevel: submitData.hr.employmentType,
      });
      console.log("Rank Category:", {
        fromFormData: formData.hr?.rank?.rankCategory,
        fromSubmitData: submitData.hr.rank?.rankCategory,
      });
      console.log("Rank Grade:", {
        fromFormData: formData.hr?.rank?.rankGrade,
        fromSubmitData: submitData.hr.rank?.rankGrade,
      });
      console.log("Rank Subgrade:", {
        fromFormData: formData.hr?.rank?.rankSubgrade,
        fromSubmitData: submitData.hr.rank?.rankSubgrade,
      });

      const response = await fetch(
        `http://localhost:5000/api/hr/employees/${params.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(submitData),
        }
      );

      const result = await response.json();
      console.log("Backend response:", result);
      console.log("Response status:", response.status);

      if (!response.ok) {
        console.error("Backend error details:", {
          status: response.status,
          statusText: response.statusText,
          result: result,
        });
        throw new Error(
          result.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      if (result.success) {
        console.log("=== POST-SAVE VALIDATION ===");
        console.log("Updated employee data from backend:", result.data);

        // Validate that all submitted data is preserved
        const validationResults = {
          division: {
            submitted: submitData.hr.division,
            returned: result.data?.hr?.division,
            preserved: submitData.hr.division
              ? (result.data?.hr?.division?._id ||
                  result.data?.hr?.division) === submitData.hr.division
              : true,
          },
          department: {
            submitted: submitData.hr.department,
            returned: result.data?.hr?.department,
            preserved: submitData.hr.department
              ? (result.data?.hr?.department?._id ||
                  result.data?.hr?.department) === submitData.hr.department
              : true,
          },
          position: {
            submitted: submitData.hr.position,
            returned: result.data?.hr?.position,
            preserved: submitData.hr.position
              ? (result.data?.hr?.position?._id ||
                  result.data?.hr?.position) === submitData.hr.position
              : true,
          },
          employmentType: {
            submitted: submitData.hr.contract?.employmentType,
            returned: result.data?.hr?.contract?.employmentType,
            preserved: submitData.hr.contract?.employmentType
              ? (result.data?.hr?.contract?.employmentType?._id ||
                  result.data?.hr?.contract?.employmentType) ===
                submitData.hr.contract?.employmentType
              : true,
          },
          hireDate: {
            submitted: submitData.hr.hireDate,
            returned: result.data?.hr?.hireDate,
            preserved: submitData.hr.hireDate === result.data?.hr?.hireDate,
          },
        };

        console.log("Data preservation validation:", validationResults);

        // Check if any critical data was lost
        const lostData = Object.entries(validationResults).filter(
          ([key, validation]) => !validation.preserved
        );
        if (lostData.length > 0) {
          console.warn("⚠️ Some data may have been lost:", lostData);
        } else {
          console.log("✅ All submitted data preserved successfully");
        }

        toast.success("Karyawan berhasil diperbarui", {
          description: "Data karyawan telah disimpan ke sistem",
        });

        // Refresh the page to reload data instead of navigating away
        // This ensures we can verify data persistence immediately
        window.location.reload();
      } else {
        throw new Error(result.message || "Failed to update employee");
      }
    } catch (error) {
      console.error("Error updating employee:", error);

      let errorMessage = "Silakan coba lagi atau hubungi administrator";
      if (error instanceof Error) {
        errorMessage = error.message;
        console.error("Detailed error message:", error.message);
      }

      toast.error("Gagal memperbarui karyawan", {
        description: errorMessage,
        duration: 5000, // Show longer for debugging
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const token = localStorage.getItem("accessToken");
      if (!token) {
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/employees/${params.id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete employee");
      }

      const result = await response.json();
      if (result.success) {
        toast.success("Karyawan berhasil dihapus", {
          description: "Data karyawan telah dihapus dari sistem",
        });
        router.push("/hr/employees");
      } else {
        throw new Error(result.message || "Failed to delete employee");
      }
    } catch (error) {
      console.error("Error deleting employee:", error);
      toast.error("Gagal menghapus karyawan", {
        description: "Silakan coba lagi atau hubungi administrator",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading && !formData.personal?.employeeId) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Karyawan</h1>
            <p className="text-gray-600 mt-1">
              Perbarui informasi karyawan {formData.personal?.firstName || ""}{" "}
              {formData.personal?.lastName || ""}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" disabled={isDeleting}>
                <Trash2 className="w-4 h-4 mr-2" />
                {isDeleting ? "Menghapus..." : "Hapus"}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Hapus Karyawan</AlertDialogTitle>
                <AlertDialogDescription>
                  Apakah Anda yakin ingin menghapus karyawan{" "}
                  {formData.personal?.firstName || ""}{" "}
                  {formData.personal?.lastName || ""}? Tindakan ini tidak dapat
                  dibatalkan.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Batal</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Hapus
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? "Menyimpan..." : "Simpan Perubahan"}
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Employee Header Section - Always Visible */}
        <div className="mb-6">
          <EditEmployeeHeader
            data={{
              personal: {
                fullName: formData.personal?.fullName || "",
                employeeId: formData.personal?.employeeId || "",
                phone: formData.personal?.phone || "",
                email: formData.personal?.email || "",
                profilePhoto:
                  profilePhoto || formData.personal?.profilePhoto || "",
              },
              hr: {
                division: formData.hr?.division || "",
                department: formData.hr?.department || "",
                position: formData.hr?.position || "",
                companyEmail: formData.hr?.companyEmail || "",
                manager: formData.hr?.manager || "",
                directSupervisor: formData.hr?.directSupervisor || "",
                tags: formData.hr?.tags || [],
                status: formData.hr?.status || "Aktif",
              },
            }}
            onUpdate={handleInputChange}
            masterData={masterData}
            onTagToggle={handleTagToggle}
            onPhotoUpload={async (file) => {
              try {
                // Convert file to base64 for storage
                const reader = new FileReader();
                reader.onload = (e) => {
                  const base64String = e.target?.result as string;
                  console.log(
                    "Photo converted to base64, length:",
                    base64String.length
                  );
                  setProfilePhoto(base64String);
                  handleInputChange("personal", "profilePhoto", base64String);
                };
                reader.readAsDataURL(file);
              } catch (error) {
                console.error("Error uploading photo:", error);
                toast.error("Gagal mengupload foto");
              }
            }}
          />
        </div>

        {/* Tabs for additional information */}
        <Tabs defaultValue="personal" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white">
            <TabsTrigger
              value="personal"
              className="flex items-center space-x-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <User className="w-4 h-4" />
              <span>Informasi Personal</span>
            </TabsTrigger>
            <TabsTrigger
              value="hr"
              className="flex items-center space-x-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <Briefcase className="w-4 h-4" />
              <span>Informasi HR</span>
            </TabsTrigger>
            <TabsTrigger
              value="family"
              className="flex items-center space-x-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <Users className="w-4 h-4" />
              <span>Informasi Keluarga</span>
            </TabsTrigger>
          </TabsList>

          {/* Personal Information Tab */}
          <TabsContent value="personal">
            <EditEmployeePersonalInfo
              data={formData.personal}
              onUpdate={(updatedPersonal) =>
                setFormData((prev) => {
                  const newFormData = {
                    ...prev,
                    personal: updatedPersonal,
                  };

                  // PHONE SYNC - Sync from HP 1 (contact.mobilePhone1) to Nomor Handphone (phone)
                  if (updatedPersonal.contact?.mobilePhone1 !== undefined) {
                    newFormData.personal = {
                      ...newFormData.personal,
                      phone: updatedPersonal.contact.mobilePhone1,
                    };
                  }

                  // Synchronize spouse fields from personal to family
                  if (updatedPersonal.maritalInfo) {
                    const maritalInfo = updatedPersonal.maritalInfo;
                    const updates: any = {};

                    if (maritalInfo.spouseName !== undefined) {
                      updates.name = maritalInfo.spouseName;
                    }
                    if (maritalInfo.spouseJob !== undefined) {
                      updates.occupation = maritalInfo.spouseJob;
                    }
                    if (maritalInfo.numberOfChildren !== undefined) {
                      updates.numberOfChildren = maritalInfo.numberOfChildren;
                    }

                    if (Object.keys(updates).length > 0) {
                      newFormData.family = {
                        ...newFormData.family,
                        spouse: {
                          ...newFormData.family.spouse,
                          ...updates,
                        },
                      };
                    }
                  }

                  return newFormData;
                })
              }
            />
          </TabsContent>

          {/* HR Information Tab */}
          <TabsContent value="hr">
            <EditEmployeeHRInfo
              data={formData.hr}
              onUpdate={(updatedHR) =>
                setFormData((prev) => {
                  const newFormData = {
                    ...prev,
                    hr: updatedHR,
                  };

                  // EMPLOYEE ID SYNC - Sync from hr.employeeId to personal.employeeId
                  if (updatedHR.employeeId !== undefined) {
                    newFormData.personal = {
                      ...newFormData.personal,
                      employeeId: updatedHR.employeeId,
                    };
                  }

                  return newFormData;
                })
              }
              masterData={masterData}
              onTagToggle={handleTagToggle}
            />
          </TabsContent>

          {/* Family Information Tab */}
          <TabsContent value="family">
            <EditEmployeeFamilyInfo
              data={formData.family}
              onUpdate={(updatedFamily) =>
                setFormData((prev) => {
                  const newFormData = {
                    ...prev,
                    family: updatedFamily,
                  };

                  // Synchronize spouse fields from family to personal
                  if (updatedFamily.spouse) {
                    const spouse = updatedFamily.spouse;
                    const updates: any = {};

                    if (spouse.name !== undefined) {
                      updates.spouseName = spouse.name;
                    }
                    if (spouse.occupation !== undefined) {
                      updates.spouseJob = spouse.occupation;
                    }
                    if (spouse.numberOfChildren !== undefined) {
                      updates.numberOfChildren = spouse.numberOfChildren;
                    }

                    if (Object.keys(updates).length > 0) {
                      newFormData.personal = {
                        ...newFormData.personal,
                        maritalInfo: {
                          ...newFormData.personal.maritalInfo,
                          ...updates,
                        },
                      };
                    }
                  }

                  return newFormData;
                })
              }
            />
          </TabsContent>
        </Tabs>
      </form>
    </div>
  );
};

export default EditEmployeePage;
