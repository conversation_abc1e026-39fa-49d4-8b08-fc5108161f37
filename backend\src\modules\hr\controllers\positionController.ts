import { Request, Response } from "express";
import Position from "../models/Position";
import { Department } from "../models/Department";

// No hardcoded data - use MongoDB database only

// Get all positions
export const getPositions = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { search, department, status } = req.query;

    // Build filter (show all positions, both active and inactive)
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (department) {
      filter.department = department;
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const positions = await Position.find(filter)
      .populate("department", "name")
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: positions,
      message: "Positions retrieved successfully",
      count: positions.length,
    });
  } catch (error) {
    console.error("Error fetching positions:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Get position by ID
export const getPositionById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const position = await Position.findById(id).populate("department", "name");

    if (!position) {
      res.status(404).json({
        success: false,
        message: "Position not found",
      });
      return;
    }

    res.json({
      success: true,
      data: position,
      message: "Position retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching position:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Create new position
export const createPosition = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { name, department, description, isActive = true } = req.body;

    // Check if department exists and is active
    const departmentExists = await Department.findById(department);
    if (!departmentExists) {
      res.status(400).json({
        success: false,
        message: "Department not found",
      });
      return;
    }

    if (!departmentExists.isActive) {
      res.status(400).json({
        success: false,
        message: "Cannot create position for inactive department",
      });
      return;
    }

    // Check if position name already exists in the same department (only check active positions)
    const existingPosition = await Position.findOne({
      name,
      department,
      isActive: true,
    });

    if (existingPosition) {
      res.status(400).json({
        success: false,
        message: "Position already exists in this department",
      });
      return;
    }

    // Create new position
    const position = new Position({
      name,
      department,
      description,
      isActive,
      // Skip createdBy/updatedBy for development
    });

    await position.save();
    await position.populate("department", "name");

    res.status(201).json({
      success: true,
      data: position,
      message: "Position created successfully",
    });
  } catch (error) {
    console.error("Error creating position:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Update position
export const updatePosition = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, department, description, isActive } = req.body;

    // Check if position exists
    const position = await Position.findById(id);
    if (!position) {
      res.status(404).json({
        success: false,
        message: "Position not found",
      });
      return;
    }

    // Check if department exists and is active
    if (department) {
      const departmentExists = await Department.findById(department);
      if (!departmentExists) {
        res.status(400).json({
          success: false,
          message: "Department not found",
        });
        return;
      }

      if (!departmentExists.isActive) {
        res.status(400).json({
          success: false,
          message: "Cannot assign position to inactive department",
        });
        return;
      }
    }

    // Update position
    position.name = name || position.name;
    position.department = department || position.department;
    position.description = description || position.description;
    position.isActive = isActive !== undefined ? isActive : position.isActive;
    // Skip updatedBy for development

    await position.save();
    await position.populate("department", "name");

    res.json({
      success: true,
      data: position,
      message: "Position updated successfully",
    });
  } catch (error) {
    console.error("Error updating position:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Delete position (status-based delete)
export const deletePosition = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const position = await Position.findById(id);
    if (!position) {
      res.status(404).json({
        success: false,
        message: "Position not found",
      });
      return;
    }

    // Status-based delete by setting isActive to false
    await Position.findByIdAndUpdate(id, {
      isActive: false,
    });

    res.json({
      success: true,
      message: "Position deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting position:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};
