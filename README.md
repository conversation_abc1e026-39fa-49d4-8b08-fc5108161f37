# Bebang Information System (BIS)

**Sistem Informasi Terintegrasi untuk PT. Prima Sarana Gemilang - Site Taliabu**

## 📋 Deskripsi Project

Bebang Information System (BIS) adalah sistem informasi terintegrasi yang dirancang khusus untuk mengelola operasional PT. Prima Sarana Gemilang di Site Taliabu. Sistem ini mencakup 6 modul utama yang saling terintegrasi untuk mendukung efisiensi operasional perusahaan.

## 🏗️ Arsitektur Sistem

- **Frontend**: Next.js 14+ dengan TypeScript
- **Backend**: Express.js dengan TypeScript  
- **Database**: MongoDB
- **Real-time**: Socket.io untuk fitur chat
- **UI Framework**: Tailwind CSS + shadcn/ui
- **Authentication**: JWT dengan Role-based Access Control

## 📦 Modul Sistem

### 1. Human Resources Management
- Data Karyawan & Mutasi
- Manajemen Cuti & Pelanggaran
- Dokumen Personal & Riwayat Aset

### 2. Inventory Management
- Tracking Aset dengan QR Code
- Pergerakan <PERSON> (Masuk/Keluar/Transfer)
- Monitoring Lokasi & Kondisi

### 3. Mess Management
- <PERSON><PERSON><PERSON>
- Check-in/Check-out Penghuni
- Monitoring Fasilitas Mess

### 4. Building Management
- Pemetaan Aset Kantor
- Monitoring Ruangan & Fasilitas
- Riwayat Maintenance

### 5. User Access Management
- Role-based Access Control (RBAC)
- Dynamic Menu Generation
- User & Permission Management

### 6. Internal Communication
- Chat 1-on-1 & Group Chat
- Real-time Notifications
- File Sharing & Attachments

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB 6.0+
- npm atau yarn

### Installation

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd pt-psg-taliabu
   ```

2. **Install Dependencies**
   ```bash
   npm run setup
   ```

3. **Setup Environment**
   ```bash
   # Backend environment
   cp backend/.env.example backend/.env
   # Frontend environment  
   cp frontend/.env.local.example frontend/.env.local
   ```

4. **Configure Database**
   - Pastikan MongoDB berjalan di `mongodb://localhost:27017`
   - Update connection string di `backend/.env` jika diperlukan

5. **Start Development**
   ```bash
   npm run dev
   ```

   Aplikasi akan berjalan di:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## 📁 Struktur Project

```
pt-psg-taliabu/
├── backend/                 # Express.js API Server
│   ├── src/
│   │   ├── modules/        # Modul-modul aplikasi
│   │   ├── shared/         # Shared utilities & services
│   │   └── config/         # Konfigurasi aplikasi
│   └── uploads/            # File storage
├── frontend/               # Next.js Frontend
│   ├── src/
│   │   ├── app/           # Next.js App Router
│   │   ├── components/    # Reusable components
│   │   ├── modules/       # Feature-specific components
│   │   └── shared/        # Shared hooks & utilities
│   └── public/            # Static assets
├── memory-bank/           # Project documentation
└── docs/                  # Additional documentation
```

## 🛠️ Development Commands

```bash
# Development
npm run dev              # Start both frontend & backend
npm run dev:backend      # Start backend only
npm run dev:frontend     # Start frontend only

# Build
npm run build           # Build both applications
npm run build:backend   # Build backend only
npm run build:frontend  # Build frontend only

# Testing
npm run test           # Run all tests
npm run lint           # Run linting

# Database
npm run seed           # Seed database with sample data
```

## 🔧 Configuration

### Backend Configuration (backend/.env)
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/psg-sisinfo
JWT_SECRET=your-secret-key
```

### Frontend Configuration (frontend/.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_WS_URL=http://localhost:5000
```

## 📚 Documentation

- [Project Brief](memory-bank/projectbrief.md)
- [System Architecture](memory-bank/systemPatterns.md)
- [Technical Context](memory-bank/techContext.md)
- [API Documentation](docs/api/)
- [Deployment Guide](docs/deployment/)

## 🔐 Security Features

- JWT Authentication dengan Refresh Token
- Role-based Access Control (RBAC)
- Input Validation & Sanitization
- File Upload Security
- CORS Protection
- Rate Limiting

## 🚀 Deployment

### Windows Server (Production)
1. Install Node.js 18+ dan MongoDB
2. Build aplikasi: `npm run build`
3. Setup environment variables
4. Start dengan PM2: `pm2 start ecosystem.config.js`

### Cloud Deployment
- Siap untuk deployment ke Vercel (Frontend) + Railway/Heroku (Backend)
- Docker support untuk containerization
- MongoDB Atlas untuk cloud database

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/nama-fitur`
3. Commit changes: `git commit -m 'Add some feature'`
4. Push to branch: `git push origin feature/nama-fitur`
5. Submit pull request

## 📄 License

Copyright © 2024 PT. Prima Sarana Gemilang. All rights reserved.

## 📞 Support

Untuk support dan pertanyaan, hubungi tim development PT. Prima Sarana Gemilang.

---

**Dibuat dengan ❤️ untuk PT. Prima Sarana Gemilang - Site Taliabu**
