"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  DollarSign,
  FileText,
  Download,
  Filter,
  RefreshCw,
  Eye,
  Clock,
  Award,
} from "lucide-react";

const ReportsPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Laporan HR</h1>
          <p className="text-gray-600 mt-1">
            Analitik dan laporan komprehensif untuk manajemen sumber daya
            manusia
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Karyawan
                </p>
                <p className="text-2xl font-bold text-gray-900">156</p>
                <p className="text-xs text-green-600 mt-1">+12 bulan ini</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Tingkat Kehadiran
                </p>
                <p className="text-2xl font-bold text-gray-900">94.2%</p>
                <p className="text-xs text-green-600 mt-1">
                  +2.1% dari bulan lalu
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Rata-rata Kinerja
                </p>
                <p className="text-2xl font-bold text-gray-900">4.3/5</p>
                <p className="text-xs text-green-600 mt-1">
                  +0.2 dari quarter lalu
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Award className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Penggajian
                </p>
                <p className="text-2xl font-bold text-gray-900">2.4M</p>
                <p className="text-xs text-gray-600 mt-1">Bulan ini</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Employee Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Users className="w-5 h-5 mr-2 text-blue-600" />
              Laporan Karyawan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Demografi Karyawan</p>
                <p className="text-xs text-gray-600">
                  Distribusi usia, gender, pendidikan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Struktur Organisasi</p>
                <p className="text-xs text-gray-600">
                  Distribusi per departemen & posisi
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Turnover Analysis</p>
                <p className="text-xs text-gray-600">
                  Tingkat keluar masuk karyawan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Attendance Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Clock className="w-5 h-5 mr-2 text-green-600" />
              Laporan Kehadiran
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Tingkat Kehadiran</p>
                <p className="text-xs text-gray-600">
                  Statistik kehadiran harian/bulanan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Keterlambatan</p>
                <p className="text-xs text-gray-600">
                  Analisis pola keterlambatan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Jam Lembur</p>
                <p className="text-xs text-gray-600">
                  Tracking dan kompensasi lembur
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Leave Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Calendar className="w-5 h-5 mr-2 text-purple-600" />
              Laporan Cuti & Izin
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Penggunaan Cuti</p>
                <p className="text-xs text-gray-600">
                  Saldo dan penggunaan cuti tahunan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Pola Cuti</p>
                <p className="text-xs text-gray-600">
                  Analisis tren pengambilan cuti
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Approval Workflow</p>
                <p className="text-xs text-gray-600">Status persetujuan cuti</p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Payroll Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <DollarSign className="w-5 h-5 mr-2 text-orange-600" />
              Laporan Penggajian
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Cost Analysis</p>
                <p className="text-xs text-gray-600">
                  Analisis biaya tenaga kerja
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Salary Distribution</p>
                <p className="text-xs text-gray-600">
                  Distribusi gaji per departemen
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Tax & Benefits</p>
                <p className="text-xs text-gray-600">
                  Laporan pajak dan tunjangan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Performance Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="w-5 h-5 mr-2 text-red-600" />
              Laporan Kinerja
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">KPI Dashboard</p>
                <p className="text-xs text-gray-600">
                  Pencapaian KPI per karyawan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Performance Trends</p>
                <p className="text-xs text-gray-600">
                  Tren kinerja tim dan individu
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">360° Feedback</p>
                <p className="text-xs text-gray-600">
                  Hasil evaluasi menyeluruh
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Custom Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <FileText className="w-5 h-5 mr-2 text-indigo-600" />
              Laporan Kustom
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Report Builder</p>
                <p className="text-xs text-gray-600">
                  Buat laporan sesuai kebutuhan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Scheduled Reports</p>
                <p className="text-xs text-gray-600">
                  Laporan otomatis terjadwal
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Executive Summary</p>
                <p className="text-xs text-gray-600">
                  Ringkasan eksekutif bulanan
                </p>
              </div>
              <Eye className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-8 text-center">
          <BarChart3 className="w-16 h-16 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Advanced HR Analytics
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem pelaporan dan analitik HR yang komprehensif dengan dashboard
            interaktif, visualisasi data real-time, dan insights berbasis AI
            untuk pengambilan keputusan strategis.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            🚀 Priority High - Phase 2C Development
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsPage;
