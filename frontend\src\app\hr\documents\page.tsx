"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  FileText,
  Upload,
  Download,
  Shield,
  Search,
  Archive,
} from "lucide-react";

const DocumentsPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dokumen</h1>
        <p className="text-gray-600 mt-1">
          Manajemen dokumen karyawan digital dengan keamanan tinggi
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-slate-50 to-gray-50 border-slate-200">
        <CardContent className="p-8 text-center">
          <FileText className="w-16 h-16 text-slate-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Digital Document Management
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem manajemen dokumen digital yang aman untuk menyi<PERSON>,
            mengorganisir, dan mengelola semua dokumen karyawan dengan akses
            kontrol yang ketat.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-slate-100 text-slate-800 rounded-full text-sm font-medium">
            🚀 Priority Low - Phase 3 Development
          </div>
        </CardContent>
      </Card>

      {/* Key Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Upload className="w-5 h-5 mr-2 text-blue-600" />
              Document Upload
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Upload dokumen dengan drag & drop, support multiple format file
              dan batch upload
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Shield className="w-5 h-5 mr-2 text-red-600" />
              Security & Access Control
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Enkripsi dokumen dengan access control berlapis berdasarkan role
              dan permission
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Search className="w-5 h-5 mr-2 text-green-600" />
              Smart Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Pencarian dokumen dengan OCR dan AI untuk menemukan dokumen
              berdasarkan konten
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Download className="w-5 h-5 mr-2 text-purple-600" />
              Version Control
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Tracking versi dokumen dengan history lengkap dan kemampuan
              rollback
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Archive className="w-5 h-5 mr-2 text-orange-600" />
              Auto Archiving
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Sistem archiving otomatis berdasarkan retention policy dan
              compliance requirements
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <FileText className="w-5 h-5 mr-2 text-indigo-600" />
              Digital Signature
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Tanda tangan digital untuk dokumen kontrak dan persetujuan dengan
              validitas hukum
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Document Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Kategori Dokumen</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">
                Personal Documents
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• KTP & Identitas</li>
                <li>• CV & Portfolio</li>
                <li>• Ijazah & Sertifikat</li>
                <li>• Foto Profil</li>
              </ul>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">
                Employment Documents
              </h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Kontrak Kerja</li>
                <li>• Job Description</li>
                <li>• Performance Review</li>
                <li>• Promotion Letters</li>
              </ul>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-2">
                Legal & Compliance
              </h4>
              <ul className="text-sm text-purple-700 space-y-1">
                <li>• BPJS Documents</li>
                <li>• Tax Documents</li>
                <li>• Legal Agreements</li>
                <li>• Compliance Certificates</li>
              </ul>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-900 mb-2">
                Training & Development
              </h4>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• Training Certificates</li>
                <li>• Course Materials</li>
                <li>• Skill Assessments</li>
                <li>• Development Plans</li>
              </ul>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <h4 className="font-medium text-red-900 mb-2">
                Medical & Health
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• Medical Check-up</li>
                <li>• Health Insurance</li>
                <li>• Sick Leave Certificates</li>
                <li>• Vaccination Records</li>
              </ul>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Administrative</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Leave Applications</li>
                <li>• Expense Reports</li>
                <li>• Travel Documents</li>
                <li>• Miscellaneous</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Features */}
      <Card>
        <CardHeader>
          <CardTitle>Fitur Keamanan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Data Protection</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• AES-256 Encryption</li>
                <li>• Secure file storage</li>
                <li>• Encrypted data transmission</li>
                <li>• Regular security audits</li>
                <li>• GDPR compliance</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">Access Control</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Role-based permissions</li>
                <li>• Document-level access</li>
                <li>• Audit trail logging</li>
                <li>• Session management</li>
                <li>• Multi-factor authentication</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compliance & Retention */}
      <Card>
        <CardHeader>
          <CardTitle>Compliance & Retention Policy</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-medium text-yellow-900 mb-2">
                Retention Periods
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Personal Documents:</span>
                  <span className="text-yellow-700 ml-2">
                    5 years after termination
                  </span>
                </div>
                <div>
                  <span className="font-medium">Employment Records:</span>
                  <span className="text-yellow-700 ml-2">7 years</span>
                </div>
                <div>
                  <span className="font-medium">Tax Documents:</span>
                  <span className="text-yellow-700 ml-2">10 years</span>
                </div>
              </div>
            </div>
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">
                Compliance Standards
              </h4>
              <div className="text-sm text-blue-700">
                Sistem mengikuti standar ISO 27001, GDPR, dan regulasi
                ketenagakerjaan Indonesia untuk memastikan keamanan dan
                kepatuhan data karyawan.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentsPage;
