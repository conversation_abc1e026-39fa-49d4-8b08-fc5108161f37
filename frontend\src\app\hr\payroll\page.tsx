"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DollarSign,
  Calculator,
  FileText,
  CreditCard,
  PieChart,
  Shield,
} from "lucide-react";

const PayrollPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Penggajian</h1>
        <p className="text-gray-600 mt-1">
          Sistem penggajian otomatis dengan perhitungan pajak dan tunjangan
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
        <CardContent className="p-8 text-center">
          <DollarSign className="w-16 h-16 text-emerald-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Sistem Penggajian <PERSON>tomatis
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem payroll yang komprehensif dengan perhitungan otomatis gaji,
            tunjangan, potongan, pajak, dan integrasi dengan bank untuk transfer
            gaji.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium">
            🚀 Priority Medium - Phase 2B Development
          </div>
        </CardContent>
      </Card>

      {/* Key Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Calculator className="w-5 h-5 mr-2 text-blue-600" />
              Auto Calculation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Perhitungan otomatis gaji pokok, tunjangan, lembur, dan potongan
              berdasarkan aturan perusahaan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <FileText className="w-5 h-5 mr-2 text-green-600" />
              Payslip Generation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Generate slip gaji digital dengan detail lengkap dan dapat diunduh
              dalam format PDF
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <CreditCard className="w-5 h-5 mr-2 text-purple-600" />
              Bank Integration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Integrasi dengan sistem perbankan untuk transfer gaji otomatis ke
              rekening karyawan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <PieChart className="w-5 h-5 mr-2 text-orange-600" />
              Tax Calculation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Perhitungan pajak penghasilan (PPh 21) otomatis sesuai dengan
              peraturan perpajakan terbaru
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Shield className="w-5 h-5 mr-2 text-red-600" />
              BPJS Integration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Integrasi dengan BPJS Ketenagakerjaan dan Kesehatan untuk
              perhitungan iuran otomatis
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <DollarSign className="w-5 h-5 mr-2 text-indigo-600" />
              Bonus & Incentives
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Manajemen bonus, insentif, dan tunjangan khusus dengan aturan yang
              dapat dikustomisasi
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Salary Components */}
      <Card>
        <CardHeader>
          <CardTitle>Komponen Gaji</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-green-700">
                Pendapatan (+)
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Gaji Pokok</li>
                <li>• Tunjangan Jabatan</li>
                <li>• Tunjangan Transport</li>
                <li>• Tunjangan Makan</li>
                <li>• Tunjangan Komunikasi</li>
                <li>• Lembur</li>
                <li>• Bonus & Insentif</li>
                <li>• Tunjangan Hari Raya (THR)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3 text-red-700">Potongan (-)</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• PPh 21</li>
                <li>• BPJS Kesehatan (1%)</li>
                <li>• BPJS Ketenagakerjaan (2%)</li>
                <li>• Potongan Keterlambatan</li>
                <li>• Potongan Alpha</li>
                <li>• Pinjaman Karyawan</li>
                <li>• Potongan Lainnya</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payroll Process */}
      <Card>
        <CardHeader>
          <CardTitle>Proses Penggajian</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div className="flex-1">
                <div className="font-medium">Data Collection</div>
                <div className="text-sm text-gray-600">
                  Pengumpulan data absensi, lembur, dan adjustment
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div className="flex-1">
                <div className="font-medium">Calculation</div>
                <div className="text-sm text-gray-600">
                  Perhitungan otomatis semua komponen gaji
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div className="flex-1">
                <div className="font-medium">Review & Approval</div>
                <div className="text-sm text-gray-600">
                  Review hasil perhitungan dan approval dari management
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">
                4
              </div>
              <div className="flex-1">
                <div className="font-medium">Payment & Distribution</div>
                <div className="text-sm text-gray-600">
                  Transfer gaji dan distribusi slip gaji digital
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compliance & Security */}
      <Card>
        <CardHeader>
          <CardTitle>Kepatuhan & Keamanan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Regulatory Compliance</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• UU Ketenagakerjaan No. 13/2003</li>
                <li>• Peraturan Menteri Tenaga Kerja</li>
                <li>• Peraturan Pajak PPh 21</li>
                <li>• Regulasi BPJS</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">Data Security</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Enkripsi data gaji</li>
                <li>• Access control berlapis</li>
                <li>• Audit trail lengkap</li>
                <li>• Backup otomatis</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PayrollPage;
