// Script untuk menghapus semua data master HR
// Jalankan di browser console pada halaman HR

async function cleanupMasterData() {
  const token = localStorage.getItem("accessToken");
  
  if (!token) {
    console.error("❌ Token tidak ditemukan. Silakan login terlebih dahulu.");
    return;
  }

  console.log("🧹 Memulai pembersihan data master HR...");
  
  try {
    // 1. Hapus semua Departments
    console.log("\n📋 Menghapus semua Departments...");
    const deptResponse = await fetch('http://localhost:5000/api/hr/departments', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (deptResponse.ok) {
      const deptResult = await deptResponse.json();
      if (deptResult.success && deptResult.data.length > 0) {
        console.log(`   Ditemukan ${deptResult.data.length} departments`);
        
        for (const dept of deptResult.data) {
          try {
            const deleteResponse = await fetch(`http://localhost:5000/api/hr/departments/${dept._id}`, {
              method: 'DELETE',
              headers: { 'Authorization': `Bearer ${token}` }
            });
            
            if (deleteResponse.ok) {
              console.log(`   ✅ Deleted: ${dept.name}`);
            } else {
              console.log(`   ❌ Failed to delete: ${dept.name}`);
            }
          } catch (error) {
            console.log(`   ❌ Error deleting ${dept.name}:`, error.message);
          }
        }
      } else {
        console.log("   ℹ️  Tidak ada departments untuk dihapus");
      }
    }

    // 2. Hapus semua Divisions
    console.log("\n🏢 Menghapus semua Divisions...");
    const divResponse = await fetch('http://localhost:5000/api/hr/divisions', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (divResponse.ok) {
      const divResult = await divResponse.json();
      if (divResult.success && divResult.data.length > 0) {
        console.log(`   Ditemukan ${divResult.data.length} divisions`);
        
        for (const div of divResult.data) {
          try {
            const deleteResponse = await fetch(`http://localhost:5000/api/hr/divisions/${div._id}`, {
              method: 'DELETE',
              headers: { 'Authorization': `Bearer ${token}` }
            });
            
            if (deleteResponse.ok) {
              console.log(`   ✅ Deleted: ${div.name}`);
            } else {
              console.log(`   ❌ Failed to delete: ${div.name}`);
            }
          } catch (error) {
            console.log(`   ❌ Error deleting ${div.name}:`, error.message);
          }
        }
      } else {
        console.log("   ℹ️  Tidak ada divisions untuk dihapus");
      }
    }

    // 3. Verifikasi pembersihan
    console.log("\n🔍 Verifikasi pembersihan...");
    
    const verifyDept = await fetch('http://localhost:5000/api/hr/departments', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const verifyDiv = await fetch('http://localhost:5000/api/hr/divisions', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const deptData = await verifyDept.json();
    const divData = await verifyDiv.json();
    
    console.log(`   📋 Departments tersisa: ${deptData.data?.length || 0}`);
    console.log(`   🏢 Divisions tersisa: ${divData.data?.length || 0}`);
    
    if ((deptData.data?.length || 0) === 0 && (divData.data?.length || 0) === 0) {
      console.log("\n🎉 Pembersihan berhasil! Database master data sudah bersih.");
      console.log("✨ Siap untuk input data real.");
    } else {
      console.log("\n⚠️  Masih ada data tersisa. Mungkin perlu pembersihan manual.");
    }
    
  } catch (error) {
    console.error("❌ Error during cleanup:", error);
  }
}

// Jalankan pembersihan
cleanupMasterData();
