"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Download,
  Upload,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function BulkImportPage() {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadSuccess, setDownloadSuccess] = useState(false);

  const handleDownloadTemplate = async () => {
    try {
      setIsDownloading(true);

      const response = await fetch(
        "http://localhost:5000/api/hr/employees/template",
        {
          method: "GET",
          headers: {
            Authorization: "Bearer mock-token",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to download template");
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "Employee_Import_Template.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setDownloadSuccess(true);
      setTimeout(() => setDownloadSuccess(false), 3000);
    } catch (error) {
      console.error("Download error:", error);
      alert("Gagal mendownload template");
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Bulk Import Karyawan</h1>
          <p className="text-gray-600 mt-2">
            Import data karyawan dalam jumlah banyak menggunakan file Excel
          </p>
        </div>
      </div>

      {/* Step 1: Download Template */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              1
            </span>
            <span>Download Template Excel</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            Download template Excel yang sudah berisi format dan contoh data
            yang benar.
          </p>

          <div className="flex items-center space-x-4">
            <Button
              onClick={handleDownloadTemplate}
              disabled={isDownloading}
              className="flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>
                {isDownloading ? "Downloading..." : "Download Template"}
              </span>
            </Button>

            {downloadSuccess && (
              <Alert className="flex-1 max-w-md">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Template berhasil didownload!
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">
              Template Ultra Lengkap berisi:
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>
                • <strong>120+ kolom field ultra lengkap</strong> - SEMUA field
                database
              </li>
              <li>
                • Data Pribadi Lengkap (15 field) + Alamat Detail (6 field)
              </li>
              <li>
                • Data Kontak Lengkap (4 field) + Data Pernikahan (4 field)
              </li>
              <li>
                • Data Rekening Bank (3 field) + Data HR Lengkap (4 field)
              </li>
              <li>
                • Data Kontrak Lengkap (5 field) + Data Pendidikan Lengkap (6
                field)
              </li>
              <li>
                • Data Pangkat & Golongan (4 field) + Kontak Darurat Lengkap (5
                field)
              </li>
              <li>• Data POO/POH (2 field) + Seragam & Sepatu (2 field)</li>
              <li>
                • Data Gaji & Tunjangan Lengkap (7 field) + Jadwal Kerja (4
                field)
              </li>
              <li>
                • Data Keluarga (6 field) + Data Anak 1-4 Lengkap (20 field)
              </li>
              <li>• Data Saudara Kandung 1-4 Lengkap (24 field)</li>
              <li>
                • Format tanggal: <strong>DD-MM-YYYY</strong> (contoh:
                15-01-2024)
              </li>
              <li>• Contoh data dan petunjuk penggunaan ultra lengkap</li>
              <li>• Validasi dropdown untuk semua field yang diperlukan</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Step 2: Fill Template */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span className="bg-gray-400 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              2
            </span>
            <span>Isi Template dengan Data Karyawan</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            Buka file template yang sudah didownload dan isi dengan data
            karyawan Anda.
          </p>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Penting:</strong> Pastikan mengikuti format yang sudah
              ditentukan dan mengisi semua field yang wajib (bertanda *).
            </AlertDescription>
          </Alert>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">
              Tips pengisian:
            </h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Hapus baris contoh sebelum mengisi data</li>
              <li>
                • Gunakan format tanggal: <strong>DD-MM-YYYY</strong> (contoh:
                15-01-2024)
              </li>
              <li>• Field anak dan saudara kandung bersifat opsional</li>
              <li>• Jangan mengubah nama kolom atau sheet</li>
              <li>• Maksimal 500 baris data per upload</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Step 3: Upload File */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span className="bg-gray-400 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">
              3
            </span>
            <span>Upload File Excel</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            Upload file Excel yang sudah diisi untuk memulai proses import.
          </p>

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <FileSpreadsheet className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">
              Drag & drop file Excel atau klik untuk browse
            </p>
            <Button variant="outline" className="flex items-center space-x-2">
              <Upload className="w-4 h-4" />
              <span>Pilih File Excel</span>
            </Button>
          </div>

          <div className="text-sm text-gray-500">
            <p>Format yang didukung: .xlsx, .xls</p>
            <p>Ukuran maksimal: 10MB</p>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="bg-green-100 p-2 rounded">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Hemat Waktu</p>
                <p className="font-semibold">95% lebih cepat</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="bg-blue-100 p-2 rounded">
                <FileSpreadsheet className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Kapasitas</p>
                <p className="font-semibold">500 karyawan/upload</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="bg-purple-100 p-2 rounded">
                <AlertCircle className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Validasi</p>
                <p className="font-semibold">Auto-check data</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
