"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  TrendingUp,
  Target,
  Star,
  BarChart3,
  Users,
  Award,
} from "lucide-react";

const PerformancePage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Kinerja</h1>
        <p className="text-gray-600 mt-1">
          Sistem evaluasi dan penilaian kinerja karyawan berbasis KPI
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200">
        <CardContent className="p-8 text-center">
          <TrendingUp className="w-16 h-16 text-amber-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Performance Management System
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem manajemen kinerja yang komprehensif dengan KPI tracking,
            360-degree feedback, dan analytics untuk pengembangan karyawan.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium">
            🚀 Priority Medium - Phase 3 Development
          </div>
        </CardContent>
      </Card>

      {/* Key Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Target className="w-5 h-5 mr-2 text-blue-600" />
              KPI Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Penetapan dan tracking KPI individual dengan target yang terukur
              dan timeline yang jelas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Star className="w-5 h-5 mr-2 text-yellow-600" />
              360° Feedback
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Sistem feedback menyeluruh dari atasan, rekan kerja, dan bawahan
              untuk evaluasi objektif
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
              Performance Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Dashboard analytics dengan insights mendalam tentang tren kinerja
              dan area improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Users className="w-5 h-5 mr-2 text-purple-600" />
              Team Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Evaluasi kinerja tim dengan metrics kolaborasi dan pencapaian
              target bersama
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Award className="w-5 h-5 mr-2 text-red-600" />
              Recognition System
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Sistem penghargaan dan recognition untuk apresiasi pencapaian
              kinerja exceptional
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="w-5 h-5 mr-2 text-indigo-600" />
              Development Planning
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Perencanaan pengembangan karir berdasarkan hasil evaluasi dan
              potensi karyawan
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Cycle */}
      <Card>
        <CardHeader>
          <CardTitle>Siklus Evaluasi Kinerja</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Target className="w-6 h-6" />
              </div>
              <h4 className="font-medium text-blue-900">Goal Setting</h4>
              <p className="text-sm text-blue-700 mt-1">
                Penetapan target dan KPI
              </p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-6 h-6" />
              </div>
              <h4 className="font-medium text-green-900">Monitoring</h4>
              <p className="text-sm text-green-700 mt-1">
                Tracking progress berkala
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6" />
              </div>
              <h4 className="font-medium text-purple-900">Evaluation</h4>
              <p className="text-sm text-purple-700 mt-1">
                Penilaian komprehensif
              </p>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Award className="w-6 h-6" />
              </div>
              <h4 className="font-medium text-orange-900">Development</h4>
              <p className="text-sm text-orange-700 mt-1">
                Rencana pengembangan
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KPI Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Kategori KPI</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Productivity</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Output quality</li>
                <li>• Task completion rate</li>
                <li>• Efficiency metrics</li>
              </ul>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Quality</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Error rate</li>
                <li>• Customer satisfaction</li>
                <li>• Compliance score</li>
              </ul>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Collaboration</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Teamwork rating</li>
                <li>• Communication skills</li>
                <li>• Knowledge sharing</li>
              </ul>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Innovation</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Process improvement</li>
                <li>• Creative solutions</li>
                <li>• Initiative taking</li>
              </ul>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Leadership</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Team development</li>
                <li>• Decision making</li>
                <li>• Mentoring ability</li>
              </ul>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Professional Growth</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Skill development</li>
                <li>• Learning agility</li>
                <li>• Certification progress</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Implementation Roadmap */}
      <Card>
        <CardHeader>
          <CardTitle>Roadmap Implementasi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">
                  Phase 3A: Core Performance System
                </div>
                <div className="text-sm text-gray-600">
                  KPI setup, basic evaluation forms, scoring system
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Phase 3B: Advanced Features</div>
                <div className="text-sm text-gray-600">
                  360° feedback, analytics dashboard, automated reports
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Phase 3C: AI Integration</div>
                <div className="text-sm text-gray-600">
                  Performance predictions, development recommendations
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformancePage;
