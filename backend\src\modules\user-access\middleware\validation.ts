import { Request, Response, NextFunction } from "express";
import <PERSON><PERSON> from "joi";
import { ApiResponse } from "../../../shared/types/common";

/**
 * Generic validation middleware
 */
const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map((detail) => ({
        field: detail.path.join("."),
        message: detail.message,
        value: detail.context?.value,
      }));

      res.status(400).json({
        success: false,
        message: "Data tidak valid",
        error: "VALIDATION_ERROR",
        data: { errors },
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    next();
  };
};

/**
 * Login validation schema
 */
const loginSchema = Joi.object({
  username: Joi.string().required().messages({
    "any.required": "Username wajib diisi",
  }),
  password: Joi.string().min(6).required().messages({
    "string.min": "Password minimal 6 karakter",
    "any.required": "Password wajib diisi",
  }),
});

/**
 * Refresh token validation schema
 */
const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    "any.required": "Refresh token wajib diisi",
  }),
});

/**
 * User creation validation schema
 */
const createUserSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      "string.email": "Format email tidak valid",
      "any.required": "Email wajib diisi",
    }),
  password: Joi.string()
    .min(6)
    .pattern(new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)"))
    .required()
    .messages({
      "string.min": "Password minimal 6 karakter",
      "string.pattern.base":
        "Password harus mengandung huruf besar, huruf kecil, dan angka",
      "any.required": "Password wajib diisi",
    }),
  firstName: Joi.string().trim().min(2).max(50).required().messages({
    "string.min": "Nama depan minimal 2 karakter",
    "string.max": "Nama depan maksimal 50 karakter",
    "any.required": "Nama depan wajib diisi",
  }),
  lastName: Joi.string().trim().min(2).max(50).required().messages({
    "string.min": "Nama belakang minimal 2 karakter",
    "string.max": "Nama belakang maksimal 50 karakter",
    "any.required": "Nama belakang wajib diisi",
  }),
  employeeId: Joi.string().trim().uppercase().optional().allow(""),
  phone: Joi.string()
    .pattern(new RegExp("^(\\+62|62|0)8[1-9][0-9]{6,9}$"))
    .optional()
    .allow("")
    .messages({
      "string.pattern.base": "Format nomor telepon tidak valid",
    }),
  role: Joi.string().required().messages({
    "any.required": "Role wajib diisi",
  }),
});

/**
 * User update validation schema
 */
const updateUserSchema = Joi.object({
  firstName: Joi.string().trim().min(2).max(50).optional().messages({
    "string.min": "Nama depan minimal 2 karakter",
    "string.max": "Nama depan maksimal 50 karakter",
  }),
  lastName: Joi.string().trim().min(2).max(50).optional().messages({
    "string.min": "Nama belakang minimal 2 karakter",
    "string.max": "Nama belakang maksimal 50 karakter",
  }),
  employeeId: Joi.string().trim().uppercase().optional().allow(""),
  phone: Joi.string()
    .pattern(new RegExp("^(\\+62|62|0)8[1-9][0-9]{6,9}$"))
    .optional()
    .allow("")
    .messages({
      "string.pattern.base": "Format nomor telepon tidak valid",
    }),
  role: Joi.string().optional(),
  isActive: Joi.boolean().optional(),
});

/**
 * Change password validation schema
 */
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required().messages({
    "any.required": "Password saat ini wajib diisi",
  }),
  newPassword: Joi.string()
    .min(6)
    .pattern(new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)"))
    .required()
    .messages({
      "string.min": "Password baru minimal 6 karakter",
      "string.pattern.base":
        "Password baru harus mengandung huruf besar, huruf kecil, dan angka",
      "any.required": "Password baru wajib diisi",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("newPassword"))
    .required()
    .messages({
      "any.only": "Konfirmasi password tidak cocok",
      "any.required": "Konfirmasi password wajib diisi",
    }),
});

/**
 * Role validation schema
 */
const createRoleSchema = Joi.object({
  name: Joi.string().trim().min(2).max(50).required().messages({
    "string.min": "Nama role minimal 2 karakter",
    "string.max": "Nama role maksimal 50 karakter",
    "any.required": "Nama role wajib diisi",
  }),
  description: Joi.string().trim().max(200).optional().allow("").messages({
    "string.max": "Deskripsi maksimal 200 karakter",
  }),
  level: Joi.number().integer().min(1).max(10).required().messages({
    "number.min": "Level minimal 1",
    "number.max": "Level maksimal 10",
    "any.required": "Level wajib diisi",
  }),
  permissions: Joi.array()
    .items(
      Joi.object({
        module: Joi.string()
          .valid(
            "hr",
            "inventory",
            "mess",
            "building",
            "user-access",
            "chatting"
          )
          .required(),
        actions: Joi.object({
          create: Joi.boolean().default(false),
          read: Joi.boolean().default(false),
          update: Joi.boolean().default(false),
          delete: Joi.boolean().default(false),
          approve: Joi.boolean().default(false),
        }).required(),
        conditions: Joi.object().optional(),
      })
    )
    .required()
    .messages({
      "any.required": "Permissions wajib diisi",
    }),
});

// Export validation middlewares
export const validateLogin = validate(loginSchema);
export const validateRefreshToken = validate(refreshTokenSchema);
export const validateCreateUser = validate(createUserSchema);
export const validateUpdateUser = validate(updateUserSchema);
export const validateChangePassword = validate(changePasswordSchema);
export const validateCreateRole = validate(createRoleSchema);
export const validateUpdateRole = validate(
  createRoleSchema.fork(["name", "level", "permissions"], (schema) =>
    schema.optional()
  )
);
