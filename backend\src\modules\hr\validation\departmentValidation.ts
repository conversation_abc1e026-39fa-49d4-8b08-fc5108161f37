import Joi from "joi";

export const createDepartmentSchema = Joi.object({
  name: Joi.string().required().trim().max(100).messages({
    "string.empty": "Department name is required",
    "string.max": "Department name cannot exceed 100 characters",
    "any.required": "Department name is required",
  }),

  division: Joi.string().optional().allow("").messages({
    "string.base": "Division must be a valid ID",
  }),

  manager: Joi.string().optional().trim().max(100).allow("").messages({
    "string.max": "Manager name cannot exceed 100 characters",
  }),

  description: Joi.string().optional().trim().max(500).allow("").messages({
    "string.max": "Description cannot exceed 500 characters",
  }),

  isActive: Joi.boolean().optional().messages({
    "boolean.base": "isActive must be a boolean value",
  }),
});

export const updateDepartmentSchema = Joi.object({
  name: Joi.string().optional().trim().max(100).messages({
    "string.empty": "Department name cannot be empty",
    "string.max": "Department name cannot exceed 100 characters",
  }),

  division: Joi.string().optional().allow("").messages({
    "string.base": "Division must be a valid ID",
  }),

  manager: Joi.string().optional().trim().max(100).allow("").messages({
    "string.max": "Manager name cannot exceed 100 characters",
  }),

  description: Joi.string().optional().trim().max(500).allow("").messages({
    "string.max": "Description cannot exceed 500 characters",
  }),

  isActive: Joi.boolean().optional().messages({
    "boolean.base": "isActive must be a boolean value",
  }),
});
