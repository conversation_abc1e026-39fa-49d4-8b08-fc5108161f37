"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Users,
  Shield,
  Bell,
  Database,
  Mail,
  Calendar,
  Clock,
  FileText,
  Globe,
  Lock,
  Key,
  UserCheck,
  Workflow,
  BarChart3,
} from "lucide-react";

const SettingsPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Pengaturan HR</h1>
        <p className="text-gray-600 mt-1">
          Konfigurasi dan pengaturan sistem manajemen sumber daya manusia
        </p>
      </div>

      {/* Settings Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Settings className="w-5 h-5 mr-2 text-blue-600" />
              Pengaturan Umum
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Informasi Perusahaan</p>
                <p className="text-xs text-gray-600">
                  Nama, alamat, logo perusahaan
                </p>
              </div>
              <Settings className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Zona Waktu</p>
                <p className="text-xs text-gray-600">
                  Pengaturan zona waktu sistem
                </p>
              </div>
              <Clock className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Bahasa & Lokalisasi</p>
                <p className="text-xs text-gray-600">
                  Pengaturan bahasa dan format
                </p>
              </div>
              <Globe className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* User Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Users className="w-5 h-5 mr-2 text-green-600" />
              Manajemen Pengguna
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Role & Permissions</p>
                <p className="text-xs text-gray-600">
                  Kelola peran dan hak akses
                </p>
              </div>
              <Shield className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">User Accounts</p>
                <p className="text-xs text-gray-600">Manajemen akun pengguna</p>
              </div>
              <UserCheck className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Authentication</p>
                <p className="text-xs text-gray-600">
                  Pengaturan keamanan login
                </p>
              </div>
              <Key className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Shield className="w-5 h-5 mr-2 text-red-600" />
              Keamanan Sistem
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Password Policy</p>
                <p className="text-xs text-gray-600">
                  Kebijakan keamanan password
                </p>
              </div>
              <Lock className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Session Management</p>
                <p className="text-xs text-gray-600">
                  Pengaturan sesi pengguna
                </p>
              </div>
              <Clock className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Audit Logs</p>
                <p className="text-xs text-gray-600">Log aktivitas sistem</p>
              </div>
              <FileText className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Bell className="w-5 h-5 mr-2 text-purple-600" />
              Notifikasi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Email Notifications</p>
                <p className="text-xs text-gray-600">
                  Pengaturan notifikasi email
                </p>
              </div>
              <Mail className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Push Notifications</p>
                <p className="text-xs text-gray-600">Notifikasi real-time</p>
              </div>
              <Bell className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Notification Templates</p>
                <p className="text-xs text-gray-600">
                  Template pesan notifikasi
                </p>
              </div>
              <FileText className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* Workflow Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Workflow className="w-5 h-5 mr-2 text-orange-600" />
              Workflow & Approval
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Leave Approval</p>
                <p className="text-xs text-gray-600">Alur persetujuan cuti</p>
              </div>
              <Calendar className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Expense Approval</p>
                <p className="text-xs text-gray-600">
                  Persetujuan reimbursement
                </p>
              </div>
              <FileText className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Document Approval</p>
                <p className="text-xs text-gray-600">Persetujuan dokumen</p>
              </div>
              <UserCheck className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        {/* System Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Database className="w-5 h-5 mr-2 text-indigo-600" />
              Konfigurasi Sistem
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Database Settings</p>
                <p className="text-xs text-gray-600">Konfigurasi database</p>
              </div>
              <Database className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">Backup & Recovery</p>
                <p className="text-xs text-gray-600">
                  Pengaturan backup otomatis
                </p>
              </div>
              <Shield className="w-4 h-4 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer">
              <div>
                <p className="font-medium text-sm">System Monitoring</p>
                <p className="text-xs text-gray-600">
                  Monitoring performa sistem
                </p>
              </div>
              <BarChart3 className="w-4 h-4 text-gray-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* HR Specific Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Payroll Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <FileText className="w-5 h-5 mr-2 text-green-600" />
              Pengaturan Payroll
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">Periode Payroll</p>
                  <p className="text-xs text-gray-600">Bulanan - Tanggal 25</p>
                </div>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">PPh 21 Rate</p>
                  <p className="text-xs text-gray-600">
                    Sesuai peraturan terbaru
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">BPJS Settings</p>
                  <p className="text-xs text-gray-600">
                    Kesehatan 4%, Ketenagakerjaan 2%
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Clock className="w-5 h-5 mr-2 text-blue-600" />
              Pengaturan Absensi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">Jam Kerja</p>
                  <p className="text-xs text-gray-600">
                    08:00 - 17:00 (Senin-Jumat)
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">Toleransi Keterlambatan</p>
                  <p className="text-xs text-gray-600">15 menit</p>
                </div>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">QR Code Validity</p>
                  <p className="text-xs text-gray-600">5 menit</p>
                </div>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-gray-50 to-slate-50 border-gray-200">
        <CardContent className="p-8 text-center">
          <Settings className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Advanced System Configuration
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Pengaturan sistem yang komprehensif dengan konfigurasi lanjutan,
            integrasi API eksternal, dan customization sesuai kebutuhan
            perusahaan.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
            🚀 Priority Low - Phase 3 Development
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SettingsPage;
