"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Copy } from "lucide-react";
import { toast } from "sonner";
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker";

interface PersonalInfoData {
  // Employee Biodata
  employeeId: string;
  fullName: string;
  firstName: string;
  lastName: string;
  gender: string;
  placeOfBirth: string;
  dateOfBirth: string;
  email: string;
  phone: string;

  // Identification
  religion: string;
  bloodType: string;
  familyCardNumber: string;
  idCardNumber: string;
  taxNumber: string;
  bpjsTkNumber: string;
  nikKkNumber: string;
  taxStatus: string;

  // Current Address
  currentAddress: {
    street: string;
    city: string;
    province: string;
  };

  // ID Card Address
  idCardAddress: {
    street: string;
    city: string;
    province: string;
  };

  // Contact Information
  contact: {
    mobilePhone1: string;
    mobilePhone2: string;
    homePhone1: string;
    homePhone2: string;
  };

  // Marital Status and Children
  maritalInfo: {
    status: string;
    spouseName: string;
    spouseJob: string;
    numberOfChildren: number;
  };

  // Bank Account
  bankAccount: {
    accountNumber: string;
    accountHolder: string;
    bankName: string;
  };

  // Legacy fields for backward compatibility
  nationality?: string;
  profilePhoto?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

interface EditEmployeePersonalInfoProps {
  data: PersonalInfoData;
  onUpdate: (data: PersonalInfoData) => void;
}

const religionOptions = [
  "Islam",
  "Kristen",
  "Katolik",
  "Hindu",
  "Buddha",
  "Konghucu",
  "Lainnya",
];

const bloodTypeOptions = ["A", "B", "AB", "O"];

const genderOptions = [
  { value: "Laki-laki", label: "Laki-laki" },
  { value: "Perempuan", label: "Perempuan" },
];

const maritalStatusOptions = [
  "Belum Menikah",
  "Menikah",
  "Cerai",
  "Janda/Duda",
];

const taxStatusOptions = [
  "TK/0",
  "TK/1",
  "TK/2",
  "TK/3",
  "K/0",
  "K/1",
  "K/2",
  "K/3",
];

export default function EditEmployeePersonalInfo({
  data,
  onUpdate,
}: EditEmployeePersonalInfoProps) {
  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    let newData = { ...data };

    if (keys.length === 1) {
      newData[field as keyof PersonalInfoData] = value;
    } else if (keys.length === 2) {
      const [level1, level2] = keys;
      newData[level1 as keyof PersonalInfoData] = {
        ...newData[level1 as keyof PersonalInfoData],
        [level2]: value,
      } as any;
    } else if (keys.length === 3) {
      // Handle nested fields like contact.mobilePhone1
      const [level1, level2, level3] = keys;
      newData[level1 as keyof PersonalInfoData] = {
        ...newData[level1 as keyof PersonalInfoData],
        [level2]: {
          ...(newData[level1 as keyof PersonalInfoData] as any)?.[level2],
          [level3]: value,
        },
      } as any;
    }

    onUpdate(newData);
  };

  const copyAddressToIdCard = () => {
    onUpdate({
      ...data,
      idCardAddress: { ...data.currentAddress },
    });
    toast.success("Alamat domisili berhasil disalin ke alamat KTP");
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type: "text" | "select" | "date" | "number" = "text",
    options?: string[] | { value: string; label: string }[],
    required = false
  ) => (
    <div>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {type === "select" ? (
        <Select
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
              : (data[field as keyof PersonalInfoData] as string)
          }
          onValueChange={(value) => handleInputChange(field, value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {options?.map((option) => (
              <SelectItem
                key={typeof option === "string" ? option : option.value}
                value={typeof option === "string" ? option : option.value}
              >
                {typeof option === "string" ? option : option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : type === "date" ? (
        <EnhancedDatePicker
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
                ? new Date(
                    field.split(".").reduce((obj, key) => obj?.[key], data)
                  )
                : undefined
              : data.dateOfBirth
              ? new Date(data.dateOfBirth)
              : undefined
          }
          onChange={(date) =>
            handleInputChange(field, date ? date.toISOString() : "")
          }
          placeholder="Pilih tanggal"
          minYear={1950}
          maxYear={new Date().getFullYear()}
        />
      ) : (
        <Input
          id={field}
          type={type}
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data) || ""
              : (data[field as keyof PersonalInfoData] as string) || ""
          }
          onChange={(e) =>
            handleInputChange(
              field,
              type === "number" ? Number(e.target.value) : e.target.value
            )
          }
          placeholder={`Masukkan ${label.toLowerCase()}`}
        />
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informasi Personal</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Employee Biodata */}
        {renderFormGroup(
          "Data Karyawan",
          <>
            {renderField("Nama Lengkap", "fullName", "text", undefined, true)}
            {renderField(
              "Jenis Kelamin",
              "gender",
              "select",
              genderOptions,
              true
            )}
            {renderField(
              "Tempat Lahir",
              "placeOfBirth",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Tanggal Lahir",
              "dateOfBirth",
              "date",
              undefined,
              true
            )}
            {renderField("Email", "email", "text", undefined, false)}
          </>
        )}

        <Separator />

        {/* Identification */}
        {renderFormGroup(
          "Identifikasi",
          <>
            {renderField("Agama", "religion", "select", religionOptions, true)}
            {renderField(
              "Golongan Darah",
              "bloodType",
              "select",
              bloodTypeOptions,
              true
            )}
            {renderField(
              "Nomor KK",
              "familyCardNumber",
              "text",
              undefined,
              true
            )}
            {renderField("Nomor KTP", "idCardNumber", "text", undefined, true)}
            {renderField("NPWP", "taxNumber", "text")}
            {renderField("BPJS TK", "bpjsTkNumber", "text")}
            {renderField("NIK KK", "nikKkNumber", "text")}
            {renderField(
              "Status Pajak",
              "taxStatus",
              "select",
              taxStatusOptions,
              true
            )}
          </>
        )}

        <Separator />

        {/* Current Address */}
        {renderFormGroup(
          "Alamat Domisili",
          <>
            {renderField(
              "Jalan",
              "currentAddress.street",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Kota",
              "currentAddress.city",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Provinsi",
              "currentAddress.province",
              "text",
              undefined,
              true
            )}
            <div></div> {/* Empty div for grid alignment */}
          </>
        )}

        <Separator />

        {/* ID Card Address */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 border-b pb-2">
              Alamat KTP
            </h4>
            <Button size="sm" variant="outline" onClick={copyAddressToIdCard}>
              <Copy className="w-4 h-4 mr-2" />
              Salin dari Alamat Domisili
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderField(
              "Jalan",
              "idCardAddress.street",
              "text",
              undefined,
              true
            )}
            {renderField("Kota", "idCardAddress.city", "text", undefined, true)}
            {renderField(
              "Provinsi",
              "idCardAddress.province",
              "text",
              undefined,
              true
            )}
            <div></div> {/* Empty div for grid alignment */}
          </div>
        </div>

        <Separator />

        {/* Contact Information */}
        {renderFormGroup(
          "Informasi Kontak",
          <>
            {renderField(
              "HP 1 (Sinkron dengan Nomor Handphone di Header)",
              "contact.mobilePhone1",
              "text",
              undefined,
              true
            )}
            {renderField("HP 2", "contact.mobilePhone2", "text")}
            {renderField("Telepon Rumah 1", "contact.homePhone1", "text")}
            {renderField("Telepon Rumah 2", "contact.homePhone2", "text")}
          </>
        )}

        <Separator />

        {/* Marital Status and Children */}
        {renderFormGroup(
          "Status Pernikahan dan Anak",
          <>
            {renderField(
              "Status Pernikahan",
              "maritalInfo.status",
              "select",
              maritalStatusOptions,
              true
            )}
            {renderField("Nama Pasangan", "maritalInfo.spouseName", "text")}
            {renderField("Pekerjaan Pasangan", "maritalInfo.spouseJob", "text")}
            {renderField(
              "Jumlah Anak",
              "maritalInfo.numberOfChildren",
              "number"
            )}
          </>
        )}

        <Separator />

        {/* Bank Account */}
        {renderFormGroup(
          "Rekening Bank",
          <>
            {renderField(
              "Nomor Rekening",
              "bankAccount.accountNumber",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Pemegang Rekening",
              "bankAccount.accountHolder",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Bank",
              "bankAccount.bankName",
              "text",
              undefined,
              true
            )}
            <div></div> {/* Empty div for grid alignment */}
          </>
        )}
      </CardContent>
    </Card>
  );
}
