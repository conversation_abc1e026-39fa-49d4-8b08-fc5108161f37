"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { Separator } from "@/components/ui/separator";
import { CalendarIcon, Edit, Save, X } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface HRInfoData {
  // Basic HR Information
  department?: {
    _id: string;
    name: string;
  };
  position?: {
    _id: string;
    name: string;
  };
  tags?: Array<{
    _id: string;
    name: string;
    color: string;
  }>;
  companyEmail?: string; // Email perusahaan

  // Contract Information
  contract: {
    employmentType: string;
    hireDate: Date | undefined;
    contractDate: Date | undefined;
    contractEndDate: Date | undefined;
  };

  // Education
  education: {
    certificateLevel: string;
    fieldOfStudy: string;
    schoolName: string;
    schoolCity: string;
    description: string;
  };

  // Rank and Grade
  rank: {
    rankCategory: string;
    rankGrade: string;
    rankSubgrade: string;
    pensionFundNumber: string;
  };

  // Emergency Contact
  emergency: {
    contactName: string;
    contactPhone: string;
    contactPhone2: string;
    relationship: string;
    address: string;
  };

  // POO/POH
  location: {
    pointOfOrigin: string;
    pointOfHire: string;
  };

  // Uniform and Work Shoes
  uniform: {
    workUniformSize: string;
    workShoesSize: string;
  };
}

interface EmployeeHRInfoProps {
  data?: HRInfoData;
  onSave: (data: HRInfoData) => void;
  isEditing: boolean;
  onEditToggle: () => void;
  // Master data options
  departments?: Array<{ _id?: string; id: string; name: string }>;
  positions?: Array<{ _id?: string; id: string; name: string }>;
  tags?: Array<{ _id?: string; id: string; name: string; color: string }>;
  employmentTypes: Array<{ _id?: string; id: string; name: string }>;
  rankCategories: Array<{ _id?: string; id: string; name: string }>;
  rankGrades: Array<{ _id?: string; id: string; name: string }>;
  rankSubgrades: Array<{ _id?: string; id: string; name: string }>;
}

const certificateLevelOptions = [
  "SD",
  "SMP",
  "SMA",
  "D1",
  "D2",
  "D3",
  "S1",
  "S2",
  "S3",
];

const uniformSizeOptions = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];

const relationshipOptions = [
  "Orang Tua",
  "Saudara Kandung",
  "Pasangan",
  "Anak",
  "Kerabat",
  "Teman",
  "Lainnya",
];

export default function EmployeeHRInfo({
  data,
  onSave,
  isEditing,
  onEditToggle,
  departments = [],
  positions = [],
  tags = [],
  employmentTypes,
  rankCategories,
  rankGrades,
  rankSubgrades,
}: EmployeeHRInfoProps) {
  const [formData, setFormData] = useState<HRInfoData>({
    // Basic HR Information
    department: data?.department,
    position: data?.position,
    tags: data?.tags,
    companyEmail: data?.companyEmail || "",

    // Contract Information
    contract: {
      employmentType: data?.contract?.employmentType || "",
      hireDate: data?.contract?.hireDate,
      contractDate: data?.contract?.contractDate,
      contractEndDate: data?.contract?.contractEndDate,
    },

    // Education
    education: {
      certificateLevel: data?.education?.certificateLevel || "",
      fieldOfStudy: data?.education?.fieldOfStudy || "",
      schoolName: data?.education?.schoolName || "",
      schoolCity: data?.education?.schoolCity || "",
      description: data?.education?.description || "",
    },

    // Rank and Grade
    rank: {
      rankCategory: data?.rank?.rankCategory || "",
      rankGrade: data?.rank?.rankGrade || "",
      rankSubgrade: data?.rank?.rankSubgrade || "",
      pensionFundNumber: data?.rank?.pensionFundNumber || "",
    },

    // Emergency Contact
    emergency: {
      contactName: data?.emergency?.contactName || "",
      contactPhone: data?.emergency?.contactPhone || "",
      contactPhone2: data?.emergency?.contactPhone2 || "",
      relationship: data?.emergency?.relationship || "",
      address: data?.emergency?.address || "",
    },

    // POO/POH
    location: {
      pointOfOrigin: data?.location?.pointOfOrigin || "",
      pointOfHire: data?.location?.pointOfHire || "",
    },

    // Uniform and Work Shoes
    uniform: {
      workUniformSize: data?.uniform?.workUniformSize || "",
      workShoesSize: data?.uniform?.workShoesSize || "",
    },
  });

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    if (keys.length === 1) {
      // Handle top-level fields like companyEmail
      setFormData((prev) => ({
        ...prev,
        [keys[0]]: value,
      }));
    } else if (keys.length === 2) {
      // Handle nested fields
      setFormData((prev) => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0] as keyof HRInfoData],
          [keys[1]]: value,
        },
      }));
    }
  };

  const handleSave = () => {
    // Validate required fields
    const requiredFields = [
      { field: "contract.employmentType", label: "Jenis hubungan kerja" },
      { field: "contract.hireDate", label: "Tanggal masuk" },
      { field: "education.certificateLevel", label: "Tingkat pendidikan" },
      { field: "education.fieldOfStudy", label: "Bidang studi" },
      { field: "education.schoolName", label: "Nama sekolah" },
      { field: "education.schoolCity", label: "Kota sekolah" },
      { field: "emergency.contactName", label: "Nama kontak darurat" },
      { field: "emergency.contactPhone", label: "Telepon kontak darurat" },
      { field: "emergency.relationship", label: "Hubungan kontak darurat" },
      { field: "emergency.address", label: "Alamat kontak darurat" },
      { field: "location.pointOfOrigin", label: "Point of Origin" },
      { field: "location.pointOfHire", label: "Point of Hire" },
      { field: "uniform.workUniformSize", label: "Ukuran seragam kerja" },
      { field: "uniform.workShoesSize", label: "Ukuran sepatu kerja" },
    ];

    for (const { field, label } of requiredFields) {
      const keys = field.split(".");
      let value = formData;
      for (const key of keys) {
        value = value[key as keyof typeof value];
      }
      if (!value) {
        toast.error(`${label} harus diisi`);
        return;
      }
    }

    onSave(formData);
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type:
      | "text"
      | "select"
      | "searchable-select"
      | "date"
      | "textarea" = "text",
    options?:
      | string[]
      | { value: string; label: string }[]
      | Array<{ _id?: string; id: string; name: string }>,
    required = false
  ) => (
    <div className={type === "textarea" ? "md:col-span-2" : ""}>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {isEditing ? (
        type === "select" ? (
          <Select
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onValueChange={(value) => handleInputChange(field, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem
                  key={
                    typeof option === "string"
                      ? option
                      : "id" in option
                      ? option.id || option._id
                      : option.value
                  }
                  value={
                    typeof option === "string"
                      ? option
                      : "id" in option
                      ? option.id || option._id
                      : option.value
                  }
                >
                  {typeof option === "string"
                    ? option
                    : "id" in option
                    ? option.name
                    : option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : type === "searchable-select" ? (
          <SearchableSelect
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onValueChange={(value) => handleInputChange(field, value)}
            placeholder={`Pilih ${label.toLowerCase()}`}
            searchPlaceholder={`Cari ${label.toLowerCase()}...`}
            emptyMessage={`Tidak ada ${label.toLowerCase()} ditemukan.`}
            options={
              options?.map((option) => ({
                value:
                  typeof option === "string"
                    ? option
                    : "id" in option
                    ? option.id || option._id
                    : option.value,
                label:
                  typeof option === "string"
                    ? option
                    : "id" in option
                    ? option.name
                    : option.label,
              })) || []
            }
          />
        ) : type === "date" ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !field
                    .split(".")
                    .reduce((obj, key) => obj?.[key], formData) &&
                    "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {field.split(".").reduce((obj, key) => obj?.[key], formData) ? (
                  format(
                    field.split(".").reduce((obj, key) => obj?.[key], formData),
                    "dd MMMM yyyy",
                    { locale: id }
                  )
                ) : (
                  <span>Pilih tanggal</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={field
                  .split(".")
                  .reduce((obj, key) => obj?.[key], formData)}
                onSelect={(date) => handleInputChange(field, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : type === "textarea" ? (
          <Textarea
            id={field}
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder={`Masukkan ${label.toLowerCase()}`}
            rows={3}
          />
        ) : (
          <Input
            id={field}
            type={type}
            value={field.split(".").reduce((obj, key) => obj?.[key], formData)}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder={`Masukkan ${label.toLowerCase()}`}
          />
        )
      ) : (
        <p className="text-sm text-gray-700">
          {field.split(".").reduce((obj, key) => obj?.[key], formData) || "-"}
        </p>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Informasi HR</CardTitle>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Simpan
              </Button>
              <Button size="sm" variant="outline" onClick={onEditToggle}>
                <X className="w-4 h-4 mr-2" />
                Batal
              </Button>
            </>
          ) : (
            <Button size="sm" variant="outline" onClick={onEditToggle}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Basic HR Information */}
        {renderFormGroup(
          "Kepegawaian",
          <>
            <div>
              <Label>Departemen</Label>
              <p className="text-sm text-gray-700">
                {data?.department?.name || "-"}
              </p>
            </div>
            <div>
              <Label>Posisi Jabatan</Label>
              <p className="text-sm text-gray-700">
                {data?.position?.name || "-"}
              </p>
            </div>
            {renderField("Email Perusahaan", "companyEmail", "text")}
            {data?.tags && data.tags.length > 0 && (
              <div className="md:col-span-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {data.tags.map((tag) => (
                    <span
                      key={tag._id}
                      className="px-2 py-1 text-xs rounded-full"
                      style={{
                        backgroundColor: tag.color + "20",
                        color: tag.color,
                        border: `1px solid ${tag.color}40`,
                      }}
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </>
        )}

        <Separator />

        {/* Contract Information */}
        {renderFormGroup(
          "Kontrak",
          <>
            {renderField(
              "Jenis Hubungan Kerja",
              "contract.employmentType",
              "searchable-select",
              employmentTypes,
              true
            )}
            {renderField(
              "Tanggal Masuk",
              "contract.hireDate",
              "date",
              undefined,
              true
            )}
            {renderField("Tanggal Kontrak", "contract.contractDate", "date")}
            {renderField(
              "Tanggal Akhir Kontrak",
              "contract.contractEndDate",
              "date"
            )}
          </>
        )}

        <Separator />

        {/* Education */}
        {renderFormGroup(
          "Pendidikan",
          <>
            {renderField(
              "Tingkat Pendidikan",
              "education.certificateLevel",
              "select",
              certificateLevelOptions,
              true
            )}
            {renderField(
              "Bidang Studi",
              "education.fieldOfStudy",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Sekolah",
              "education.schoolName",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Kota Sekolah",
              "education.schoolCity",
              "text",
              undefined,
              true
            )}
            {renderField("Keterangan", "education.description", "textarea")}
          </>
        )}

        <Separator />

        {/* Rank and Grade */}
        {renderFormGroup(
          "Pangkat dan Golongan",
          <>
            {renderField(
              "Kategori Pangkat",
              "rank.rankCategory",
              "searchable-select",
              rankCategories
            )}
            {renderField(
              "Golongan Pangkat",
              "rank.rankGrade",
              "searchable-select",
              rankGrades
            )}
            {renderField(
              "Sub Golongan Pangkat",
              "rank.rankSubgrade",
              "searchable-select",
              rankSubgrades
            )}
            {renderField("No Dana Pensiun", "rank.pensionFundNumber", "text")}
          </>
        )}

        <Separator />

        {/* Emergency Contact */}
        {renderFormGroup(
          "Kontak Darurat",
          <>
            {renderField(
              "Nama Kontak",
              "emergency.contactName",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Telepon Kontak",
              "emergency.contactPhone",
              "text",
              undefined,
              true
            )}
            {renderField("Telepon Kontak 2", "emergency.contactPhone2", "text")}
            {renderField(
              "Hubungan",
              "emergency.relationship",
              "select",
              relationshipOptions,
              true
            )}
            {renderField(
              "Alamat",
              "emergency.address",
              "textarea",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* POO/POH */}
        {renderFormGroup(
          "POO/POH",
          <>
            {renderField(
              "Point of Origin (POO)",
              "location.pointOfOrigin",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Point of Hire (POH)",
              "location.pointOfHire",
              "text",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* Uniform and Work Shoes */}
        {renderFormGroup(
          "Seragam dan Sepatu Kerja",
          <>
            {renderField(
              "Ukuran Seragam Kerja",
              "uniform.workUniformSize",
              "select",
              uniformSizeOptions,
              true
            )}
            {renderField(
              "Ukuran Sepatu Kerja",
              "uniform.workShoesSize",
              "text",
              undefined,
              true
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
