import { Request, Response, NextFunction } from "express";
import { JWTService, JWTPayload } from "../../../config/jwt";
import { User, IUser } from "../models/User";
import { IRole } from "../models/Role";

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      userRole?: IRole;
      permissions?: string[];
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: IUser;
  userRole: IRole;
  permissions: string[];
}

/**
 * Authentication middleware - verifies JWT token
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      res.status(401).json({
        success: false,
        message: "Token akses tidak ditemukan",
        error: "MISSING_TOKEN",
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    let decoded: JWTPayload;
    try {
      decoded = JWTService.verifyAccessToken(token);
    } catch (error) {
      res.status(401).json({
        success: false,
        message: error instanceof Error ? error.message : "Token tidak valid",
        error: "INVALID_TOKEN",
      });
      return;
    }

    // Get user from database
    const user = await User.findById(decoded.userId)
      .populate("role")
      .select("+password");

    if (!user) {
      res.status(401).json({
        success: false,
        message: "User tidak ditemukan",
        error: "USER_NOT_FOUND",
      });
      return;
    }

    // Check if user is active
    if (!user.isActive) {
      res.status(401).json({
        success: false,
        message: "Akun tidak aktif",
        error: "ACCOUNT_INACTIVE",
      });
      return;
    }

    // Check if user is deleted
    if (user.isDeleted) {
      res.status(401).json({
        success: false,
        message: "Akun telah dihapus",
        error: "ACCOUNT_DELETED",
      });
      return;
    }

    // Check if account is locked
    if (user.isAccountLocked()) {
      res.status(401).json({
        success: false,
        message: "Akun terkunci karena terlalu banyak percobaan login",
        error: "ACCOUNT_LOCKED",
      });
      return;
    }

    // Check if password was changed after token was issued
    const tokenIssuedAt = new Date(decoded.iat! * 1000);
    if (user.passwordChangedAt && user.passwordChangedAt > tokenIssuedAt) {
      res.status(401).json({
        success: false,
        message: "Password telah diubah, silakan login ulang",
        error: "PASSWORD_CHANGED",
      });
      return;
    }

    // Get user role and permissions
    const userRole = user.role as any as IRole;
    if (!userRole || !userRole.isActive) {
      res.status(403).json({
        success: false,
        message: "Role tidak aktif",
        error: "ROLE_INACTIVE",
      });
      return;
    }

    // Attach user data to request
    req.user = user;
    req.userRole = userRole;
    req.permissions = userRole.getPermissionsList();

    // Update last activity
    user.lastLoginAt = new Date();
    user.lastLoginIP = req.ip || "";
    await user.save();

    next();
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(500).json({
      success: false,
      message: "Terjadi kesalahan saat verifikasi autentikasi",
      error: "AUTHENTICATION_ERROR",
    });
  }
};

/**
 * Authorization middleware - checks user permissions
 */
export const authorize = (module: string, action: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const authReq = req as AuthenticatedRequest;

      if (!authReq.user || !authReq.userRole) {
        res.status(401).json({
          success: false,
          message: "User tidak terautentikasi",
          error: "NOT_AUTHENTICATED",
        });
        return;
      }

      // Check if user has required permission
      const hasPermission = authReq.userRole.hasPermission(module, action);

      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: `Anda tidak memiliki izin untuk ${action} pada modul ${module}`,
          error: "INSUFFICIENT_PERMISSIONS",
        });
        return;
      }

      next();
    } catch (error) {
      console.error("Authorization error:", error);
      res.status(500).json({
        success: false,
        message: "Terjadi kesalahan saat verifikasi otorisasi",
        error: "AUTHORIZATION_ERROR",
      });
    }
  };
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const authReq = req as AuthenticatedRequest;

      if (!authReq.user || !authReq.userRole) {
        res.status(401).json({
          success: false,
          message: "User tidak terautentikasi",
          error: "NOT_AUTHENTICATED",
        });
        return;
      }

      const userRoleName = authReq.userRole.name;

      if (!allowedRoles.includes(userRoleName)) {
        res.status(403).json({
          success: false,
          message: `Role ${userRoleName} tidak diizinkan mengakses resource ini`,
          error: "ROLE_NOT_ALLOWED",
        });
        return;
      }

      next();
    } catch (error) {
      console.error("Role authorization error:", error);
      res.status(500).json({
        success: false,
        message: "Terjadi kesalahan saat verifikasi role",
        error: "ROLE_AUTHORIZATION_ERROR",
      });
    }
  };
};

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return next();
    }

    const token = authHeader.substring(7);

    try {
      const decoded = JWTService.verifyAccessToken(token);
      const user = await User.findById(decoded.userId).populate("role");

      if (user && user.isActive && !user.isDeleted) {
        req.user = user;
        req.userRole = user.role as any as IRole;
        req.permissions = (user.role as any as IRole).getPermissionsList();
      }
    } catch (error) {
      // Ignore token errors in optional auth
    }

    next();
  } catch (error) {
    next();
  }
};
