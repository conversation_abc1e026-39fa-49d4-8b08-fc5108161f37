# Master Data Guidelines - BIS System

## 🎯 Tujuan
Dokumen ini memastikan konsistensi penggunaan master data di seluruh sistem BIS, mencegah error seperti yang terjadi di department edit form.

## 🔧 Struktur Data

### Backend Models
Semua model master data menggunakan transform yang konsisten:

```typescript
toJSON: {
  transform: function (_doc, ret) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  },
}
```

### Frontend Interfaces
Semua interface master data harus mendukung kedua format:

```typescript
export interface MasterDataItem {
  _id?: string; // For backward compatibility
  id: string;   // Primary ID field from backend transform
  name: string;
  // ... other fields
}
```

## 📋 Master Data Types

### 1. Department
- **Backend**: `/api/hr/departments`
- **Model**: `Department.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `manager`, `description`, `isActive`

### 2. Position
- **Backend**: `/api/hr/positions`
- **Model**: `Position.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `department`, `description`, `isActive`

### 3. Division
- **Backend**: `/api/hr/divisions`
- **Model**: `Division.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `description`, `isActive`

### 4. Employment Type
- **Backend**: `/api/hr/employment-types`
- **Model**: `EmploymentType.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `description`, `isActive`

### 5. Rank Category
- **Backend**: `/api/hr/rank-categories`
- **Model**: `RankCategory.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `description`, `isActive`

### 6. Rank Grade
- **Backend**: `/api/hr/rank-grades`
- **Model**: `RankGrade.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `description`, `isActive`

### 7. Rank Subgrade
- **Backend**: `/api/hr/rank-subgrades`
- **Model**: `RankSubgrade.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `description`, `isActive`

### 8. Tag
- **Backend**: `/api/hr/tags`
- **Model**: `Tag.ts`
- **Transform**: ✅ Ada
- **Fields**: `id`, `name`, `color`, `description`, `isActive`

## 🛠️ Best Practices

### 1. Menggunakan Master Data di Forms

**❌ JANGAN:**
```typescript
// Hardcode options
const options = [
  { value: "OPERATIONAL", label: "Operational" },
  { value: "SUPPORT", label: "Support" }
];

// Menggunakan nama sebagai value
<option value={division.name}>{division.name}</option>
```

**✅ LAKUKAN:**
```typescript
// Gunakan utility functions
import { mapToDropdownOptions, getMasterDataId } from '@/utils/masterDataUtils';

// Untuk dropdown biasa
const options = mapToDropdownOptions(divisions);

// Untuk SearchableSelect
<SearchableSelect
  options={divisions.map((division) => ({
    value: getMasterDataId(division),
    label: division.name,
  }))}
/>
```

### 2. Validasi Data Sebelum Submit

```typescript
import { validateMasterDataIds } from '@/utils/masterDataUtils';

const handleSubmit = async (formData: any) => {
  // Validasi master data IDs
  const validation = validateMasterDataIds(formData, masterData);
  
  if (!validation.isValid) {
    toast.error("Data tidak valid", {
      description: validation.errors.join(", ")
    });
    return;
  }
  
  // Lanjutkan submit...
};
```

### 3. Debug Master Data

```typescript
import { debugMasterData } from '@/utils/masterDataUtils';

// Debug saat development
if (process.env.NODE_ENV === 'development') {
  debugMasterData(masterData, 'Employee Form Master Data');
}
```

## 🚨 Common Errors & Solutions

### Error 1: "Cast to ObjectId failed"
**Penyebab**: Mengirim nama/string bukan ObjectId
**Solusi**: Gunakan `getMasterDataId()` untuk mendapatkan ID yang benar

### Error 2: "Department not found"
**Penyebab**: ID tidak valid atau data tidak sinkron
**Solusi**: Validasi dengan `validateMasterDataIds()` sebelum submit

### Error 3: Dropdown tidak bisa dipilih
**Penyebab**: Event handler tidak benar atau value mapping salah
**Solusi**: Gunakan `mapToDropdownOptions()` atau `mapToSearchableSelectOptions()`

## 📝 Checklist Development

Saat membuat form yang menggunakan master data:

- [ ] ✅ Interface mendukung `id` dan `_id`
- [ ] ✅ Gunakan utility functions untuk mapping
- [ ] ✅ Validasi data sebelum submit
- [ ] ✅ Handle loading state untuk master data
- [ ] ✅ Error handling yang proper
- [ ] ✅ Debug logs untuk development
- [ ] ✅ Test dengan data real dari API

## 🔄 Update Process

Jika menambah master data baru:

1. **Backend**: Buat model dengan transform yang konsisten
2. **Frontend**: Tambah interface di `masterDataService.ts`
3. **Utility**: Update `validateMasterDataIds()` jika perlu
4. **Documentation**: Update dokumen ini

## 📞 Support

Jika mengalami masalah dengan master data:

1. Cek console logs untuk error details
2. Gunakan `debugMasterData()` untuk inspect data
3. Pastikan backend API mengembalikan data dengan format yang benar
4. Verifikasi transform di model backend

---

**Ingat**: Konsistensi adalah kunci! Selalu gunakan utility functions yang sudah disediakan untuk menghindari error yang sama terulang.
