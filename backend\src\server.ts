import dotenv from "dotenv";
import App from "./app";

// Load environment variables
dotenv.config();

// Handle uncaught exceptions
process.on("uncaughtException", (err: Error) => {
  console.error("UNCAUGHT EXCEPTION! 💥 Shutting down...");
  console.error("Error:", err.name, err.message);
  console.error("Stack:", err.stack);
  process.exit(1);
});

// Create and start the application - BIS Backend
const app = new App();
app.listen();

// Handle unhandled promise rejections
process.on("unhandledRejection", (err: Error) => {
  console.error("UNHANDLED REJECTION! 💥 Shutting down...");
  console.error("Error:", err.name, err.message);
  console.error("Stack:", err.stack);

  process.exit(1);
});

// Handle SIGTERM signal
process.on("SIGTERM", () => {
  console.log("👋 SIGTERM RECEIVED. Shutting down gracefully");
  process.exit(0);
});

// Handle SIGINT signal (Ctrl+C)
process.on("SIGINT", () => {
  console.log("👋 SIGINT RECEIVED. Shutting down gracefully");
  process.exit(0);
});
