import mongoose, { Schema, Types } from "mongoose";
import { BaseDocument, TimestampFields } from "../../../shared/types/common";

export interface IPermission {
  module: string;
  actions: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
    approve?: boolean;
  };
  conditions?: Record<string, any>;
}

export interface IRole extends BaseDocument, TimestampFields {
  name: string;
  description?: string;
  permissions: IPermission[];
  isActive: boolean;
  isSystem: boolean; // System roles cannot be deleted
  level: number; // Role hierarchy level (1 = highest)
  createdBy?: Types.ObjectId;
  updatedBy?: Types.ObjectId;

  // Methods
  hasPermission(module: string, action: string): boolean;
  getPermissionsList(): string[];
}

const permissionSchema = new Schema<IPermission>(
  {
    module: {
      type: String,
      required: [true, "Module wajib diisi"],
      enum: ["hr", "inventory", "mess", "building", "user-access", "chatting"],
      trim: true,
    },
    actions: {
      create: { type: Boolean, default: false },
      read: { type: Boolean, default: false },
      update: { type: Boolean, default: false },
      delete: { type: Boolean, default: false },
      approve: { type: Boolean, default: false },
    },
    conditions: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  { _id: false }
);

const roleSchema = new Schema<IRole>(
  {
    name: {
      type: String,
      required: [true, "Nama role wajib diisi"],
      unique: true,
      trim: true,
      maxlength: [50, "Nama role maksimal 50 karakter"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, "Deskripsi maksimal 200 karakter"],
    },
    permissions: [permissionSchema],
    isActive: {
      type: Boolean,
      default: true,
    },
    isSystem: {
      type: Boolean,
      default: false,
    },
    level: {
      type: Number,
      required: [true, "Level role wajib diisi"],
      min: [1, "Level minimal 1"],
      max: [10, "Level maksimal 10"],
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
roleSchema.index({ name: 1 });
roleSchema.index({ isActive: 1 });
roleSchema.index({ level: 1 });
roleSchema.index({ createdAt: -1 });

// Virtual for users count
roleSchema.virtual("usersCount", {
  ref: "User",
  localField: "_id",
  foreignField: "role",
  count: true,
});

// Instance methods
roleSchema.methods.hasPermission = function (
  module: string,
  action: string
): boolean {
  const modulePermission = this.permissions.find(
    (p: IPermission) => p.module === module
  );
  if (!modulePermission) return false;

  return (
    modulePermission.actions[action as keyof typeof modulePermission.actions] ||
    false
  );
};

roleSchema.methods.getPermissionsList = function (): string[] {
  const permissions: string[] = [];

  this.permissions.forEach((permission: IPermission) => {
    Object.entries(permission.actions).forEach(([action, allowed]) => {
      if (allowed) {
        permissions.push(`${permission.module}:${action}`);
      }
    });
  });

  return permissions;
};

// Static methods
roleSchema.statics.findByName = function (name: string) {
  return this.findOne({ name: name.trim(), isActive: true });
};

roleSchema.statics.findSystemRoles = function () {
  return this.find({ isSystem: true, isActive: true }).sort({ level: 1 });
};

roleSchema.statics.findUserRoles = function () {
  return this.find({ isSystem: false, isActive: true }).sort({ level: 1 });
};

// Pre-save middleware
roleSchema.pre("save", function (next) {
  // Ensure system roles cannot be deactivated
  if (this.isSystem && !this.isActive) {
    this.isActive = true;
  }
  next();
});

// Pre-remove middleware
roleSchema.pre(
  "deleteOne",
  { document: true, query: false },
  async function (next) {
    // Prevent deletion of system roles
    if (this.isSystem) {
      throw new Error("Role sistem tidak dapat dihapus");
    }

    // Check if role is being used by users
    const User = mongoose.model("User");
    const usersCount = await User.countDocuments({
      role: this._id,
      isDeleted: false,
    });

    if (usersCount > 0) {
      throw new Error(
        `Role tidak dapat dihapus karena masih digunakan oleh ${usersCount} user`
      );
    }

    next();
  }
);

export const Role = mongoose.model<IRole>("Role", roleSchema);
export default Role;
