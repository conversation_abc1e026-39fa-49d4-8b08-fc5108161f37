import mongoose, { Schema, Document } from "mongoose";

export interface IRankGrade extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  deletedBy?: mongoose.Types.ObjectId;
}

const RankGradeSchema = new Schema<IRankGrade>(
  {
    name: {
      type: String,
      required: [true, "Rank grade name is required"],
      trim: true,
      unique: true,
      maxlength: [100, "Rank grade name cannot exceed 100 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes
RankGradeSchema.index({ name: 1 }, { unique: true });
RankGradeSchema.index({ isDeleted: 1 });
RankGradeSchema.index({ isActive: 1 });

// Pre-save middleware
RankGradeSchema.pre("save", function (next) {
  if (this.isDeleted && !this.deletedAt) {
    this.deletedAt = new Date();
  }
  next();
});

const RankGrade = mongoose.model<IRankGrade>("RankGrade", RankGradeSchema);

export default RankGrade;
