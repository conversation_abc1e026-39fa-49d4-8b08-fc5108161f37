@echo off
setlocal enabledelayedexpansion

:: BIS Service Manager - Batch Version
:: Usage: service-manager.bat [start|stop|restart|status|kill-all] [backend|frontend|both]

set BACKEND_PORT=5000
set FRONTEND_PORT=3000

if "%1"=="" (
    echo.
    echo 🔧 BIS Service Manager
    echo =====================
    echo.
    echo Usage: %0 [action] [service]
    echo.
    echo Actions:
    echo   start    - Mulai service
    echo   stop     - Hentikan service
    echo   restart  - Restart service
    echo   status   - Lihat status service
    echo   kill-all - Hentikan semua Node.js process
    echo.
    echo Services:
    echo   backend  - Backend service saja
    echo   frontend - Frontend service saja
    echo   both     - Kedua service (default)
    echo.
    echo Contoh:
    echo   %0 start both
    echo   %0 stop backend
    echo   %0 status
    echo.
    goto :eof
)

set ACTION=%1
set SERVICE=%2
if "%SERVICE%"=="" set SERVICE=both

echo.
echo 🔧 BIS Service Manager
echo ======================

if "%ACTION%"=="status" goto :status
if "%ACTION%"=="stop" goto :stop
if "%ACTION%"=="start" goto :start
if "%ACTION%"=="restart" goto :restart
if "%ACTION%"=="kill-all" goto :kill-all

echo ❌ Action tidak valid: %ACTION%
goto :eof

:status
echo.
echo 📊 STATUS SERVICE BIS
echo =====================
call :check_port %BACKEND_PORT% "Backend"
call :check_port %FRONTEND_PORT% "Frontend"
echo.
goto :eof

:stop
echo.
echo 🔴 Menghentikan service...
if "%SERVICE%"=="backend" (
    call :kill_port %BACKEND_PORT% "Backend"
) else if "%SERVICE%"=="frontend" (
    call :kill_port %FRONTEND_PORT% "Frontend"
) else (
    call :kill_port %BACKEND_PORT% "Backend"
    call :kill_port %FRONTEND_PORT% "Frontend"
)
timeout /t 2 /nobreak >nul
goto :status

:start
echo.
echo 🚀 Memulai service...
call :status
if "%SERVICE%"=="backend" (
    call :start_backend
) else if "%SERVICE%"=="frontend" (
    call :start_frontend
) else (
    call :start_backend
    timeout /t 3 /nobreak >nul
    call :start_frontend
)
timeout /t 3 /nobreak >nul
goto :status

:restart
echo.
echo 🔄 Restart service...
if "%SERVICE%"=="backend" (
    call :kill_port %BACKEND_PORT% "Backend"
    timeout /t 2 /nobreak >nul
    call :start_backend
) else if "%SERVICE%"=="frontend" (
    call :kill_port %FRONTEND_PORT% "Frontend"
    timeout /t 2 /nobreak >nul
    call :start_frontend
) else (
    call :kill_port %BACKEND_PORT% "Backend"
    call :kill_port %FRONTEND_PORT% "Frontend"
    timeout /t 3 /nobreak >nul
    call :start_backend
    timeout /t 3 /nobreak >nul
    call :start_frontend
)
timeout /t 3 /nobreak >nul
goto :status

:kill-all
echo.
echo 💀 Menghentikan semua Node.js process...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM "next-server" 2>nul
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000 " ^| findstr "LISTENING"') do (
    taskkill /F /PID %%a 2>nul
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3001 " ^| findstr "LISTENING"') do (
    taskkill /F /PID %%a 2>nul
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5000 " ^| findstr "LISTENING"') do (
    taskkill /F /PID %%a 2>nul
)
echo ✅ Semua Node.js process dihentikan
timeout /t 2 /nobreak >nul
goto :status

:check_port
set PORT=%1
set SERVICE_NAME=%2
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%PORT% " ^| findstr "LISTENING"') do (
    set PID=%%a
    echo 🟢 %SERVICE_NAME%: RUNNING ^(PID: !PID!, Port: %PORT%^)
    goto :eof
)
echo 🔴 %SERVICE_NAME%: STOPPED ^(Port: %PORT%^)
goto :eof

:kill_port
set PORT=%1
set SERVICE_NAME=%2
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%PORT% " ^| findstr "LISTENING"') do (
    set PID=%%a
    echo 🔴 Menghentikan %SERVICE_NAME% ^(PID: !PID!, Port: %PORT%^)...
    taskkill /F /PID !PID! 2>nul
    if !errorlevel! equ 0 (
        echo ✅ %SERVICE_NAME% berhasil dihentikan
    ) else (
        echo ❌ Gagal menghentikan %SERVICE_NAME%
    )
    goto :eof
)
echo ℹ️  %SERVICE_NAME% tidak berjalan di port %PORT%
goto :eof

:start_backend
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%BACKEND_PORT% " ^| findstr "LISTENING"') do (
    echo ⚠️  Backend sudah berjalan di port %BACKEND_PORT%
    goto :eof
)
echo 🚀 Memulai Backend Service...
cd backend
start "BIS Backend" cmd /k "npm run dev"
cd ..
echo ⏳ Menunggu backend service siap...
timeout /t 5 /nobreak >nul
goto :eof

:start_frontend
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%FRONTEND_PORT% " ^| findstr "LISTENING"') do (
    echo ⚠️  Frontend sudah berjalan di port %FRONTEND_PORT%
    goto :eof
)
echo 🚀 Memulai Frontend Service...
cd frontend
start "BIS Frontend" cmd /k "npm run dev"
cd ..
echo ⏳ Menunggu frontend service siap...
timeout /t 8 /nobreak >nul
goto :eof
