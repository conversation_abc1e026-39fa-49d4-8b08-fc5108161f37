"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Briefcase,
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface EmploymentType {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const EmploymentTypesPage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [employmentTypes, setEmploymentTypes] = useState<EmploymentType[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch employment types from API
  const fetchEmploymentTypes = async () => {
    try {
      setIsLoading(true);

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        "http://localhost:5000/api/hr/employment-types",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success) {
        setEmploymentTypes(result.data || []);
      } else {
        toast.error("Gagal memuat data jenis hubungan kerja", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error fetching employment types:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmploymentTypes();
  }, []);

  const filteredEmploymentTypes = employmentTypes.filter(
    (type) =>
      type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (type.description &&
        type.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleEdit = (id: string) => {
    router.push(`/hr/employment-types/edit/${id}`);
  };

  const handleDelete = async (id: string, name: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Menghapus jenis hubungan kerja...", {
        description: `Sedang menghapus ${name}`,
      });

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.dismiss(loadingToast);
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/employment-types/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success("Jenis hubungan kerja berhasil dihapus!", {
          description: `${name} telah dinonaktifkan dari sistem`,
          action: {
            label: "Undo",
            onClick: () => {
              toast.info("Fitur undo akan segera tersedia");
            },
          },
        });

        // Refresh data
        await fetchEmploymentTypes();
      } else {
        toast.error("Gagal menghapus jenis hubungan kerja", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error deleting employment type:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Jenis Hubungan Kerja
            </h1>
            <p className="text-gray-600 mt-1">
              Kelola jenis hubungan kerja karyawan
            </p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => router.push("/hr/employment-types/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Jenis Hubungan
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Buat jenis hubungan kerja baru</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari jenis hubungan kerja..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">
                {filteredEmploymentTypes.length} jenis hubungan
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">
              Memuat data jenis hubungan kerja...
            </span>
          </div>
        ) : (
          <>
            {/* Employment Types Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredEmploymentTypes.map((type) => (
                <Card
                  key={type.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-indigo-100 rounded-lg">
                          <Briefcase className="w-6 h-6 text-indigo-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{type.name}</CardTitle>
                        </div>
                      </div>
                      <Badge
                        variant={type.isActive ? "default" : "secondary"}
                        className={
                          type.isActive ? "bg-green-100 text-green-800" : ""
                        }
                      >
                        {type.isActive ? "Aktif" : "Tidak Aktif"}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600">
                      {type.description || "Tidak ada deskripsi"}
                    </p>

                    <div className="flex items-center text-xs text-gray-500">
                      <Calendar className="w-3 h-3 mr-1" />
                      Dibuat:{" "}
                      {new Date(type.createdAt).toLocaleDateString("id-ID")}
                    </div>

                    <div className="flex space-x-2 pt-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(type.id)}
                            className="flex-1"
                          >
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Edit jenis hubungan kerja</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(type.id, type.name)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Hapus jenis hubungan kerja</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {filteredEmploymentTypes.length === 0 && !isLoading && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Tidak ada jenis hubungan kerja
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm
                      ? "Coba sesuaikan kata kunci pencarian."
                      : "Mulai dengan membuat jenis hubungan kerja pertama."}
                  </p>
                  {!searchTerm && (
                    <Button
                      onClick={() => router.push("/hr/employment-types/add")}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Tambah Jenis Hubungan
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </TooltipProvider>
  );
};

export default EmploymentTypesPage;
