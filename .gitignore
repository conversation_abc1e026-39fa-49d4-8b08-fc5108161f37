# Dependencies
node_modules/
*/node_modules/

# Environment files
.env
.env.local
.env.production
.env.test
*/.env
*/.env.local
*/.env.production
*/.env.test

# Build outputs
dist/
build/
.next/
*/dist/
*/build/
*/.next/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*/coverage/
*.lcov

# nyc test coverage
.nyc_output/
*/.nyc_output/

# Uploads
uploads/
*/uploads/

# Temporary folders
tmp/
temp/
*/tmp/
*/temp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
.parcel-cache/
*/.cache/
*/.parcel-cache/

# TypeScript
*.tsbuildinfo
*/next-env.d.ts

# Optional npm cache directory
.npm/
*/.npm/

# Optional eslint cache
.eslintcache
*/.eslintcache

# Yarn
.yarn-integrity
*/.yarn-integrity

# Package lock files (choose one)
# package-lock.json
# yarn.lock

# Testing
.nyc_output/
coverage/

# Storybook
.out/
.storybook-out/

# Vercel
.vercel/
*/.vercel/
