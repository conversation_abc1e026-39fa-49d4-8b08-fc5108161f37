const mongoose = require("mongoose");

// Connect to MongoDB
mongoose.connect("mongodb://localhost:27017/psg-sisinfo");

const employeeSchema = new mongoose.Schema({}, { strict: false });
const Employee = mongoose.model("Employee", employeeSchema, "employees");

async function debugPhotos() {
  try {
    console.log("🔍 Searching for employee LUSIUS FEBRIANUS MARO...");

    // Find specific employee
    const lusius = await Employee.findOne({
      "personal.fullName": { $regex: /LUSIUS.*MARO/i },
    });

    if (lusius) {
      console.log("📊 Found employee:");
      console.log("Name:", lusius.personal?.fullName);
      console.log("ID:", lusius.personal?.employeeId);
      console.log(
        "Photo:",
        lusius.personal?.profilePhoto
          ? lusius.personal.profilePhoto.substring(0, 100) + "..."
          : "No photo"
      );
      console.log("Photo type:", typeof lusius.personal?.profilePhoto);
      console.log(
        "Is blob URL:",
        lusius.personal?.profilePhoto?.startsWith("blob:")
      );

      if (lusius.personal?.profilePhoto?.startsWith("blob:")) {
        console.log("\n🧹 Cleaning up blob URL for this employee...");

        const result = await Employee.updateOne(
          { _id: lusius._id },
          { $unset: { "personal.profilePhoto": "" } }
        );

        console.log("✅ Cleanup result:", result);
      }
    } else {
      console.log("❌ Employee LUSIUS FEBRIANUS MARO not found");
    }

    console.log("\n🔍 Searching for ALL employees with blob URLs...");

    // Find all employees with any kind of blob URL
    const allEmployees = await Employee.find({});

    let blobCount = 0;
    allEmployees.forEach((emp) => {
      if (
        emp.personal?.profilePhoto &&
        emp.personal.profilePhoto.includes("blob:")
      ) {
        blobCount++;
        console.log(`${blobCount}. ${emp.personal?.fullName || "Unknown"}`);
        console.log(
          `   Photo: ${emp.personal.profilePhoto.substring(0, 80)}...`
        );
      }
    });

    if (blobCount === 0) {
      console.log("✅ No blob URLs found in any employee");
    } else {
      console.log(`\n🧹 Cleaning up ${blobCount} employees with blob URLs...`);

      const result = await Employee.updateMany(
        { "personal.profilePhoto": { $regex: /blob:/ } },
        { $unset: { "personal.profilePhoto": "" } }
      );

      console.log("✅ Cleanup completed:", result);
    }
  } catch (error) {
    console.error("❌ Error during debug:", error);
  } finally {
    mongoose.connection.close();
    console.log("🔌 Database connection closed");
  }
}

// Run debug
debugPhotos();
