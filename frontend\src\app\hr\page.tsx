"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  UserPlus,
  Building2,
  Building,
  Award,
  FileText,
  Calendar,
  BarChart3,
  Settings,
  UserCheck,
  UserX,
  DollarSign,
  TrendingUp,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { employeeService } from "@/services/employeeService";

interface EmployeeStats {
  overview: {
    total: number;
    active: number;
    inactive: number;
    avgSalary: number;
  };
  byDepartment: Array<{
    _id: string;
    count: number;
  }>;
}

const HRDashboard = () => {
  const router = useRouter();
  const [stats, setStats] = useState<EmployeeStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await employeeService.getEmployeeStats();
        if (response.success && response.data) {
          setStats(response.data);
        }
      } catch (error) {
        console.error("Error fetching employee stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const menuItems = [
    {
      title: "Master Data",
      description: "Kelola data master HR",
      icon: Settings,
      color: "bg-blue-500",
      items: [
        { name: "Divisi", path: "/hr/divisions", icon: Building },
        { name: "Departemen", path: "/hr/departments", icon: Building2 },
        { name: "Posisi Jabatan", path: "/hr/positions", icon: Award },
        {
          name: "Jenis Kepegawaian",
          path: "/hr/employment-types",
          icon: FileText,
        },
        {
          name: "Kategori Pangkat",
          path: "/hr/rank-categories",
          icon: BarChart3,
        },
      ],
    },
    {
      title: "Manajemen Karyawan",
      description: "Kelola data karyawan",
      icon: Users,
      color: "bg-green-500",
      items: [
        { name: "Daftar Karyawan", path: "/hr/employees", icon: Users },
        { name: "Tambah Karyawan", path: "/hr/employees/add", icon: UserPlus },
        { name: "Laporan Karyawan", path: "/hr/reports", icon: FileText },
      ],
    },
    {
      title: "Absensi",
      description: "Kelola absensi karyawan",
      icon: Calendar,
      color: "bg-purple-500",
      items: [
        { name: "Absensi Harian", path: "/hr/attendance", icon: Calendar },
        {
          name: "Laporan Absensi",
          path: "/hr/attendance/reports",
          icon: BarChart3,
        },
      ],
    },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getActivePercentage = () => {
    if (!stats || stats.overview.total === 0) return 0;
    return Math.round((stats.overview.active / stats.overview.total) * 100);
  };

  const getDepartmentColor = (index: number) => {
    const colors = [
      "bg-blue-100 text-blue-800",
      "bg-green-100 text-green-800",
      "bg-purple-100 text-purple-800",
      "bg-orange-100 text-orange-800",
      "bg-pink-100 text-pink-800",
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manajemen SDM</h1>
          <p className="text-gray-600 mt-1">Modul Sumber Daya Manusia</p>
        </div>
        <Button
          onClick={() => router.push("/hr/employees/add")}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <UserPlus className="w-4 h-4 mr-2" />
          Tambah Karyawan
        </Button>
      </div>

      {/* Real-time Stats Cards */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                  </div>
                  <div className="w-8 h-8 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Employees */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Karyawan
                  </p>
                  <p className="text-3xl font-bold text-gray-900">
                    {stats?.overview.total || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Semua karyawan terdaftar
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Active Employees */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Karyawan Aktif
                  </p>
                  <p className="text-3xl font-bold text-green-600">
                    {stats?.overview.active || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {getActivePercentage()}% dari total
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <UserCheck className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Inactive Employees */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Tidak Aktif
                  </p>
                  <p className="text-3xl font-bold text-red-600">
                    {stats?.overview.inactive || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {100 - getActivePercentage()}% dari total
                  </p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <UserX className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Average Salary */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Rata-rata Gaji
                  </p>
                  <p className="text-2xl font-bold text-purple-600">
                    {stats?.overview.avgSalary
                      ? formatCurrency(stats.overview.avgSalary)
                      : "Rp 0"}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Per bulan</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <DollarSign className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Department Distribution & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Building2 className="w-5 h-5" />
              <span>Distribusi per Departemen</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between animate-pulse"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 rounded-full bg-gray-200"></div>
                      <div className="h-4 bg-gray-200 rounded w-32"></div>
                    </div>
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </div>
                ))}
              </div>
            ) : stats?.byDepartment && stats.byDepartment.length > 0 ? (
              stats.byDepartment.map((dept, index) => (
                <div
                  key={dept._id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span className="text-sm font-medium text-gray-900">
                      {dept._id}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="secondary"
                      className={getDepartmentColor(index)}
                    >
                      {dept.count} orang
                    </Badge>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Building2 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Belum ada data departemen</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Aksi Cepat</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <Button
                variant="outline"
                className="h-16 flex items-center justify-start space-x-4 p-4"
                onClick={() => router.push("/hr/employees")}
              >
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-left">
                  <p className="font-medium">Daftar Karyawan</p>
                  <p className="text-sm text-gray-500">Kelola data karyawan</p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex items-center justify-start space-x-4 p-4"
                onClick={() => router.push("/hr/employees/add")}
              >
                <div className="p-2 bg-green-100 rounded-lg">
                  <UserPlus className="w-5 h-5 text-green-600" />
                </div>
                <div className="text-left">
                  <p className="font-medium">Tambah Karyawan</p>
                  <p className="text-sm text-gray-500">
                    Daftarkan karyawan baru
                  </p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex items-center justify-start space-x-4 p-4"
                onClick={() => router.push("/hr/departments")}
              >
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Building2 className="w-5 h-5 text-purple-600" />
                </div>
                <div className="text-left">
                  <p className="font-medium">Master Data</p>
                  <p className="text-sm text-gray-500">Kelola data master HR</p>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Menu Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {menuItems.map((section, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${section.color}`}>
                  <section.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                  <p className="text-sm text-gray-600">{section.description}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              {section.items.map((item, itemIndex) => (
                <Button
                  key={itemIndex}
                  variant="ghost"
                  className="w-full justify-start h-auto p-3 hover:bg-gray-50"
                  onClick={() => router.push(item.path)}
                >
                  <item.icon className="w-4 h-4 mr-3 text-gray-500" />
                  <span className="text-left">{item.name}</span>
                </Button>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default HRDashboard;
