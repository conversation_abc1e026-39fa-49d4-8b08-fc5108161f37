import { Request, Response } from "express";
import { Employee } from "../models/Employee";
import Division from "../models/Division";
import { Department } from "../models/Department";
import { ultraCompleteExcelTemplateService } from "../services/ultraCompleteTemplateService";

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: any;
}

// Get all employees with pagination and search
export const getAllEmployees = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = (req.query.search as string) || "";
    const department = (req.query.department as string) || "";
    const position = (req.query.position as string) || "";
    const status = (req.query.status as string) || "";

    // Build filter object
    const filter: any = { isActive: true };

    if (search) {
      filter.$or = [
        { "personal.fullName": { $regex: search, $options: "i" } },
        { "personal.firstName": { $regex: search, $options: "i" } },
        { "personal.lastName": { $regex: search, $options: "i" } },
        { "personal.employeeId": { $regex: search, $options: "i" } },
        { "personal.email": { $regex: search, $options: "i" } },
      ];
    }

    if (department) filter["hr.department"] = department;
    if (position) filter["hr.position"] = position;
    if (status) filter.status = status;

    const skip = (page - 1) * limit;

    const employees = await Employee.find(filter)
      .populate("hr.division", "_id name")
      .populate("hr.department", "_id name")
      .populate("hr.position", "_id name")
      .populate("hr.contract.employmentType", "_id name")
      .populate("hr.tags", "_id name color")
      .sort({ "personal.fullName": 1 })
      .skip(skip)
      .limit(limit);

    const total = await Employee.countDocuments(filter);

    res.status(200).json({
      success: true,
      message: "Employees retrieved successfully",
      data: employees,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total,
        limit,
      },
    });
  } catch (error) {
    console.error("Get employees error:", error);
    res.status(500).json({
      success: false,
      message: "Gagal mengambil data karyawan",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get employee by ID
export const getEmployeeById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const employee = await Employee.findById(id)
      .populate("hr.division", "_id name description")
      .populate("hr.department", "_id name manager")
      .populate("hr.position", "_id name description")
      .populate("hr.rank.rankCategory", "_id name description")
      .populate("hr.rank.rankGrade", "_id name description")
      .populate("hr.rank.rankSubgrade", "_id name description")
      .populate("hr.contract.employmentType", "_id name description")
      .populate("hr.manager", "_id personal.fullName personal.employeeId")
      .populate(
        "hr.directSupervisor",
        "_id personal.fullName personal.employeeId"
      )
      .populate("hr.tags", "_id name color description");

    if (!employee) {
      res.status(404).json({
        success: false,
        message: "Karyawan tidak ditemukan",
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: "Employee retrieved successfully",
      data: employee,
    });
  } catch (error) {
    console.error("Get employee by ID error:", error);
    res.status(500).json({
      success: false,
      message: "Gagal mengambil data karyawan",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Create new employee
export const createEmployee = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const employeeData = req.body;

    // Clean up empty string ObjectId fields to prevent casting errors
    if (employeeData.hr.manager === "") {
      delete employeeData.hr.manager;
    }
    if (employeeData.hr.directSupervisor === "") {
      delete employeeData.hr.directSupervisor;
    }

    // Ensure employeeId is not null or empty
    if (
      !employeeData.personal.employeeId ||
      employeeData.personal.employeeId.trim() === ""
    ) {
      res.status(400).json({
        success: false,
        message: "Nomor Induk Karyawan harus diisi",
        field: "personal.employeeId",
      });
      return;
    }

    // Check if employee ID already exists
    const existingEmployee = await Employee.findOne({
      "personal.employeeId": employeeData.personal.employeeId,
    });

    if (existingEmployee) {
      res.status(400).json({
        success: false,
        message: "ID Karyawan sudah digunakan",
      });
      return;
    }

    // Check if personal email already exists
    if (employeeData.personal.email) {
      const existingEmail = await Employee.findOne({
        "personal.email": employeeData.personal.email,
      });

      if (existingEmail) {
        res.status(400).json({
          success: false,
          message: "Email pribadi sudah digunakan",
        });
        return;
      }
    }

    // Check if company email already exists
    if (employeeData.hr.companyEmail) {
      const existingCompanyEmail = await Employee.findOne({
        "hr.companyEmail": employeeData.hr.companyEmail,
      });

      if (existingCompanyEmail) {
        res.status(400).json({
          success: false,
          message: "Email perusahaan sudah digunakan",
        });
        return;
      }
    }

    // Verify referenced documents exist
    if (employeeData.hr.division) {
      const division = await Division.findById(employeeData.hr.division);
      if (!division) {
        res.status(400).json({
          success: false,
          message: "Divisi tidak ditemukan",
        });
        return;
      }
    }

    if (employeeData.hr.department) {
      const department = await Department.findById(employeeData.hr.department);
      if (!department) {
        res.status(400).json({
          success: false,
          message: "Departemen tidak ditemukan",
        });
        return;
      }
    }

    // Additional validation can be added here for other referenced models

    // Create employee - skip createdBy in development mode
    const employee = new Employee({
      ...employeeData,
    });

    await employee.save();

    // Populate the created employee
    const populatedEmployee = await Employee.findById(employee._id)
      .populate("hr.division", "_id name")
      .populate("hr.department", "_id name")
      .populate("hr.position", "_id name")
      .populate("hr.contract.employmentType", "_id name")
      .populate("hr.manager", "_id personal.fullName personal.employeeId")
      .populate(
        "hr.directSupervisor",
        "_id personal.fullName personal.employeeId"
      )
      .populate("hr.tags", "_id name color");

    res.status(201).json({
      success: true,
      message: "Karyawan berhasil ditambahkan",
      data: populatedEmployee,
    });
  } catch (error) {
    console.error("Create employee error:", error);

    // Handle validation errors with specific messages
    if (error instanceof Error && error.name === "ValidationError") {
      const validationErrors = (error as any).errors;
      const errorMessages = [];

      for (const field in validationErrors) {
        const fieldError = validationErrors[field];

        // Custom error messages for specific fields
        if (field === "personal.employeeId") {
          errorMessages.push("Nomor Induk Karyawan harus diisi");
        } else if (field === "personal.fullName") {
          errorMessages.push("Nama lengkap harus diisi");
        } else if (field === "personal.dateOfBirth") {
          errorMessages.push("Tanggal lahir harus diisi");
        } else if (field === "personal.email") {
          errorMessages.push("Email pribadi tidak valid atau sudah digunakan");
        } else if (field === "hr.division") {
          errorMessages.push("Divisi harus dipilih");
        } else if (field === "hr.department") {
          errorMessages.push("Departemen harus dipilih");
        } else if (field === "hr.position") {
          errorMessages.push("Posisi jabatan harus dipilih");
        } else if (field === "hr.contract.employmentType") {
          errorMessages.push("Jenis hubungan kerja harus dipilih");
        } else if (field === "hr.emergency.address") {
          errorMessages.push("Alamat kontak darurat harus diisi");
        } else if (field === "hr.manager") {
          errorMessages.push("Manager tidak valid (kosongkan jika tidak ada)");
        } else if (field === "hr.directSupervisor") {
          errorMessages.push(
            "Atasan langsung tidak valid (kosongkan jika tidak ada)"
          );
        } else {
          // Generic message for other fields
          errorMessages.push(`${field}: ${fieldError.message}`);
        }
      }

      res.status(400).json({
        success: false,
        message: "Data tidak valid",
        errors: errorMessages,
        details: errorMessages.join(", "),
      });
      return;
    }

    // Handle duplicate key errors
    if (error instanceof Error && (error as any).code === 11000) {
      const duplicateField = Object.keys((error as any).keyValue)[0];
      let message = "Data sudah ada";

      if (duplicateField === "personal.employeeId") {
        message = "Nomor Induk Karyawan sudah digunakan";
      } else if (duplicateField === "personal.email") {
        message = "Email pribadi sudah digunakan";
      } else if (duplicateField === "hr.companyEmail") {
        message = "Email perusahaan sudah digunakan";
      }

      res.status(400).json({
        success: false,
        message: message,
        field: duplicateField,
      });
      return;
    }

    // Generic error
    res.status(500).json({
      success: false,
      message: "Gagal menambahkan karyawan",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Update employee
export const updateEmployee = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const employeeData = req.body;

    console.log("Update employee request:", {
      id,
      bodyKeys: Object.keys(employeeData),
      personalKeys: employeeData.personal
        ? Object.keys(employeeData.personal)
        : [],
      hrKeys: employeeData.hr ? Object.keys(employeeData.hr) : [],
    });

    // Detailed logging for problematic fields
    console.log("=== BACKEND RECEIVED DATA ===");
    if (employeeData.hr) {
      console.log("Employment Type:", {
        contract: employeeData.hr.contract?.employmentType,
        rootLevel: employeeData.hr.employmentType,
      });
      console.log("Rank fields:", {
        rankCategory: employeeData.hr.rank?.rankCategory,
        rankGrade: employeeData.hr.rank?.rankGrade,
        rankSubgrade: employeeData.hr.rank?.rankSubgrade,
      });
    }

    // Log specific fields for debugging
    if (employeeData.personal) {
      console.log("Personal data:", {
        employeeId: employeeData.personal.employeeId,
        fullName: employeeData.personal.fullName,
        email: employeeData.personal.email,
        phone: employeeData.personal.phone,
      });
    }

    // Check if employee exists
    const existingEmployee = await Employee.findById(id);
    if (!existingEmployee) {
      res.status(404).json({
        success: false,
        message: "Karyawan tidak ditemukan",
      });
      return;
    }

    // Check if employee ID is being changed and already exists
    if (
      employeeData.personal?.employeeId &&
      employeeData.personal.employeeId !== existingEmployee.personal?.employeeId
    ) {
      const duplicateEmployeeId = await Employee.findOne({
        "personal.employeeId": employeeData.personal.employeeId,
        _id: { $ne: id },
      });

      if (duplicateEmployeeId) {
        res.status(400).json({
          success: false,
          message: "ID Karyawan sudah digunakan",
        });
        return;
      }
    }

    // Check if personal email is being changed and already exists
    if (
      employeeData.personal?.email &&
      employeeData.personal.email !== existingEmployee.personal?.email
    ) {
      const duplicateEmail = await Employee.findOne({
        "personal.email": employeeData.personal.email,
        _id: { $ne: id },
      });

      if (duplicateEmail) {
        res.status(400).json({
          success: false,
          message: "Email pribadi sudah digunakan",
        });
        return;
      }
    }

    // Check if company email is being changed and already exists
    if (
      employeeData.hr?.companyEmail &&
      employeeData.hr.companyEmail !== existingEmployee.hr?.companyEmail
    ) {
      const duplicateCompanyEmail = await Employee.findOne({
        "hr.companyEmail": employeeData.hr.companyEmail,
        _id: { $ne: id },
      });

      if (duplicateCompanyEmail) {
        res.status(400).json({
          success: false,
          message: "Email perusahaan sudah digunakan",
        });
        return;
      }
    }

    // Prepare update data with proper merging
    const updateData: any = {};

    // Handle personal data updates
    if (employeeData.personal) {
      Object.keys(employeeData.personal).forEach((key) => {
        if (
          employeeData.personal[key] !== undefined &&
          employeeData.personal[key] !== null
        ) {
          updateData[`personal.${key}`] = employeeData.personal[key];
        }
      });
    }

    // Handle HR data updates
    if (employeeData.hr) {
      Object.keys(employeeData.hr).forEach((key) => {
        if (
          employeeData.hr[key] !== undefined &&
          employeeData.hr[key] !== null &&
          employeeData.hr[key] !== "" // Skip empty strings for ObjectId fields
        ) {
          let value = employeeData.hr[key];

          // Handle special cases for object references
          if (
            key === "division" ||
            key === "department" ||
            key === "position" ||
            key === "manager" ||
            key === "directSupervisor"
          ) {
            // Skip empty strings for ObjectId fields
            if (value === "") {
              return;
            }
            if (typeof value === "object" && value.id) {
              value = value.id;
            }
          }

          // Handle contract with employmentType
          if (key === "contract" && typeof value === "object") {
            const contract = { ...value };
            console.log("Processing contract:", contract);
            if (
              contract.employmentType &&
              typeof contract.employmentType === "object" &&
              contract.employmentType.id
            ) {
              console.log(
                "Converting employmentType from object to ID:",
                contract.employmentType.id
              );
              contract.employmentType = contract.employmentType.id;
            }
            // Allow clearing employmentType if it's empty string
            if (contract.employmentType === "") {
              contract.employmentType = null;
            }
            console.log("Final contract:", contract);
            value = contract;
          }

          // Handle rank nested object
          if (key === "rank" && typeof value === "object") {
            const rank = { ...value };
            console.log("Processing rank:", rank);

            // Convert rank category, grade, and subgrade from objects to IDs
            if (rank.rankCategory) {
              if (
                typeof rank.rankCategory === "object" &&
                rank.rankCategory.id
              ) {
                rank.rankCategory = rank.rankCategory.id;
              } else if (rank.rankCategory === "") {
                rank.rankCategory = null;
              }
            }
            if (rank.rankGrade) {
              if (typeof rank.rankGrade === "object" && rank.rankGrade.id) {
                rank.rankGrade = rank.rankGrade.id;
              } else if (rank.rankGrade === "") {
                rank.rankGrade = null;
              }
            }
            if (rank.rankSubgrade) {
              if (
                typeof rank.rankSubgrade === "object" &&
                rank.rankSubgrade.id
              ) {
                rank.rankSubgrade = rank.rankSubgrade.id;
              } else if (rank.rankSubgrade === "") {
                rank.rankSubgrade = null;
              }
            }

            console.log("Final rank:", rank);

            // Set each rank field individually to avoid nested object issues
            // Always update rank fields, even if empty, to allow clearing values
            if (rank.rankCategory !== undefined) {
              updateData[`hr.rank.rankCategory`] = rank.rankCategory;
            }
            if (rank.rankGrade !== undefined) {
              updateData[`hr.rank.rankGrade`] = rank.rankGrade;
            }
            if (rank.rankSubgrade !== undefined) {
              updateData[`hr.rank.rankSubgrade`] = rank.rankSubgrade;
            }
            if (rank.pensionFundNumber !== undefined) {
              updateData[`hr.rank.pensionFundNumber`] = rank.pensionFundNumber;
            }

            // Skip setting the whole rank object
            return;
          }

          // Handle empty string values for ObjectId fields
          if (
            (key === "manager" ||
              key === "directSupervisor" ||
              key === "division" ||
              key === "department" ||
              key === "position") &&
            value === ""
          ) {
            console.log(`Converting empty ${key} to null`);
            updateData[`hr.${key}`] = null;
          } else {
            updateData[`hr.${key}`] = value;
          }
        }
      });
    }

    // Handle family data updates
    if (employeeData.family) {
      Object.keys(employeeData.family).forEach((key) => {
        if (
          employeeData.family[key] !== undefined &&
          employeeData.family[key] !== null
        ) {
          updateData[`family.${key}`] = employeeData.family[key];
        }
      });
    }

    // Handle nested employmentType in contract
    if (
      updateData["hr.contract"] &&
      typeof updateData["hr.contract"] === "object"
    ) {
      const contract = updateData["hr.contract"];
      if (
        contract.employmentType &&
        typeof contract.employmentType === "object" &&
        contract.employmentType.id
      ) {
        console.log(
          "Found nested employmentType, converting to ID:",
          contract.employmentType.id
        );
        contract.employmentType = contract.employmentType.id;
        updateData["hr.contract"] = contract;
      }
    }

    // Add system fields
    updateData.updatedAt = new Date();

    // Remove updatedBy if it exists to avoid ObjectId casting errors in dev mode
    if (updateData.updatedBy) {
      delete updateData.updatedBy;
    }

    console.log("Update data prepared:", updateData);

    // Final check and fix for employmentType
    if (updateData["hr.contract"] && updateData["hr.contract"].employmentType) {
      const employmentType = updateData["hr.contract"].employmentType;
      if (typeof employmentType === "object" && employmentType.id) {
        console.log(
          "FINAL FIX: Converting employmentType object to ID:",
          employmentType.id
        );
        updateData["hr.contract"].employmentType = employmentType.id;
      }
    }

    // Final cleanup: Convert any remaining empty string ObjectId fields to null
    const objectIdFields = [
      "hr.manager",
      "hr.directSupervisor",
      "hr.division",
      "hr.department",
      "hr.position",
      "hr.rank.rankCategory",
      "hr.rank.rankGrade",
      "hr.rank.rankSubgrade",
    ];

    objectIdFields.forEach((field) => {
      if (updateData[field] === "") {
        console.log(`FINAL CLEANUP: Converting empty ${field} to null`);
        updateData[field] = null;
      }
    });

    // Final cleanup for employmentType - ensure it's a string ID, not object
    if (
      updateData["hr.employmentType"] &&
      typeof updateData["hr.employmentType"] === "object"
    ) {
      if (updateData["hr.employmentType"].id) {
        console.log(
          `FINAL CLEANUP: Converting employmentType object to ID:`,
          updateData["hr.employmentType"].id
        );
        updateData["hr.employmentType"] = updateData["hr.employmentType"].id;
      }
    }

    console.log("Final update data:", updateData);

    // Update employee
    const updatedEmployee = await Employee.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: false }
    )
      .populate("hr.division", "_id name")
      .populate("hr.department", "_id name")
      .populate("hr.position", "_id name")
      .populate("hr.rank.rankCategory", "_id name")
      .populate("hr.rank.rankGrade", "_id name")
      .populate("hr.rank.rankSubgrade", "_id name")
      .populate("hr.contract.employmentType", "_id name")
      .populate("hr.manager", "_id personal.fullName personal.employeeId")
      .populate(
        "hr.directSupervisor",
        "_id personal.fullName personal.employeeId"
      )
      .populate("hr.tags", "_id name color");

    res.status(200).json({
      success: true,
      message: "Data karyawan berhasil diperbarui",
      data: updatedEmployee,
    });
  } catch (error) {
    console.error("Update employee error:", error);

    let errorMessage = "Gagal memperbarui data karyawan";
    let statusCode = 500;

    if (error instanceof Error) {
      // Handle specific MongoDB errors
      if (error.message.includes("Cast to ObjectId failed")) {
        errorMessage =
          "Format ID tidak valid. Pastikan semua field referensi terisi dengan benar.";
        statusCode = 400;
      } else if (error.message.includes("duplicate key")) {
        errorMessage =
          "Data sudah ada. Periksa Employee ID atau email yang mungkin sudah digunakan.";
        statusCode = 400;
      } else if (error.message.includes("validation failed")) {
        errorMessage =
          "Data tidak valid. Periksa kembali semua field yang wajib diisi.";
        statusCode = 400;
      } else if (error.message.includes("BSONError")) {
        errorMessage =
          "Format data tidak sesuai. Pastikan semua dropdown terisi dengan pilihan yang valid.";
        statusCode = 400;
      } else {
        // Use the original error message for other cases
        errorMessage = error.message;
      }
    }

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Delete employee (soft delete)
export const deleteEmployee = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const employee = await Employee.findById(id);
    if (!employee) {
      res.status(404).json({
        success: false,
        message: "Karyawan tidak ditemukan",
      });
      return;
    }

    // Soft delete - skip updatedBy in development mode
    await Employee.findByIdAndUpdate(id, {
      isActive: false,
      status: "Tidak Aktif",
    });

    res.status(200).json({
      success: true,
      message: "Karyawan berhasil dihapus",
    });
  } catch (error) {
    console.error("Delete employee error:", error);
    res.status(500).json({
      success: false,
      message: "Gagal menghapus karyawan",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get employee statistics
export const getEmployeeStats = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    const stats = await Employee.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: [{ $eq: ["$status", "Aktif"] }, 1, 0] } },
          inactive: {
            $sum: { $cond: [{ $eq: ["$status", "Tidak Aktif"] }, 1, 0] },
          },
          avgSalary: { $avg: "$hr.salary.total" },
        },
      },
    ]);

    const departmentStats = await Employee.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: "departments",
          localField: "hr.department",
          foreignField: "_id",
          as: "department",
        },
      },
      { $unwind: "$department" },
      {
        $group: {
          _id: "$department.name",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
    ]);

    res.status(200).json({
      success: true,
      message: "Employee statistics retrieved successfully",
      data: {
        overview: stats[0] || {
          total: 0,
          active: 0,
          inactive: 0,
          avgSalary: 0,
        },
        byDepartment: departmentStats,
      },
    });
  } catch (error) {
    console.error("Get employee stats error:", error);
    res.status(500).json({
      success: false,
      message: "Gagal mengambil statistik karyawan",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Download Excel template for bulk import
export const downloadExcelTemplate = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    // Only use Ultra Complete Template (all database fields)
    const buffer = await ultraCompleteExcelTemplateService.generateUltraCompleteTemplate();

    // Set headers for file download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      "attachment; filename=Employee_Import_Template.xlsx"
    );
    res.setHeader("Content-Length", buffer.length);

    res.send(buffer);
  } catch (error) {
    console.error("Download template error:", error);
    res.status(500).json({
      success: false,
      message: "Gagal mendownload template",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
