import mongoose from 'mongoose';
import { connectDatabase } from '../config/database';

async function fixRolePermissions() {
  try {
    console.log('🔧 Fixing role permissions...\n');

    // Get database instance
    const db = mongoose.connection.db;
    const rolesCollection = db.collection('roles');
    
    // Define correct permissions structure for admin role
    const adminPermissions = [
      {
        module: "hr",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "inventory",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "mess",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "building",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "user-access",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
    ];

    // Define correct permissions structure for hr_manager role
    const hrManagerPermissions = [
      {
        module: "hr",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "inventory",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "mess",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "building",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "user-access",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
    ];

    // Define correct permissions structure for employee role
    const employeePermissions = [
      {
        module: "hr",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "inventory",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "mess",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "building",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "user-access",
        actions: {
          create: false,
          read: false,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
    ];

    // Update admin role
    console.log('🔧 Updating admin role permissions...');
    const adminResult = await rolesCollection.updateOne(
      { name: 'admin' },
      { 
        $set: { 
          permissions: adminPermissions,
          level: 1,
          isSystem: true,
          updatedAt: new Date()
        }
      }
    );
    console.log(`   ✅ Admin role updated: ${adminResult.modifiedCount} document(s)`);

    // Update hr_manager role
    console.log('🔧 Updating hr_manager role permissions...');
    const hrResult = await rolesCollection.updateOne(
      { name: 'hr_manager' },
      { 
        $set: { 
          permissions: hrManagerPermissions,
          level: 2,
          isSystem: false,
          updatedAt: new Date()
        }
      }
    );
    console.log(`   ✅ HR Manager role updated: ${hrResult.modifiedCount} document(s)`);

    // Update employee role
    console.log('🔧 Updating employee role permissions...');
    const employeeResult = await rolesCollection.updateOne(
      { name: 'employee' },
      { 
        $set: { 
          permissions: employeePermissions,
          level: 3,
          isSystem: false,
          updatedAt: new Date()
        }
      }
    );
    console.log(`   ✅ Employee role updated: ${employeeResult.modifiedCount} document(s)`);

    // Verify updates
    console.log('\n📊 Verification:');
    const roles = await rolesCollection.find({}).toArray();
    
    roles.forEach((role) => {
      console.log(`\n🎭 Role: ${role.name}`);
      console.log(`   Level: ${role.level}`);
      console.log(`   System: ${role.isSystem}`);
      console.log(`   Permissions count: ${role.permissions ? role.permissions.length : 0}`);
      
      if (role.permissions && role.permissions.length > 0) {
        console.log('   Modules:');
        role.permissions.forEach((perm: any) => {
          const actions = Object.entries(perm.actions)
            .filter(([_, allowed]) => allowed)
            .map(([action, _]) => action);
          console.log(`     - ${perm.module}: [${actions.join(', ')}]`);
        });
      }
    });

    console.log('\n🎉 Role permissions fixed successfully!');

  } catch (error) {
    console.error('❌ Error fixing role permissions:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await fixRolePermissions();
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the fix
main();
