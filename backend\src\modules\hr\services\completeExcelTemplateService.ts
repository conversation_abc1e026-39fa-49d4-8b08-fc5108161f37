import ExcelJS from "exceljs";

export interface TemplateField {
  column: string;
  field: string;
  label: string;
  required: boolean;
  example: string;
  validation?: string[];
}

// COMPLETE EMPLOYEE TEMPLATE FIELDS - ALL FIELDS INCLUDING FAMILY
export const COMPLETE_TEMPLATE_FIELDS: TemplateField[] = [
  // === PERSONAL INFORMATION ===
  {
    column: "A",
    field: "personal.employeeId",
    label: "NIK*",
    required: true,
    example: "EMP001",
  },
  {
    column: "B",
    field: "personal.fullName",
    label: "<PERSON>a <PERSON>*",
    required: true,
    example: "John Doe",
  },
  {
    column: "C",
    field: "personal.gender",
    label: "<PERSON><PERSON>*",
    required: true,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "D",
    field: "personal.placeOfBirth",
    label: "Tempat Lahir*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "E",
    field: "personal.dateOfBirth",
    label: "<PERSON><PERSON>*",
    required: true,
    example: "15-01-1990",
  },
  {
    column: "F",
    field: "personal.religion",
    label: "Agama*",
    required: true,
    example: "Islam",
    validation: [
      "Islam",
      "Kristen",
      "Katolik",
      "Hindu",
      "Buddha",
      "Konghucu",
      "Lainnya",
    ],
  },
  {
    column: "G",
    field: "personal.bloodType",
    label: "<PERSON>longan <PERSON>ah*",
    required: true,
    example: "A",
    validation: ["A", "B", "AB", "O"],
  },
  {
    column: "H",
    field: "personal.maritalStatus",
    label: "Status Pernikahan*",
    required: true,
    example: "Menikah",
    validation: ["Belum Menikah", "Menikah", "Cerai Hidup", "Cerai Mati"],
  },
  {
    column: "I",
    field: "personal.nationality",
    label: "Kewarganegaraan",
    required: false,
    example: "Indonesia",
  },
  {
    column: "J",
    field: "personal.idCardNumber",
    label: "No KTP*",
    required: true,
    example: "3171234567890123",
  },
  {
    column: "K",
    field: "personal.phone",
    label: "No HP*",
    required: true,
    example: "081234567890",
  },
  {
    column: "L",
    field: "personal.email",
    label: "Email Pribadi",
    required: false,
    example: "<EMAIL>",
  },
  {
    column: "M",
    field: "personal.currentAddress",
    label: "Alamat Domisili*",
    required: true,
    example: "Jl. Sudirman No. 123, Jakarta Pusat",
  },
  {
    column: "N",
    field: "personal.idCardAddress",
    label: "Alamat KTP*",
    required: true,
    example: "Jl. Thamrin No. 456, Jakarta Pusat",
  },

  // === EDUCATION INFORMATION ===
  {
    column: "O",
    field: "hr.education.lastEducation",
    label: "Pendidikan Terakhir",
    required: false,
    example: "S1",
    validation: ["SD", "SMP", "SMA", "SMK", "D1", "D2", "D3", "S1", "S2", "S3"],
  },
  {
    column: "P",
    field: "hr.education.institution",
    label: "Nama Institusi",
    required: false,
    example: "Universitas Indonesia",
  },
  {
    column: "Q",
    field: "hr.education.major",
    label: "Jurusan",
    required: false,
    example: "Teknik Informatika",
  },
  {
    column: "R",
    field: "hr.education.graduationYear",
    label: "Tahun Lulus",
    required: false,
    example: "2012",
  },

  // === WORK INFORMATION ===
  {
    column: "S",
    field: "hr.division",
    label: "Divisi*",
    required: true,
    example: "OPERATIONAL",
  },
  {
    column: "T",
    field: "hr.department",
    label: "Departemen*",
    required: true,
    example: "PRODUKSI",
  },
  {
    column: "U",
    field: "hr.position",
    label: "Jabatan*",
    required: true,
    example: "Staff",
  },
  {
    column: "V",
    field: "hr.contract.employmentType",
    label: "Status Kerja*",
    required: true,
    example: "TETAP",
    validation: [
      "TETAP",
      "KONTRAK",
      "PROBATION",
      "MAGANG",
      "FREELANCE",
      "KONSULTAN",
    ],
  },
  {
    column: "W",
    field: "hr.contract.hireDate",
    label: "Tanggal Masuk*",
    required: true,
    example: "15-01-2024",
  },
  {
    column: "X",
    field: "hr.salary.basic",
    label: "Gaji Pokok",
    required: false,
    example: "5000000",
  },

  // === EMERGENCY CONTACT ===
  {
    column: "Y",
    field: "hr.emergency.contactName",
    label: "Nama Kontak Darurat*",
    required: true,
    example: "Jane Doe",
  },
  {
    column: "Z",
    field: "hr.emergency.contactPhone",
    label: "HP Kontak Darurat*",
    required: true,
    example: "081234567891",
  },
  {
    column: "AA",
    field: "hr.emergency.relationship",
    label: "Hubungan Kontak Darurat*",
    required: true,
    example: "Istri",
  },
  {
    column: "AB",
    field: "hr.emergency.address",
    label: "Alamat Kontak Darurat*",
    required: true,
    example: "Jl. Sudirman No. 123, Jakarta",
  },

  // === FAMILY INFORMATION ===
  {
    column: "AC",
    field: "family.fatherName",
    label: "Nama Ayah",
    required: false,
    example: "Robert Doe",
  },
  {
    column: "AD",
    field: "family.motherName",
    label: "Nama Ibu",
    required: false,
    example: "Maria Doe",
  },
  {
    column: "AE",
    field: "family.spouseName",
    label: "Nama Pasangan",
    required: false,
    example: "Jane Smith",
  },
  {
    column: "AF",
    field: "family.spouseDateOfBirth",
    label: "Tanggal Lahir Pasangan",
    required: false,
    example: "20-05-1992",
  },
  {
    column: "AG",
    field: "family.spouseOccupation",
    label: "Pekerjaan Pasangan",
    required: false,
    example: "Guru",
  },

  // === CHILDREN INFORMATION (1-4) ===
  {
    column: "AH",
    field: "family.children.0.name",
    label: "Nama Anak 1",
    required: false,
    example: "Alice Doe",
  },
  {
    column: "AI",
    field: "family.children.0.dateOfBirth",
    label: "Tanggal Lahir Anak 1",
    required: false,
    example: "10-03-2015",
  },
  {
    column: "AJ",
    field: "family.children.0.gender",
    label: "Jenis Kelamin Anak 1",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "AK",
    field: "family.children.1.name",
    label: "Nama Anak 2",
    required: false,
    example: "Bob Doe",
  },
  {
    column: "AL",
    field: "family.children.1.dateOfBirth",
    label: "Tanggal Lahir Anak 2",
    required: false,
    example: "15-07-2017",
  },
  {
    column: "AM",
    field: "family.children.1.gender",
    label: "Jenis Kelamin Anak 2",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "AN",
    field: "family.children.2.name",
    label: "Nama Anak 3",
    required: false,
    example: "Charlie Doe",
  },
  {
    column: "AO",
    field: "family.children.2.dateOfBirth",
    label: "Tanggal Lahir Anak 3",
    required: false,
    example: "22-11-2019",
  },
  {
    column: "AP",
    field: "family.children.2.gender",
    label: "Jenis Kelamin Anak 3",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "AQ",
    field: "family.children.3.name",
    label: "Nama Anak 4",
    required: false,
    example: "Diana Doe",
  },
  {
    column: "AR",
    field: "family.children.3.dateOfBirth",
    label: "Tanggal Lahir Anak 4",
    required: false,
    example: "05-09-2021",
  },
  {
    column: "AS",
    field: "family.children.3.gender",
    label: "Jenis Kelamin Anak 4",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },

  // === SIBLINGS INFORMATION (1-4) ===
  {
    column: "AT",
    field: "family.siblings.0.name",
    label: "Nama Saudara 1",
    required: false,
    example: "Michael Doe",
  },
  {
    column: "AU",
    field: "family.siblings.0.dateOfBirth",
    label: "Tanggal Lahir Saudara 1",
    required: false,
    example: "12-08-1988",
  },
  {
    column: "AV",
    field: "family.siblings.0.gender",
    label: "Jenis Kelamin Saudara 1",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "AW",
    field: "family.siblings.0.occupation",
    label: "Pekerjaan Saudara 1",
    required: false,
    example: "Dokter",
  },
  {
    column: "AX",
    field: "family.siblings.1.name",
    label: "Nama Saudara 2",
    required: false,
    example: "Sarah Doe",
  },
  {
    column: "AY",
    field: "family.siblings.1.dateOfBirth",
    label: "Tanggal Lahir Saudara 2",
    required: false,
    example: "25-12-1992",
  },
  {
    column: "AZ",
    field: "family.siblings.1.gender",
    label: "Jenis Kelamin Saudara 2",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "BA",
    field: "family.siblings.1.occupation",
    label: "Pekerjaan Saudara 2",
    required: false,
    example: "Guru",
  },
  {
    column: "BB",
    field: "family.siblings.2.name",
    label: "Nama Saudara 3",
    required: false,
    example: "David Doe",
  },
  {
    column: "BC",
    field: "family.siblings.2.dateOfBirth",
    label: "Tanggal Lahir Saudara 3",
    required: false,
    example: "18-04-1995",
  },
  {
    column: "BD",
    field: "family.siblings.2.gender",
    label: "Jenis Kelamin Saudara 3",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "BE",
    field: "family.siblings.2.occupation",
    label: "Pekerjaan Saudara 3",
    required: false,
    example: "Engineer",
  },
  {
    column: "BF",
    field: "family.siblings.3.name",
    label: "Nama Saudara 4",
    required: false,
    example: "Lisa Doe",
  },
  {
    column: "BG",
    field: "family.siblings.3.dateOfBirth",
    label: "Tanggal Lahir Saudara 4",
    required: false,
    example: "30-06-1997",
  },
  {
    column: "BH",
    field: "family.siblings.3.gender",
    label: "Jenis Kelamin Saudara 4",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "BI",
    field: "family.siblings.3.occupation",
    label: "Pekerjaan Saudara 4",
    required: false,
    example: "Designer",
  },
];

export class CompleteExcelTemplateService {
  async generateCompleteTemplate(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Employee Data
    const dataSheet = workbook.addWorksheet("Employee_Data");

    // Add headers
    const headerRow = dataSheet.addRow(
      COMPLETE_TEMPLATE_FIELDS.map((field) => field.label)
    );
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE6F3FF" },
    };

    // Add example data
    const exampleRow = dataSheet.addRow(
      COMPLETE_TEMPLATE_FIELDS.map((field) => field.example)
    );
    exampleRow.font = { italic: true };
    exampleRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFF0F8FF" },
    };

    // Auto-fit columns
    dataSheet.columns.forEach((column, index) => {
      const field = COMPLETE_TEMPLATE_FIELDS[index];
      column.width = Math.max(field.label.length, field.example.length) + 3;
    });

    return this.addReferenceAndInstructionSheets(workbook);
  }

  private async addReferenceAndInstructionSheets(
    workbook: ExcelJS.Workbook
  ): Promise<Buffer> {
    // Sheet 2: Reference Data
    const refSheet = workbook.addWorksheet("Reference_Data");

    // Add reference data for dropdowns
    const referenceData = {
      "Jenis Kelamin": ["Laki-laki", "Perempuan"],
      Agama: [
        "Islam",
        "Kristen",
        "Katolik",
        "Hindu",
        "Buddha",
        "Konghucu",
        "Lainnya",
      ],
      "Golongan Darah": ["A", "B", "AB", "O"],
      "Status Pernikahan": [
        "Belum Menikah",
        "Menikah",
        "Cerai Hidup",
        "Cerai Mati",
      ],
      Pendidikan: [
        "SD",
        "SMP",
        "SMA",
        "SMK",
        "D1",
        "D2",
        "D3",
        "S1",
        "S2",
        "S3",
      ],
      "Status Kerja": [
        "TETAP",
        "KONTRAK",
        "PROBATION",
        "MAGANG",
        "FREELANCE",
        "KONSULTAN",
      ],
    };

    let currentCol = 1;
    Object.entries(referenceData).forEach(([title, values]) => {
      refSheet.getCell(1, currentCol).value = title;
      refSheet.getCell(1, currentCol).font = { bold: true };

      values.forEach((value, index) => {
        refSheet.getCell(index + 2, currentCol).value = value;
      });

      currentCol++;
    });

    // Sheet 3: Instructions
    const instructionSheet = workbook.addWorksheet("Instructions");

    const instructions = [
      "PETUNJUK PENGGUNAAN TEMPLATE EMPLOYEE IMPORT LENGKAP",
      "",
      '1. ISI DATA KARYAWAN PADA SHEET "Employee_Data"',
      "2. KOLOM DENGAN TANDA (*) WAJIB DIISI",
      "3. GUNAKAN FORMAT YANG SESUAI:",
      "   - Tanggal: DD-MM-YYYY (contoh: 15-01-2024)",
      "   - Jenis Kelamin: Laki-laki atau Perempuan",
      "   - Agama: Islam, Kristen, Katolik, Hindu, Buddha, Konghucu, Lainnya",
      "   - Golongan Darah: A, B, AB, atau O",
      "   - Status Pernikahan: Belum Menikah, Menikah, Cerai Hidup, Cerai Mati",
      "   - Status Kerja: TETAP, KONTRAK, PROBATION, MAGANG, FREELANCE, KONSULTAN",
      "",
      "4. JANGAN MENGUBAH NAMA KOLOM ATAU SHEET",
      "5. MAKSIMAL 500 BARIS DATA PER UPLOAD",
      "6. HAPUS BARIS CONTOH SEBELUM UPLOAD",
      "",
      "TEMPLATE INI BERISI:",
      "- Data Pribadi Lengkap (14 field)",
      "- Data Pendidikan (4 field)",
      "- Data Pekerjaan (6 field)",
      "- Kontak Darurat (4 field)",
      "- Data Keluarga (5 field)",
      "- Data Anak 1-4 (12 field)",
      "- Data Saudara 1-4 (16 field)",
      "",
      "TOTAL: 61 FIELD LENGKAP",
      "",
      "FIELD WAJIB YANG HARUS DIISI:",
      "- NIK (Nomor Induk Karyawan)",
      "- Nama Lengkap",
      "- Jenis Kelamin",
      "- Tempat Lahir",
      "- Tanggal Lahir",
      "- Agama",
      "- Golongan Darah",
      "- Status Pernikahan",
      "- No KTP",
      "- No HP",
      "- Alamat Domisili",
      "- Alamat KTP",
      "- Divisi",
      "- Departemen",
      "- Jabatan",
      "- Status Kerja",
      "- Tanggal Masuk",
      "- Nama Kontak Darurat",
      "- HP Kontak Darurat",
      "- Hubungan Kontak Darurat",
      "- Alamat Kontak Darurat",
    ];

    instructions.forEach((instruction, index) => {
      const cell = instructionSheet.getCell(index + 1, 1);
      cell.value = instruction;
      if (index === 0) {
        cell.font = { bold: true, size: 14 };
      } else if (
        instruction.startsWith("FIELD WAJIB") ||
        instruction.startsWith("TEMPLATE INI") ||
        instruction.match(/^\d+\./)
      ) {
        cell.font = { bold: true };
      }
    });

    instructionSheet.getColumn(1).width = 60;

    // Convert to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}

export const completeExcelTemplateService = new CompleteExcelTemplateService();
