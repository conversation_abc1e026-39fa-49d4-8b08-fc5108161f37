"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface HRInfoData {
  // Employee Information
  employeeId: string;
  position: string;
  division: string;
  department: string;
  companyEmail: string;
  manager: string;
  directSupervisor: string;
  status: string;

  // Contract Information
  contract: {
    employmentType: string;
    hireDate: string;
    contractDate: string;
    contractEndDate: string;
    permanentDate: string;
    exitDate: string;
  };

  // Education
  education: {
    certificateLevel: string;
    fieldOfStudy: string;
    schoolName: string;
    schoolCity: string;
    graduationStatus: string;
    description: string;
  };

  // Rank and Grade
  rank: {
    rankCategory: string;
    rankGrade: string;
    rankSubgrade: string;
    pensionFundNumber: string;
  };

  // Emergency Contact
  emergency: {
    contactName: string;
    contactPhone: string;
    contactPhone2: string;
    relationship: string;
    address: string;
  };

  // POO/POH
  location: {
    pointOfOrigin: string;
    pointOfHire: string;
  };

  // Uniform and Work Shoes
  uniform: {
    workUniformSize: string;
    workShoesSize: string;
  };

  // Salary
  salary: {
    basic: number;
    allowances: {
      transport: number;
      meal: number;
      communication: number;
      position: number;
      other: number;
    };
  };

  workSchedule: string;
}

interface CreateEmployeeHRInfoProps {
  data: HRInfoData;
  onUpdate: (data: Partial<HRInfoData>) => void;
  // Master data options
  divisions: Array<{
    _id?: string;
    id?: string;
    name: string;
    isActive?: boolean;
  }>;
  departments: Array<{
    _id?: string;
    id?: string;
    name: string;
    isActive?: boolean;
  }>;
  positions: Array<{
    _id?: string;
    id?: string;
    name: string;
    isActive?: boolean;
  }>;
  employmentTypes: Array<{ _id: string; name: string }>;
  rankCategories: Array<{ _id: string; name: string }>;
  rankGrades: Array<{ _id: string; name: string }>;
  rankSubgrades: Array<{ _id: string; name: string }>;
  employees: Array<{
    _id?: string;
    id: string;
    personal: { fullName: string; employeeId: string };
  }>;
}

const certificateLevelOptions = [
  "SD",
  "SMP",
  "SMA",
  "SMK",
  "D1",
  "D2",
  "D3",
  "S1",
  "S2",
  "S3",
];

const uniformSizeOptions = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];

const graduationStatusOptions = [
  { value: "Lulus", label: "Lulus" },
  { value: "Tidak Lulus", label: "Tidak Lulus" },
  { value: "Sedang Belajar", label: "Sedang Belajar" },
];

const relationshipOptions = [
  "Orang Tua",
  "Saudara Kandung",
  "Pasangan",
  "Anak",
  "Kerabat",
  "Teman",
  "Lainnya",
];

const workScheduleOptions = [
  { value: "Regular", label: "Regular" },
  { value: "Shift", label: "Shift" },
  { value: "Flexible", label: "Flexible" },
  { value: "Remote", label: "Remote" },
  { value: "Part Time", label: "Part Time" },
];

const statusOptions = [
  { value: "Aktif", label: "🟢 Aktif" },
  { value: "Probation", label: "🟡 Probation" },
  { value: "Cuti", label: "🔵 Cuti" },
  { value: "Notice Period", label: "🟠 Notice Period" },
  { value: "Tidak Aktif", label: "⚫ Tidak Aktif" },
  { value: "Resign", label: "🔴 Resign" },
  { value: "Terminated", label: "❌ Terminated" },
  { value: "Pension", label: "🏆 Pension" },
  { value: "Kontrak Habis", label: "📋 Kontrak Habis" },
];

export default function CreateEmployeeHRInfo({
  data,
  onUpdate,
  divisions,
  departments,
  positions,
  employmentTypes,
  rankCategories,
  rankGrades,
  rankSubgrades,
  employees,
}: CreateEmployeeHRInfoProps) {
  // Employee options for manager and direct supervisor
  const employeeOptions = employees
    .filter((emp) => emp.personal?.fullName && emp.personal?.employeeId)
    .map((emp) => ({
      value: emp.id || emp._id || "",
      label: `${emp.personal?.fullName} (${emp.personal?.employeeId})`,
    }));

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    let updateData: any = {};

    if (keys.length === 2) {
      updateData[keys[0]] = {
        ...data[keys[0] as keyof HRInfoData],
        [keys[1]]: value,
      };
    } else if (keys.length === 3) {
      updateData[keys[0]] = {
        ...data[keys[0] as keyof HRInfoData],
        [keys[1]]: {
          ...data[keys[0] as keyof HRInfoData][keys[1] as any],
          [keys[2]]: value,
        },
      };
    } else {
      updateData[field] = value;
    }

    onUpdate(updateData);
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type:
      | "text"
      | "select"
      | "searchable-select"
      | "date"
      | "textarea"
      | "number" = "text",
    options?:
      | string[]
      | { value: string; label: string }[]
      | Array<{ _id?: string; id: string; name: string }>,
    required = false
  ) => (
    <div className={type === "textarea" ? "md:col-span-2" : ""}>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {type === "select" ? (
        <Select
          value={field.split(".").reduce((obj, key) => obj?.[key], data)}
          onValueChange={(value) => handleInputChange(field, value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {options?.map((option) => (
              <SelectItem
                key={
                  typeof option === "string"
                    ? option
                    : "id" in option
                    ? option.id || option._id
                    : option.value
                }
                value={
                  typeof option === "string"
                    ? option
                    : "id" in option
                    ? option.id || option._id
                    : option.value
                }
              >
                {typeof option === "string"
                  ? option
                  : "id" in option
                  ? option.name
                  : option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : type === "searchable-select" ? (
        <SearchableSelect
          value={field.split(".").reduce((obj, key) => obj?.[key], data)}
          onValueChange={(value) => handleInputChange(field, value)}
          placeholder={`Pilih ${label.toLowerCase()}`}
          searchPlaceholder={`Cari ${label.toLowerCase()}...`}
          emptyMessage={`Tidak ada ${label.toLowerCase()} ditemukan.`}
          options={
            options?.map((option) => ({
              value:
                typeof option === "string"
                  ? option
                  : "id" in option
                  ? option.id || option._id
                  : option.value,
              label:
                typeof option === "string"
                  ? option
                  : "id" in option
                  ? option.name
                  : option.label,
            })) || []
          }
        />
      ) : type === "date" ? (
        <EnhancedDatePicker
          value={
            field.split(".").reduce((obj, key) => obj?.[key], data)
              ? new Date(
                  field.split(".").reduce((obj, key) => obj?.[key], data)
                )
              : undefined
          }
          onChange={(date) =>
            handleInputChange(field, date ? date.toISOString() : "")
          }
          placeholder="Pilih tanggal"
          minYear={1950}
          maxYear={new Date().getFullYear() + 10}
        />
      ) : type === "textarea" ? (
        <Textarea
          id={field}
          value={field.split(".").reduce((obj, key) => obj?.[key], data)}
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={`Masukkan ${label.toLowerCase()}`}
          rows={3}
        />
      ) : (
        <Input
          id={field}
          type={type}
          value={field.split(".").reduce((obj, key) => obj?.[key], data)}
          onChange={(e) =>
            handleInputChange(
              field,
              type === "number" ? Number(e.target.value) : e.target.value
            )
          }
          placeholder={`Masukkan ${label.toLowerCase()}`}
        />
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informasi HR</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Employee Information */}
        {renderFormGroup(
          "Kepegawaian",
          <>
            {renderField(
              "Nomor Induk Karyawan",
              "employeeId",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Posisi Jabatan",
              "position",
              "searchable-select",
              positions
                .filter(
                  (position) =>
                    (position.id || position._id) &&
                    position.name &&
                    position.isActive !== false
                )
                .map((position) => ({
                  id: position.id || position._id,
                  name: position.name,
                })),
              true
            )}
            {renderField(
              "Divisi",
              "division",
              "searchable-select",
              divisions
                .filter(
                  (div) =>
                    (div.id || div._id) && div.name && div.isActive === true
                )
                .map((div) => ({
                  id: div.id || div._id,
                  name: div.name,
                })),
              true
            )}
            {renderField(
              "Departemen",
              "department",
              "searchable-select",
              departments
                .filter(
                  (dept) =>
                    (dept.id || dept._id) && dept.name && dept.isActive === true
                )
                .map((dept) => ({
                  id: dept.id || dept._id,
                  name: dept.name,
                })),
              true
            )}
            {renderField(
              "Email Perusahaan",
              "companyEmail",
              "text",
              undefined,
              false
            )}
            {renderField(
              "Manager",
              "manager",
              "searchable-select",
              employeeOptions,
              false
            )}
            {renderField(
              "Atasan Langsung",
              "directSupervisor",
              "searchable-select",
              employeeOptions,
              false
            )}
            {renderField(
              "Status Karyawan",
              "status",
              "select",
              statusOptions,
              true
            )}
          </>
        )}

        <Separator />

        {/* Contract Information */}
        {renderFormGroup(
          "Kontrak",
          <>
            {renderField(
              "Jenis Hubungan Kerja",
              "contract.employmentType",
              "searchable-select",
              employmentTypes,
              true
            )}
            {renderField(
              "Tanggal Masuk",
              "contract.hireDate",
              "date",
              undefined,
              true
            )}
            {renderField("Tanggal Kontrak", "contract.contractDate", "date")}
            {renderField(
              "Tanggal Akhir Kontrak",
              "contract.contractEndDate",
              "date"
            )}
            {renderField("Tanggal Permanent", "contract.permanentDate", "date")}
            {renderField("Tanggal Keluar", "contract.exitDate", "date")}
          </>
        )}

        <Separator />

        {/* Education */}
        {renderFormGroup(
          "Pendidikan",
          <>
            {renderField(
              "Tingkat Pendidikan",
              "education.certificateLevel",
              "select",
              certificateLevelOptions,
              true
            )}
            {renderField(
              "Bidang Studi",
              "education.fieldOfStudy",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Sekolah",
              "education.schoolName",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Kota Sekolah",
              "education.schoolCity",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Status Kelulusan",
              "education.graduationStatus",
              "select",
              graduationStatusOptions
            )}
            {renderField("Keterangan", "education.description", "textarea")}
          </>
        )}

        <Separator />

        {/* Rank and Grade */}
        {renderFormGroup(
          "Pangkat dan Golongan",
          <>
            {renderField(
              "Kategori Pangkat",
              "rank.rankCategory",
              "searchable-select",
              rankCategories
            )}
            {renderField(
              "Golongan Pangkat",
              "rank.rankGrade",
              "searchable-select",
              rankGrades
            )}
            {renderField(
              "Sub Golongan Pangkat",
              "rank.rankSubgrade",
              "searchable-select",
              rankSubgrades
            )}
            {renderField("No Dana Pensiun", "rank.pensionFundNumber", "text")}
          </>
        )}

        <Separator />

        {/* Emergency Contact */}
        {renderFormGroup(
          "Kontak Darurat",
          <>
            {renderField(
              "Nama Kontak",
              "emergency.contactName",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Telepon Kontak",
              "emergency.contactPhone",
              "text",
              undefined,
              true
            )}
            {renderField("Telepon Kontak 2", "emergency.contactPhone2", "text")}
            {renderField(
              "Hubungan",
              "emergency.relationship",
              "select",
              relationshipOptions,
              true
            )}
            {renderField(
              "Alamat",
              "emergency.address",
              "textarea",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* POO/POH */}
        {renderFormGroup(
          "POO/POH",
          <>
            {renderField(
              "Point of Origin (POO)",
              "location.pointOfOrigin",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Point of Hire (POH)",
              "location.pointOfHire",
              "text",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* Uniform and Work Shoes */}
        {renderFormGroup(
          "Seragam dan Sepatu Kerja",
          <>
            {renderField(
              "Ukuran Seragam Kerja",
              "uniform.workUniformSize",
              "select",
              uniformSizeOptions,
              true
            )}
            {renderField(
              "Ukuran Sepatu Kerja",
              "uniform.workShoesSize",
              "text",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* Salary */}
        {renderFormGroup(
          "Gaji",
          <>
            {renderField(
              "Gaji Pokok",
              "salary.basic",
              "number",
              undefined,
              false
            )}
            {renderField(
              "Tunjangan Transport",
              "salary.allowances.transport",
              "number"
            )}
            {renderField("Tunjangan Makan", "salary.allowances.meal", "number")}
            {renderField(
              "Tunjangan Komunikasi",
              "salary.allowances.communication",
              "number"
            )}
            {renderField(
              "Tunjangan Jabatan",
              "salary.allowances.position",
              "number"
            )}
            {renderField(
              "Tunjangan Lainnya",
              "salary.allowances.other",
              "number"
            )}
          </>
        )}

        <Separator />

        {/* Work Schedule */}
        {renderFormGroup(
          "Jadwal Kerja",
          <>
            {renderField(
              "Jadwal Kerja",
              "workSchedule",
              "select",
              workScheduleOptions
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
