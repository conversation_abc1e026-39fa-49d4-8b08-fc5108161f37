"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  UserCheck,
  UserX,
  Building2,
  TrendingUp,
  DollarSign,
  Calendar,
  Award,
} from "lucide-react";

interface EmployeeStatsProps {
  stats?: {
    total: number;
    active: number;
    inactive: number;
    avgSalary: number;
    byDepartment: Array<{
      _id: string;
      count: number;
    }>;
  };
}

const EmployeeStats: React.FC<EmployeeStatsProps> = ({ stats }) => {
  // Mock data jika stats tidak tersedia
  const defaultStats = {
    total: 8,
    active: 7,
    inactive: 1,
    avgSalary: ********,
    byDepartment: [
      { _id: "Information Technology", count: 2 },
      { _id: "Human Resources", count: 2 },
      { _id: "Finance & Accounting", count: 1 },
      { _id: "Operations", count: 2 },
      { _id: "Marketing & Sales", count: 1 },
    ],
  };

  const employeeStats = stats || defaultStats;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getActivePercentage = () => {
    if (employeeStats.total === 0) return 0;
    return Math.round((employeeStats.active / employeeStats.total) * 100);
  };

  const getDepartmentColor = (index: number) => {
    const colors = [
      "bg-blue-100 text-blue-800",
      "bg-green-100 text-green-800",
      "bg-purple-100 text-purple-800",
      "bg-orange-100 text-orange-800",
      "bg-pink-100 text-pink-800",
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Employees */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Karyawan</p>
                <p className="text-3xl font-bold text-gray-900">{employeeStats.total}</p>
                <p className="text-xs text-gray-500 mt-1">
                  Semua karyawan terdaftar
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Active Employees */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Karyawan Aktif</p>
                <p className="text-3xl font-bold text-green-600">{employeeStats.active}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {getActivePercentage()}% dari total
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <UserCheck className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Inactive Employees */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tidak Aktif</p>
                <p className="text-3xl font-bold text-red-600">{employeeStats.inactive}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {100 - getActivePercentage()}% dari total
                </p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <UserX className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Average Salary */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rata-rata Gaji</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatCurrency(employeeStats.avgSalary)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Per bulan
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Building2 className="w-5 h-5" />
              <span>Distribusi per Departemen</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {employeeStats.byDepartment.map((dept, index) => (
              <div key={dept._id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium text-gray-900">
                    {dept._id}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className={getDepartmentColor(index)}>
                    {dept.count} orang
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {Math.round((dept.count / employeeStats.total) * 100)}%
                  </span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Ringkasan Bulan Ini</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-full">
                  <UserCheck className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Karyawan Baru</p>
                  <p className="text-xs text-gray-500">Bergabung bulan ini</p>
                </div>
              </div>
              <span className="text-lg font-bold text-green-600">2</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-full">
                  <Calendar className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Ulang Tahun</p>
                  <p className="text-xs text-gray-500">Bulan ini</p>
                </div>
              </div>
              <span className="text-lg font-bold text-blue-600">3</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-full">
                  <Award className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Anniversary</p>
                  <p className="text-xs text-gray-500">Tahun kerja</p>
                </div>
              </div>
              <span className="text-lg font-bold text-purple-600">1</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 rounded-full">
                  <UserX className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Masa Probasi</p>
                  <p className="text-xs text-gray-500">Berakhir bulan ini</p>
                </div>
              </div>
              <span className="text-lg font-bold text-orange-600">0</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Aktivitas Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-green-100 rounded-full">
                <UserCheck className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  Maya Putri bergabung sebagai HR Specialist
                </p>
                <p className="text-xs text-gray-500">2 hari yang lalu</p>
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-blue-100 rounded-full">
                <TrendingUp className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  Ahmad Fauzi dipromosikan menjadi IT Manager
                </p>
                <p className="text-xs text-gray-500">1 minggu yang lalu</p>
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-purple-100 rounded-full">
                <Award className="w-4 h-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  Siti Nurhaliza merayakan 1 tahun bekerja
                </p>
                <p className="text-xs text-gray-500">2 minggu yang lalu</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeStats;
