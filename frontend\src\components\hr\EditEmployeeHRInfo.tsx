"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { Separator } from "@/components/ui/separator";
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker";

interface HRInfoData {
  division: string;
  department: string;
  position: string;
  employmentType: string;
  hireDate: string;
  probationEndDate: string;
  contractEndDate: string;

  // Enhanced HR fields
  contract: {
    employmentType: string;
    hireDate: string;
    contractDate: string;
    contractEndDate: string;
    permanentDate: string;
    exitDate: string;
  };

  // Education
  education: {
    certificateLevel: string;
    fieldOfStudy: string;
    schoolName: string;
    schoolCity: string;
    graduationStatus: string;
    description: string;
  };

  // Rank and Grade
  rank: {
    rankCategory: string;
    rankGrade: string;
    rankSubgrade: string;
    pensionFundNumber: string;
  };

  // Emergency Contact
  emergency: {
    contactName: string;
    contactPhone: string;
    contactPhone2: string;
    relationship: string;
    address: string;
  };

  // POO/POH
  location: {
    pointOfOrigin: string;
    pointOfHire: string;
  };

  // Uniform and Work Shoes
  uniform: {
    workUniformSize: string;
    workShoesSize: string;
  };

  salary: {
    basic: string;
    allowances: {
      transport: string;
      meal: string;
      communication: string;
      position: string;
      other: string;
    };
  };
  workSchedule: string;
  supervisor: string;
  manager: string;
  directSupervisor: string;
  tags: string[];
  status: string;
}

interface EditEmployeeHRInfoProps {
  data: HRInfoData;
  onUpdate: (data: HRInfoData) => void;
  masterData: {
    divisions: any[];
    departments: any[];
    positions: any[];
    employmentTypes: any[];
    tags: any[];
    employees: any[];
    rankCategories: any[];
    rankGrades: any[];
    rankSubgrades: any[];
  };
  onTagToggle: (tagId: string) => void;
}

const educationLevels = [
  "SD",
  "SMP",
  "SMA",
  "SMK",
  "D1",
  "D2",
  "D3",
  "S1",
  "S2",
  "S3",
];

const workScheduleOptions = [
  "Regular",
  "Shift",
  "Flexible",
  "Remote",
  "Part Time",
];

const uniformSizes = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];

export default function EditEmployeeHRInfo({
  data,
  onUpdate,
  masterData,
  onTagToggle,
}: EditEmployeeHRInfoProps) {
  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    let newData = { ...data };

    if (keys.length === 1) {
      newData[field as keyof HRInfoData] = value;
    } else if (keys.length === 2) {
      const [level1, level2] = keys;
      newData[level1 as keyof HRInfoData] = {
        ...newData[level1 as keyof HRInfoData],
        [level2]: value,
      } as any;
    } else if (keys.length === 3) {
      const [level1, level2, level3] = keys;
      newData[level1 as keyof HRInfoData] = {
        ...newData[level1 as keyof HRInfoData],
        [level2]: {
          ...(newData[level1 as keyof HRInfoData] as any)?.[level2],
          [level3]: value,
        },
      } as any;
    }

    onUpdate(newData);
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type:
      | "text"
      | "select"
      | "searchable-select"
      | "date"
      | "number"
      | "textarea" = "text",
    options?: any[] | string[],
    required = false,
    placeholder?: string
  ) => (
    <div>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {type === "select" ? (
        <Select
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
              : (data[field as keyof HRInfoData] as string)
          }
          onValueChange={(value) => handleInputChange(field, value)}
        >
          <SelectTrigger>
            <SelectValue
              placeholder={placeholder || `Pilih ${label.toLowerCase()}`}
            />
          </SelectTrigger>
          <SelectContent>
            {(options as string[])?.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : type === "searchable-select" ? (
        <SearchableSelect
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
              : (data[field as keyof HRInfoData] as string)
          }
          onValueChange={(value) => handleInputChange(field, value)}
          placeholder={placeholder || `Pilih ${label.toLowerCase()}`}
          searchPlaceholder={`Cari ${label.toLowerCase()}...`}
          emptyMessage={`Tidak ada ${label.toLowerCase()} ditemukan.`}
          options={options as any[]}
        />
      ) : type === "date" ? (
        <EnhancedDatePicker
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
                ? new Date(
                    field.split(".").reduce((obj, key) => obj?.[key], data)
                  )
                : undefined
              : data[field as keyof HRInfoData]
              ? new Date(data[field as keyof HRInfoData] as string)
              : undefined
          }
          onChange={(date) =>
            handleInputChange(field, date ? date.toISOString() : "")
          }
          placeholder="Pilih tanggal"
        />
      ) : type === "textarea" ? (
        <Textarea
          id={field}
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data) || ""
              : (data[field as keyof HRInfoData] as string) || ""
          }
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={placeholder || `Masukkan ${label.toLowerCase()}`}
          rows={3}
        />
      ) : (
        <Input
          id={field}
          type={type}
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data) || ""
              : (data[field as keyof HRInfoData] as string) || ""
          }
          onChange={(e) =>
            handleInputChange(
              field,
              type === "number" ? Number(e.target.value) : e.target.value
            )
          }
          placeholder={placeholder || `Masukkan ${label.toLowerCase()}`}
        />
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informasi HR</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Employee Information */}
        {renderFormGroup(
          "Kepegawaian",
          <>
            {renderField(
              "Nomor Induk Karyawan",
              "employeeId",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Posisi Jabatan",
              "position",
              "searchable-select",
              masterData.positions
                .filter(
                  (position) =>
                    (position.id || position._id) &&
                    position.name &&
                    position.isActive !== false
                )
                .map((position) => ({
                  value: position.id || position._id,
                  label: position.name,
                })),
              true
            )}
            {renderField(
              "Divisi",
              "division",
              "searchable-select",
              masterData.divisions
                .filter(
                  (div) =>
                    (div.id || div._id) && div.name && div.isActive === true
                )
                .map((div) => ({
                  value: div.id || div._id,
                  label: div.name,
                })),
              true
            )}
            {renderField(
              "Departemen",
              "department",
              "searchable-select",
              masterData.departments
                .filter(
                  (dept) =>
                    (dept.id || dept._id) && dept.name && dept.isActive === true
                )
                .map((dept) => ({
                  value: dept.id || dept._id,
                  label: dept.name,
                })),
              true
            )}
            {renderField(
              "Email Perusahaan",
              "companyEmail",
              "text",
              undefined,
              false
            )}
            {renderField(
              "Manager",
              "manager",
              "searchable-select",
              masterData.employees
                .filter(
                  (emp) =>
                    (emp.id || emp._id) &&
                    emp.personal?.fullName &&
                    emp.isActive !== false
                )
                .map((emp) => ({
                  value: emp.id || emp._id,
                  label: `${emp.personal?.fullName} (${emp.personal?.employeeId})`,
                })),
              false
            )}
            {renderField(
              "Atasan Langsung",
              "directSupervisor",
              "searchable-select",
              masterData.employees
                .filter(
                  (emp) =>
                    (emp.id || emp._id) &&
                    emp.personal?.fullName &&
                    emp.isActive !== false
                )
                .map((emp) => ({
                  value: emp.id || emp._id,
                  label: `${emp.personal?.fullName} (${emp.personal?.employeeId})`,
                })),
              false
            )}
            {renderField(
              "Status Karyawan",
              "status",
              "select",
              [
                "Aktif",
                "Probation",
                "Cuti",
                "Notice Period",
                "Tidak Aktif",
                "Resign",
                "Terminated",
                "Pension",
                "Kontrak Habis",
              ],
              true
            )}
          </>
        )}

        <Separator />

        {/* Contract Information */}
        {renderFormGroup(
          "Kontrak",
          <>
            {renderField(
              "Jenis Hubungan Kerja",
              "contract.employmentType",
              "searchable-select",
              masterData.employmentTypes
                .filter((type) => type.isActive !== false)
                .map((type) => ({
                  value: type.id || type._id,
                  label: type.name,
                })),
              true
            )}
            {renderField(
              "Tanggal Masuk",
              "contract.hireDate",
              "date",
              undefined,
              true
            )}
            {renderField("Tanggal Kontrak", "contract.contractDate", "date")}
            {renderField(
              "Tanggal Akhir Kontrak",
              "contract.contractEndDate",
              "date"
            )}
            {renderField("Tanggal Permanent", "contract.permanentDate", "date")}
            {renderField("Tanggal Keluar", "contract.exitDate", "date")}
          </>
        )}

        <Separator />

        {/* Education */}
        {renderFormGroup(
          "Pendidikan",
          <>
            {renderField(
              "Tingkat Pendidikan",
              "education.certificateLevel",
              "select",
              [
                "SD",
                "SMP",
                "SMA/SMK",
                "D1",
                "D2",
                "D3",
                "D4",
                "S1",
                "S2",
                "S3",
              ],
              true
            )}
            {renderField(
              "Bidang Studi",
              "education.fieldOfStudy",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Nama Sekolah",
              "education.schoolName",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Kota Sekolah",
              "education.schoolCity",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Status Kelulusan",
              "education.graduationStatus",
              "select",
              ["Lulus", "Tidak Lulus", "Sedang Belajar"]
            )}
            {renderField("Keterangan", "education.description", "textarea")}
          </>
        )}

        <Separator />

        {/* Rank and Grade */}
        {renderFormGroup(
          "Pangkat dan Golongan",
          <>
            {renderField(
              "Kategori Pangkat",
              "rank.rankCategory",
              "searchable-select",
              masterData.rankCategories
                .filter((cat) => cat.isActive !== false)
                .map((cat) => ({
                  value: cat.id || cat._id,
                  label: cat.name,
                }))
            )}
            {renderField(
              "Golongan Pangkat",
              "rank.rankGrade",
              "searchable-select",
              masterData.rankGrades
                .filter((grade) => grade.isActive !== false)
                .map((grade) => ({
                  value: grade.id || grade._id,
                  label: grade.name,
                }))
            )}
            {renderField(
              "Sub Golongan Pangkat",
              "rank.rankSubgrade",
              "searchable-select",
              masterData.rankSubgrades
                .filter((subgrade) => subgrade.isActive !== false)
                .map((subgrade) => ({
                  value: subgrade.id || subgrade._id,
                  label: subgrade.name,
                }))
            )}
            {renderField("No Dana Pensiun", "rank.pensionFundNumber", "text")}
          </>
        )}

        <Separator />

        {/* Emergency Contact */}
        {renderFormGroup(
          "Kontak Darurat",
          <>
            {renderField(
              "Nama Kontak",
              "emergency.contactName",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Telepon Kontak",
              "emergency.contactPhone",
              "text",
              undefined,
              true
            )}
            {renderField("Telepon Kontak 2", "emergency.contactPhone2", "text")}
            {renderField(
              "Hubungan",
              "emergency.relationship",
              "select",
              [
                "Orang Tua",
                "Suami/Istri",
                "Anak",
                "Saudara Kandung",
                "Kerabat",
                "Teman",
                "Lainnya",
              ],
              true
            )}
            {renderField(
              "Alamat",
              "emergency.address",
              "textarea",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* POO/POH */}
        {renderFormGroup(
          "POO/POH",
          <>
            {renderField(
              "Point of Origin (POO)",
              "location.pointOfOrigin",
              "text",
              undefined,
              true
            )}
            {renderField(
              "Point of Hire (POH)",
              "location.pointOfHire",
              "text",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* Uniform and Work Shoes */}
        {renderFormGroup(
          "Seragam dan Sepatu Kerja",
          <>
            {renderField(
              "Ukuran Seragam Kerja",
              "uniform.workUniformSize",
              "select",
              ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
              true
            )}
            {renderField(
              "Ukuran Sepatu Kerja",
              "uniform.workShoesSize",
              "text",
              undefined,
              true
            )}
          </>
        )}

        <Separator />

        {/* Salary */}
        {renderFormGroup(
          "Gaji",
          <>
            {renderField(
              "Gaji Pokok",
              "salary.basic",
              "number",
              undefined,
              false
            )}
            {renderField(
              "Tunjangan Transport",
              "salary.allowances.transport",
              "number"
            )}
            {renderField("Tunjangan Makan", "salary.allowances.meal", "number")}
            {renderField(
              "Tunjangan Komunikasi",
              "salary.allowances.communication",
              "number"
            )}
            {renderField(
              "Tunjangan Jabatan",
              "salary.allowances.position",
              "number"
            )}
            {renderField(
              "Tunjangan Lainnya",
              "salary.allowances.other",
              "number"
            )}
          </>
        )}

        <Separator />

        {/* Work Schedule */}
        {renderFormGroup(
          "Jadwal Kerja",
          <>
            {renderField("Jadwal Kerja", "workSchedule", "select", [
              "Regular",
              "Shift",
              "Flexible",
              "Remote",
              "Part Time",
            ])}
          </>
        )}
      </CardContent>
    </Card>
  );
}
