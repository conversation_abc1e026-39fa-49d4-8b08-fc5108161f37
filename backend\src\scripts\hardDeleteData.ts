import mongoose from "mongoose";
import Division from "../modules/hr/models/Division";
import { Department } from "../modules/hr/models/Department";

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect("mongodb://localhost:27017/psg-sisinfo");
    console.log("✅ MongoDB connected successfully");
  } catch (error) {
    console.error("❌ MongoDB connection failed:", error);
    process.exit(1);
  }
};

// Hard delete all data
const hardDeleteAllData = async () => {
  try {
    console.log("🗄️ STARTING HARD DELETE FROM DATABASE...");
    
    // 1. Delete all divisions
    console.log("\n📋 Deleting all divisions...");
    const divisionResult = await Division.deleteMany({});
    console.log(`✅ Deleted ${divisionResult.deletedCount} divisions`);
    
    // 2. Delete all departments
    console.log("\n🏢 Deleting all departments...");
    const departmentResult = await Department.deleteMany({});
    console.log(`✅ Deleted ${departmentResult.deletedCount} departments`);
    
    // 3. Verify deletion
    console.log("\n🔍 Verifying deletion...");
    const remainingDivisions = await Division.countDocuments();
    const remainingDepartments = await Department.countDocuments();
    
    console.log(`📋 Remaining divisions: ${remainingDivisions}`);
    console.log(`🏢 Remaining departments: ${remainingDepartments}`);
    
    if (remainingDivisions === 0 && remainingDepartments === 0) {
      console.log("\n🎉 ALL DATA SUCCESSFULLY DELETED FROM DATABASE!");
    } else {
      console.log("\n⚠️  Some data still remains in database");
    }
    
  } catch (error) {
    console.error("❌ Error during hard delete:", error);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  }
};

// Run the script
const main = async () => {
  await connectDB();
  await hardDeleteAllData();
};

main().catch((error) => {
  console.error("❌ Script failed:", error);
  process.exit(1);
});
