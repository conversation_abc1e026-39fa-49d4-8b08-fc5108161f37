{"name": "bebang-information-system", "version": "1.0.0", "description": "Bebang Information System - PT. Prima Sarana Gemilang", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "npm run service:start", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:safe": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm run start", "start:frontend": "cd frontend && npm run start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules .next", "setup": "npm run install:all && npm run setup:env", "setup:env": "cp backend/.env.example backend/.env && cp frontend/.env.local.example frontend/.env.local", "seed": "cd backend && npm run seed", "service:start": "scripts\\service-manager.bat start both", "service:stop": "scripts\\service-manager.bat stop both", "service:restart": "scripts\\service-manager.bat restart both", "service:status": "scripts\\service-manager.bat status", "service:kill": "scripts\\service-manager.bat kill-all"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["bebang", "information-system", "psg", "taliabu", "nodejs", "nextjs", "mongodb"], "author": "PT. Prima Sarana Gemilang", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}