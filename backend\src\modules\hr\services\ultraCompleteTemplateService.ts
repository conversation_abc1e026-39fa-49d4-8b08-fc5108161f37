import ExcelJS from "exceljs";

export interface TemplateField {
  column: string;
  field: string;
  label: string;
  required: boolean;
  example: string;
  validation?: string[];
}

// ULTRA COMPLETE EMPLOYEE TEMPLATE - ALL DATABASE FIELDS
export const ULTRA_COMPLETE_TEMPLATE_FIELDS: TemplateField[] = [
  // === PERSONAL INFORMATION ===
  {
    column: "A",
    field: "personal.employeeId",
    label: "NIK*",
    required: true,
    example: "EMP001",
  },
  {
    column: "B",
    field: "personal.fullName",
    label: "Nama <PERSON>gkap*",
    required: true,
    example: "John Doe",
  },
  {
    column: "C",
    field: "personal.gender",
    label: "<PERSON><PERSON>",
    required: true,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "D",
    field: "personal.placeOfBirth",
    label: "Tempat Lahir*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "E",
    field: "personal.dateOfBirth",
    label: "<PERSON>gal Lahir*",
    required: true,
    example: "15-01-1990",
  },
  {
    column: "F",
    field: "personal.email",
    label: "Email Pribadi",
    required: false,
    example: "<EMAIL>",
  },
  {
    column: "G",
    field: "personal.phone",
    label: "No HP*",
    required: true,
    example: "08**********",
  },
  {
    column: "H",
    field: "personal.religion",
    label: "Agama*",
    required: true,
    example: "Islam",
    validation: [
      "Islam",
      "Kristen",
      "Katolik",
      "Hindu",
      "Buddha",
      "Konghucu",
      "Lainnya",
    ],
  },
  {
    column: "I",
    field: "personal.bloodType",
    label: "Golongan Darah*",
    required: true,
    example: "A",
    validation: ["A", "B", "AB", "O"],
  },
  {
    column: "J",
    field: "personal.familyCardNumber",
    label: "No KK*",
    required: true,
    example: "317**********123",
  },
  {
    column: "K",
    field: "personal.idCardNumber",
    label: "No KTP*",
    required: true,
    example: "317**********123",
  },
  {
    column: "L",
    field: "personal.taxNumber",
    label: "NPWP",
    required: false,
    example: "**********12345",
  },
  {
    column: "M",
    field: "personal.bpjsTkNumber",
    label: "No BPJS TK",
    required: false,
    example: "**********1",
  },
  {
    column: "N",
    field: "personal.nikKkNumber",
    label: "NIK KK",
    required: false,
    example: "317**********123",
  },
  {
    column: "O",
    field: "personal.taxStatus",
    label: "Status Pajak*",
    required: true,
    example: "TK/0",
    validation: ["TK/0", "TK/1", "TK/2", "TK/3", "K/0", "K/1", "K/2", "K/3"],
  },

  // === ADDRESS INFORMATION ===
  {
    column: "P",
    field: "personal.currentAddress.street",
    label: "Alamat Domisili - Jalan*",
    required: true,
    example: "Jl. Sudirman No. 123",
  },
  {
    column: "Q",
    field: "personal.currentAddress.city",
    label: "Alamat Domisili - Kota*",
    required: true,
    example: "Jakarta Pusat",
  },
  {
    column: "R",
    field: "personal.currentAddress.province",
    label: "Alamat Domisili - Provinsi*",
    required: true,
    example: "DKI Jakarta",
  },
  {
    column: "S",
    field: "personal.idCardAddress.street",
    label: "Alamat KTP - Jalan*",
    required: true,
    example: "Jl. Thamrin No. 456",
  },
  {
    column: "T",
    field: "personal.idCardAddress.city",
    label: "Alamat KTP - Kota*",
    required: true,
    example: "Jakarta Pusat",
  },
  {
    column: "U",
    field: "personal.idCardAddress.province",
    label: "Alamat KTP - Provinsi*",
    required: true,
    example: "DKI Jakarta",
  },

  // === CONTACT INFORMATION ===
  {
    column: "V",
    field: "personal.contact.mobilePhone1",
    label: "HP 1*",
    required: true,
    example: "08**********",
  },
  {
    column: "W",
    field: "personal.contact.mobilePhone2",
    label: "HP 2",
    required: false,
    example: "081234567891",
  },
  {
    column: "X",
    field: "personal.contact.homePhone1",
    label: "Telepon Rumah 1",
    required: false,
    example: "0212345678",
  },
  {
    column: "Y",
    field: "personal.contact.homePhone2",
    label: "Telepon Rumah 2",
    required: false,
    example: "0212345679",
  },

  // === MARITAL INFORMATION ===
  {
    column: "Z",
    field: "personal.maritalInfo.status",
    label: "Status Pernikahan*",
    required: true,
    example: "Menikah",
    validation: ["Belum Menikah", "Menikah", "Cerai Hidup", "Cerai Mati"],
  },
  {
    column: "AA",
    field: "personal.maritalInfo.spouseName",
    label: "Nama Pasangan",
    required: false,
    example: "Jane Smith",
  },
  {
    column: "AB",
    field: "personal.maritalInfo.spouseJob",
    label: "Pekerjaan Pasangan",
    required: false,
    example: "Guru",
  },
  {
    column: "AC",
    field: "personal.maritalInfo.numberOfChildren",
    label: "Jumlah Anak",
    required: false,
    example: "2",
  },

  // === BANK ACCOUNT ===
  {
    column: "AD",
    field: "personal.bankAccount.number",
    label: "No Rekening",
    required: false,
    example: "**********",
  },
  {
    column: "AE",
    field: "personal.bankAccount.holder",
    label: "Nama Pemegang Rekening",
    required: false,
    example: "John Doe",
  },
  {
    column: "AF",
    field: "personal.bankAccount.name",
    label: "Nama Bank",
    required: false,
    example: "Bank Mandiri",
  },

  // === HR INFORMATION ===
  {
    column: "AG",
    field: "hr.division",
    label: "Divisi*",
    required: true,
    example: "OPERATIONAL",
  },
  {
    column: "AH",
    field: "hr.department",
    label: "Departemen*",
    required: true,
    example: "PRODUKSI",
  },
  {
    column: "AI",
    field: "hr.position",
    label: "Jabatan*",
    required: true,
    example: "Staff",
  },
  {
    column: "AJ",
    field: "hr.companyEmail",
    label: "Email Perusahaan",
    required: false,
    example: "<EMAIL>",
  },

  // === CONTRACT INFORMATION ===
  {
    column: "AK",
    field: "hr.contract.employmentType",
    label: "Status Kerja*",
    required: true,
    example: "TETAP",
    validation: [
      "TETAP",
      "KONTRAK",
      "PROBATION",
      "MAGANG",
      "FREELANCE",
      "KONSULTAN",
    ],
  },
  {
    column: "AL",
    field: "hr.contract.hireDate",
    label: "Tanggal Masuk*",
    required: true,
    example: "15-01-2024",
  },
  {
    column: "AM",
    field: "hr.contract.contractDate",
    label: "Tanggal Kontrak",
    required: false,
    example: "15-01-2024",
  },
  {
    column: "AN",
    field: "hr.contract.contractEndDate",
    label: "Tanggal Berakhir Kontrak",
    required: false,
    example: "14-01-2025",
  },
  {
    column: "AO",
    field: "hr.contract.permanentDate",
    label: "Tanggal Tetap",
    required: false,
    example: "15-01-2025",
  },

  // === EDUCATION INFORMATION ===
  {
    column: "AP",
    field: "hr.education.certificateLevel",
    label: "Pendidikan Terakhir*",
    required: true,
    example: "S1",
    validation: ["SD", "SMP", "SMA", "SMK", "D1", "D2", "D3", "S1", "S2", "S3"],
  },
  {
    column: "AQ",
    field: "hr.education.fieldOfStudy",
    label: "Jurusan*",
    required: true,
    example: "Teknik Informatika",
  },
  {
    column: "AR",
    field: "hr.education.schoolName",
    label: "Nama Sekolah/Universitas*",
    required: true,
    example: "Universitas Indonesia",
  },
  {
    column: "AS",
    field: "hr.education.schoolCity",
    label: "Kota Sekolah*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "AT",
    field: "hr.education.graduationStatus",
    label: "Status Kelulusan",
    required: false,
    example: "Lulus",
    validation: ["Lulus", "Tidak Lulus", "Sedang Belajar"],
  },
  {
    column: "AU",
    field: "hr.education.description",
    label: "Deskripsi Pendidikan",
    required: false,
    example: "Cumlaude dengan IPK 3.8",
  },

  // === RANK AND GRADE ===
  {
    column: "AV",
    field: "hr.rank.rankCategory",
    label: "Kategori Pangkat",
    required: false,
    example: "Golongan III",
  },
  {
    column: "AW",
    field: "hr.rank.rankGrade",
    label: "Tingkat Pangkat",
    required: false,
    example: "Grade A",
  },
  {
    column: "AX",
    field: "hr.rank.rankSubgrade",
    label: "Sub Tingkat Pangkat",
    required: false,
    example: "Sub Grade 1",
  },
  {
    column: "AY",
    field: "hr.rank.pensionFundNumber",
    label: "No Dana Pensiun",
    required: false,
    example: "DP123456789",
  },

  // === EMERGENCY CONTACT ===
  {
    column: "AZ",
    field: "hr.emergency.contactName",
    label: "Nama Kontak Darurat*",
    required: true,
    example: "Jane Doe",
  },
  {
    column: "BA",
    field: "hr.emergency.contactPhone",
    label: "HP Kontak Darurat*",
    required: true,
    example: "081234567891",
  },
  {
    column: "BB",
    field: "hr.emergency.contactPhone2",
    label: "HP Kontak Darurat 2",
    required: false,
    example: "081234567892",
  },
  {
    column: "BC",
    field: "hr.emergency.relationship",
    label: "Hubungan Kontak Darurat*",
    required: true,
    example: "Istri",
  },
  {
    column: "BD",
    field: "hr.emergency.address",
    label: "Alamat Kontak Darurat*",
    required: true,
    example: "Jl. Sudirman No. 123, Jakarta",
  },

  // === POO/POH ===
  {
    column: "BE",
    field: "hr.location.pointOfOrigin",
    label: "Point of Origin (POO)*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "BF",
    field: "hr.location.pointOfHire",
    label: "Point of Hire (POH)*",
    required: true,
    example: "Taliabu",
  },

  // === UNIFORM AND WORK SHOES ===
  {
    column: "BG",
    field: "hr.uniform.workUniformSize",
    label: "Ukuran Seragam Kerja*",
    required: true,
    example: "L",
    validation: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
  },
  {
    column: "BH",
    field: "hr.uniform.workShoesSize",
    label: "Ukuran Sepatu Kerja*",
    required: true,
    example: "42",
  },

  // === SALARY ===
  {
    column: "BI",
    field: "hr.salary.basic",
    label: "Gaji Pokok",
    required: false,
    example: "5000000",
  },
  {
    column: "BJ",
    field: "hr.salary.allowances.transport",
    label: "Tunjangan Transport",
    required: false,
    example: "500000",
  },
  {
    column: "BK",
    field: "hr.salary.allowances.meal",
    label: "Tunjangan Makan",
    required: false,
    example: "300000",
  },
  {
    column: "BL",
    field: "hr.salary.allowances.communication",
    label: "Tunjangan Komunikasi",
    required: false,
    example: "200000",
  },
  {
    column: "BM",
    field: "hr.salary.allowances.position",
    label: "Tunjangan Jabatan",
    required: false,
    example: "1000000",
  },
  {
    column: "BN",
    field: "hr.salary.allowances.other",
    label: "Tunjangan Lainnya",
    required: false,
    example: "100000",
  },

  // === WORK SCHEDULE ===
  {
    column: "BO",
    field: "hr.workSchedule.type",
    label: "Jenis Jadwal Kerja*",
    required: true,
    example: "Regular",
    validation: ["Regular", "Shift", "Flexible", "Remote", "Part Time"],
  },
  {
    column: "BP",
    field: "hr.workSchedule.startTime",
    label: "Jam Masuk",
    required: false,
    example: "08:00",
  },
  {
    column: "BQ",
    field: "hr.workSchedule.endTime",
    label: "Jam Pulang",
    required: false,
    example: "17:00",
  },
  {
    column: "BR",
    field: "hr.workSchedule.breakTime",
    label: "Jam Istirahat",
    required: false,
    example: "12:00-13:00",
  },

  // === FAMILY INFORMATION ===
  {
    column: "BS",
    field: "family.fatherName",
    label: "Nama Ayah",
    required: false,
    example: "Robert Doe",
  },
  {
    column: "BT",
    field: "family.motherName",
    label: "Nama Ibu",
    required: false,
    example: "Maria Doe",
  },
  {
    column: "BU",
    field: "family.spouse.name",
    label: "Nama Pasangan",
    required: false,
    example: "Jane Smith",
  },
  {
    column: "BV",
    field: "family.spouse.dateOfBirth",
    label: "Tanggal Lahir Pasangan",
    required: false,
    example: "20-05-1992",
  },
  {
    column: "BW",
    field: "family.spouse.occupation",
    label: "Pekerjaan Pasangan",
    required: false,
    example: "Guru",
  },
  {
    column: "BX",
    field: "family.spouse.numberOfChildren",
    label: "Jumlah Anak",
    required: false,
    example: "2",
  },

  // === CHILDREN INFORMATION (1-4) ===
  {
    column: "BY",
    field: "family.children.0.name",
    label: "Nama Anak 1",
    required: false,
    example: "Alice Doe",
  },
  {
    column: "BZ",
    field: "family.children.0.dateOfBirth",
    label: "Tanggal Lahir Anak 1",
    required: false,
    example: "10-03-2015",
  },
  {
    column: "CA",
    field: "family.children.0.gender",
    label: "Jenis Kelamin Anak 1",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CB",
    field: "family.children.0.education",
    label: "Pendidikan Anak 1",
    required: false,
    example: "SD",
  },
  {
    column: "CC",
    field: "family.children.0.status",
    label: "Status Anak 1",
    required: false,
    example: "Sekolah",
  },
  {
    column: "CD",
    field: "family.children.1.name",
    label: "Nama Anak 2",
    required: false,
    example: "Bob Doe",
  },
  {
    column: "CE",
    field: "family.children.1.dateOfBirth",
    label: "Tanggal Lahir Anak 2",
    required: false,
    example: "15-07-2017",
  },
  {
    column: "CF",
    field: "family.children.1.gender",
    label: "Jenis Kelamin Anak 2",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CG",
    field: "family.children.1.education",
    label: "Pendidikan Anak 2",
    required: false,
    example: "TK",
  },
  {
    column: "CH",
    field: "family.children.1.status",
    label: "Status Anak 2",
    required: false,
    example: "Sekolah",
  },
  {
    column: "CI",
    field: "family.children.2.name",
    label: "Nama Anak 3",
    required: false,
    example: "Charlie Doe",
  },
  {
    column: "CJ",
    field: "family.children.2.dateOfBirth",
    label: "Tanggal Lahir Anak 3",
    required: false,
    example: "22-11-2019",
  },
  {
    column: "CK",
    field: "family.children.2.gender",
    label: "Jenis Kelamin Anak 3",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CL",
    field: "family.children.2.education",
    label: "Pendidikan Anak 3",
    required: false,
    example: "Belum Sekolah",
  },
  {
    column: "CM",
    field: "family.children.2.status",
    label: "Status Anak 3",
    required: false,
    example: "Belum Sekolah",
  },
  {
    column: "CN",
    field: "family.children.3.name",
    label: "Nama Anak 4",
    required: false,
    example: "Diana Doe",
  },
  {
    column: "CO",
    field: "family.children.3.dateOfBirth",
    label: "Tanggal Lahir Anak 4",
    required: false,
    example: "05-09-2021",
  },
  {
    column: "CP",
    field: "family.children.3.gender",
    label: "Jenis Kelamin Anak 4",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CQ",
    field: "family.children.3.education",
    label: "Pendidikan Anak 4",
    required: false,
    example: "Belum Sekolah",
  },
  {
    column: "CR",
    field: "family.children.3.status",
    label: "Status Anak 4",
    required: false,
    example: "Belum Sekolah",
  },

  // === SIBLINGS INFORMATION (1-4) ===
  {
    column: "CS",
    field: "family.siblings.0.name",
    label: "Nama Saudara 1",
    required: false,
    example: "Michael Doe",
  },
  {
    column: "CT",
    field: "family.siblings.0.dateOfBirth",
    label: "Tanggal Lahir Saudara 1",
    required: false,
    example: "12-08-1988",
  },
  {
    column: "CU",
    field: "family.siblings.0.gender",
    label: "Jenis Kelamin Saudara 1",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CV",
    field: "family.siblings.0.occupation",
    label: "Pekerjaan Saudara 1",
    required: false,
    example: "Dokter",
  },
  {
    column: "CW",
    field: "family.siblings.0.address",
    label: "Alamat Saudara 1",
    required: false,
    example: "Jakarta",
  },
  {
    column: "CX",
    field: "family.siblings.0.maritalStatus",
    label: "Status Pernikahan Saudara 1",
    required: false,
    example: "Menikah",
  },
  {
    column: "CY",
    field: "family.siblings.1.name",
    label: "Nama Saudara 2",
    required: false,
    example: "Sarah Doe",
  },
  {
    column: "CZ",
    field: "family.siblings.1.dateOfBirth",
    label: "Tanggal Lahir Saudara 2",
    required: false,
    example: "25-12-1992",
  },
  {
    column: "DA",
    field: "family.siblings.1.gender",
    label: "Jenis Kelamin Saudara 2",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "DB",
    field: "family.siblings.1.occupation",
    label: "Pekerjaan Saudara 2",
    required: false,
    example: "Guru",
  },
  {
    column: "DC",
    field: "family.siblings.1.address",
    label: "Alamat Saudara 2",
    required: false,
    example: "Bandung",
  },
  {
    column: "DD",
    field: "family.siblings.1.maritalStatus",
    label: "Status Pernikahan Saudara 2",
    required: false,
    example: "Belum Menikah",
  },
  {
    column: "DE",
    field: "family.siblings.2.name",
    label: "Nama Saudara 3",
    required: false,
    example: "David Doe",
  },
  {
    column: "DF",
    field: "family.siblings.2.dateOfBirth",
    label: "Tanggal Lahir Saudara 3",
    required: false,
    example: "18-04-1995",
  },
  {
    column: "DG",
    field: "family.siblings.2.gender",
    label: "Jenis Kelamin Saudara 3",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "DH",
    field: "family.siblings.2.occupation",
    label: "Pekerjaan Saudara 3",
    required: false,
    example: "Engineer",
  },
  {
    column: "DI",
    field: "family.siblings.2.address",
    label: "Alamat Saudara 3",
    required: false,
    example: "Surabaya",
  },
  {
    column: "DJ",
    field: "family.siblings.2.maritalStatus",
    label: "Status Pernikahan Saudara 3",
    required: false,
    example: "Menikah",
  },
  {
    column: "DK",
    field: "family.siblings.3.name",
    label: "Nama Saudara 4",
    required: false,
    example: "Lisa Doe",
  },
  {
    column: "DL",
    field: "family.siblings.3.dateOfBirth",
    label: "Tanggal Lahir Saudara 4",
    required: false,
    example: "30-06-1997",
  },
  {
    column: "DM",
    field: "family.siblings.3.gender",
    label: "Jenis Kelamin Saudara 4",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "DN",
    field: "family.siblings.3.occupation",
    label: "Pekerjaan Saudara 4",
    required: false,
    example: "Designer",
  },
  {
    column: "DO",
    field: "family.siblings.3.address",
    label: "Alamat Saudara 4",
    required: false,
    example: "Yogyakarta",
  },
  {
    column: "DP",
    field: "family.siblings.3.maritalStatus",
    label: "Status Pernikahan Saudara 4",
    required: false,
    example: "Belum Menikah",
  },
];

export class UltraCompleteExcelTemplateService {
  async generateUltraCompleteTemplate(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Employee Data
    const dataSheet = workbook.addWorksheet("Employee_Data");

    // Add headers
    const headerRow = dataSheet.addRow(
      ULTRA_COMPLETE_TEMPLATE_FIELDS.map((field) => field.label)
    );
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE6F3FF" },
    };

    // Add example data
    const exampleRow = dataSheet.addRow(
      ULTRA_COMPLETE_TEMPLATE_FIELDS.map((field) => field.example)
    );
    exampleRow.font = { italic: true };
    exampleRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFF0F8FF" },
    };

    // Auto-fit columns
    dataSheet.columns.forEach((column, index) => {
      const field = ULTRA_COMPLETE_TEMPLATE_FIELDS[index];
      column.width = Math.max(field.label.length, field.example.length) + 3;
    });

    return this.addReferenceAndInstructionSheets(workbook);
  }

  private async addReferenceAndInstructionSheets(
    workbook: ExcelJS.Workbook
  ): Promise<Buffer> {
    // Sheet 2: Reference Data
    const refSheet = workbook.addWorksheet("Reference_Data");

    // Add reference data for dropdowns
    const referenceData = {
      "Jenis Kelamin": ["Laki-laki", "Perempuan"],
      Agama: [
        "Islam",
        "Kristen",
        "Katolik",
        "Hindu",
        "Buddha",
        "Konghucu",
        "Lainnya",
      ],
      "Golongan Darah": ["A", "B", "AB", "O"],
      "Status Pajak": [
        "TK/0",
        "TK/1",
        "TK/2",
        "TK/3",
        "K/0",
        "K/1",
        "K/2",
        "K/3",
      ],
      "Status Pernikahan": [
        "Belum Menikah",
        "Menikah",
        "Cerai Hidup",
        "Cerai Mati",
      ],
      Pendidikan: [
        "SD",
        "SMP",
        "SMA",
        "SMK",
        "D1",
        "D2",
        "D3",
        "S1",
        "S2",
        "S3",
      ],
      "Status Kerja": [
        "TETAP",
        "KONTRAK",
        "PROBATION",
        "MAGANG",
        "FREELANCE",
        "KONSULTAN",
      ],
      "Status Kelulusan": ["Lulus", "Tidak Lulus", "Sedang Belajar"],
      "Ukuran Seragam": ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
      "Jadwal Kerja": ["Regular", "Shift", "Flexible", "Remote", "Part Time"],
    };

    let currentCol = 1;
    Object.entries(referenceData).forEach(([title, values]) => {
      refSheet.getCell(1, currentCol).value = title;
      refSheet.getCell(1, currentCol).font = { bold: true };

      values.forEach((value, index) => {
        refSheet.getCell(index + 2, currentCol).value = value;
      });

      currentCol++;
    });

    // Convert to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}

export const ultraCompleteExcelTemplateService =
  new UltraCompleteExcelTemplateService();
