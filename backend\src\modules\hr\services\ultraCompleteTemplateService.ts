import ExcelJS from "exceljs";

export interface TemplateField {
  column: string;
  field: string;
  label: string;
  required: boolean;
  example: string;
  validation?: string[];
}

// ULTRA COMPLETE EMPLOYEE TEMPLATE - ALL DATABASE FIELDS
export const ULTRA_COMPLETE_TEMPLATE_FIELDS: TemplateField[] = [
  // === PERSONAL INFORMATION ===
  {
    column: "A",
    field: "personal.employeeId",
    label: "Nomor Induk <PERSON>*",
    required: true,
    example: "EMP001",
  },
  {
    column: "B",
    field: "personal.fullName",
    label: "<PERSON><PERSON>*",
    required: true,
    example: "John <PERSON>e",
  },
  {
    column: "C",
    field: "personal.gender",
    label: "<PERSON><PERSON>",
    required: true,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "D",
    field: "personal.placeOfBirth",
    label: "Tempat Lahir*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "E",
    field: "personal.dateOfBirth",
    label: "<PERSON><PERSON>*",
    required: true,
    example: "15-01-1990",
  },
  {
    column: "F",
    field: "personal.email",
    label: "Email",
    required: false,
    example: "<EMAIL>",
  },
  {
    column: "G",
    field: "personal.phone",
    label: "No HP*",
    required: true,
    example: "08**********",
  },
  {
    column: "H",
    field: "personal.religion",
    label: "Agama*",
    required: true,
    example: "Islam",
    validation: [
      "Islam",
      "Kristen",
      "Katolik",
      "Hindu",
      "Buddha",
      "Konghucu",
      "Lainnya",
    ],
  },
  {
    column: "I",
    field: "personal.bloodType",
    label: "Golongan Darah*",
    required: true,
    example: "A",
    validation: ["A", "B", "AB", "O"],
  },
  {
    column: "J",
    field: "personal.familyCardNumber",
    label: "No KK*",
    required: true,
    example: "317**********123",
  },
  {
    column: "K",
    field: "personal.idCardNumber",
    label: "No KTP*",
    required: true,
    example: "317**********123",
  },
  {
    column: "L",
    field: "personal.taxNumber",
    label: "NPWP",
    required: false,
    example: "**********12345",
  },
  {
    column: "M",
    field: "personal.bpjsTkNumber",
    label: "No BPJS TK",
    required: false,
    example: "**********1",
  },
  {
    column: "N",
    field: "personal.nikKkNumber",
    label: "NIK KK",
    required: false,
    example: "317**********123",
  },
  {
    column: "O",
    field: "personal.taxStatus",
    label: "Status Pajak*",
    required: true,
    example: "TK/0",
    validation: ["TK/0", "TK/1", "TK/2", "TK/3", "K/0", "K/1", "K/2", "K/3"],
  },

  // === ADDRESS INFORMATION ===
  {
    column: "P",
    field: "personal.currentAddress.street",
    label: "Jalan*",
    required: true,
    example: "Jl. Sudirman No. 123",
  },
  {
    column: "Q",
    field: "personal.currentAddress.city",
    label: "Kota*",
    required: true,
    example: "Jakarta Pusat",
  },
  {
    column: "R",
    field: "personal.currentAddress.province",
    label: "Provinsi*",
    required: true,
    example: "DKI Jakarta",
  },
  {
    column: "S",
    field: "personal.idCardAddress.street",
    label: "Jalan*",
    required: true,
    example: "Jl. Thamrin No. 456",
  },
  {
    column: "T",
    field: "personal.idCardAddress.city",
    label: "Kota*",
    required: true,
    example: "Jakarta Pusat",
  },
  {
    column: "U",
    field: "personal.idCardAddress.province",
    label: "Provinsi*",
    required: true,
    example: "DKI Jakarta",
  },

  // === CONTACT INFORMATION ===
  {
    column: "V",
    field: "personal.contact.mobilePhone1",
    label: "HP 1 (Sinkron dengan Nomor Handphone di Header)*",
    required: true,
    example: "08**********",
  },
  {
    column: "W",
    field: "personal.contact.mobilePhone2",
    label: "HP 2",
    required: false,
    example: "081234567891",
  },
  {
    column: "X",
    field: "personal.contact.homePhone1",
    label: "Telepon Rumah 1",
    required: false,
    example: "0212345678",
  },
  {
    column: "Y",
    field: "personal.contact.homePhone2",
    label: "Telepon Rumah 2",
    required: false,
    example: "0212345679",
  },

  // === MARITAL INFORMATION ===
  {
    column: "Z",
    field: "personal.maritalInfo.status",
    label: "Status Pernikahan*",
    required: true,
    example: "Menikah",
    validation: ["Belum Menikah", "Menikah", "Cerai Hidup", "Cerai Mati"],
  },
  {
    column: "AA",
    field: "personal.maritalInfo.spouseName",
    label: "Nama Pasangan",
    required: false,
    example: "Jane Smith",
  },
  {
    column: "AB",
    field: "personal.maritalInfo.spouseJob",
    label: "Pekerjaan Pasangan",
    required: false,
    example: "Guru",
  },
  {
    column: "AC",
    field: "personal.maritalInfo.numberOfChildren",
    label: "Jumlah Anak",
    required: false,
    example: "2",
  },

  // === BANK ACCOUNT ===
  {
    column: "AD",
    field: "personal.bankAccount.accountNumber",
    label: "Nomor Rekening",
    required: false,
    example: "**********",
  },
  {
    column: "AE",
    field: "personal.bankAccount.accountHolder",
    label: "Nama Pemegang Rekening",
    required: false,
    example: "John Doe",
  },
  {
    column: "AF",
    field: "personal.bankAccount.bankName",
    label: "Nama Bank",
    required: false,
    example: "Bank Mandiri",
  },

  // === HR INFORMATION ===
  {
    column: "AG",
    field: "hr.division",
    label: "Divisi*",
    required: true,
    example: "OPERATIONAL",
  },
  {
    column: "AH",
    field: "hr.department",
    label: "Departemen*",
    required: true,
    example: "PRODUKSI",
  },
  {
    column: "AI",
    field: "hr.position",
    label: "Posisi Jabatan*",
    required: true,
    example: "Staff",
  },
  {
    column: "AJ",
    field: "hr.companyEmail",
    label: "Email Perusahaan",
    required: false,
    example: "<EMAIL>",
  },

  // === CONTRACT INFORMATION ===
  {
    column: "AK",
    field: "hr.contract.employmentType",
    label: "Jenis Hubungan Kerja*",
    required: true,
    example: "PERMANEN",
    validation: [
      "PERMANEN",
      "PROBATION",
      "KONTRAK 1",
      "KONTRAK 2",
      "KONTRAK 3",
      "KONTRAK 4",
      "KONTRAK 5",
      "KONTRAK 6",
      "KONTRAK 7",
      "KONTRAK 8",
      "KONTRAK 9",
      "KONTRAK 10",
    ],
  },
  {
    column: "AL",
    field: "hr.contract.hireDate",
    label: "Tanggal Masuk*",
    required: true,
    example: "15-01-2024",
  },
  {
    column: "AM",
    field: "hr.contract.contractDate",
    label: "Tanggal Kontrak",
    required: false,
    example: "15-01-2024",
  },
  {
    column: "AN",
    field: "hr.contract.contractEndDate",
    label: "Tanggal Berakhir Kontrak",
    required: false,
    example: "14-01-2025",
  },
  {
    column: "AO",
    field: "hr.contract.permanentDate",
    label: "Tanggal Permanent",
    required: false,
    example: "15-01-2025",
  },
  {
    column: "AP",
    field: "hr.contract.exitDate",
    label: "Tanggal Keluar",
    required: false,
    example: "31-12-2024",
  },

  // === EDUCATION INFORMATION ===
  {
    column: "AQ",
    field: "hr.education.certificateLevel",
    label: "Pendidikan Terakhir*",
    required: true,
    example: "S1",
    validation: ["SD", "SMP", "SMA", "SMK", "D1", "D2", "D3", "S1", "S2", "S3"],
  },
  {
    column: "AR",
    field: "hr.education.fieldOfStudy",
    label: "Bidang Studi*",
    required: true,
    example: "Teknik Informatika",
  },
  {
    column: "AS",
    field: "hr.education.schoolName",
    label: "Nama Sekolah*",
    required: true,
    example: "Universitas Indonesia",
  },
  {
    column: "AT",
    field: "hr.education.schoolCity",
    label: "Kota Sekolah*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "AU",
    field: "hr.education.graduationStatus",
    label: "Status Kelulusan",
    required: false,
    example: "Lulus",
    validation: ["Lulus", "Tidak Lulus", "Sedang Belajar"],
  },
  {
    column: "AV",
    field: "hr.education.description",
    label: "Keterangan",
    required: false,
    example: "Cumlaude dengan IPK 3.8",
  },

  // === RANK AND GRADE ===
  {
    column: "AW",
    field: "hr.rank.rankCategory",
    label: "Kategori Pangkat",
    required: false,
    example: "Golongan III",
  },
  {
    column: "AX",
    field: "hr.rank.rankGrade",
    label: "Golongan Pangkat",
    required: false,
    example: "Grade A",
  },
  {
    column: "AY",
    field: "hr.rank.rankSubgrade",
    label: "Sub Golongan Pangkat",
    required: false,
    example: "Sub Grade 1",
  },
  {
    column: "AZ",
    field: "hr.rank.pensionFundNumber",
    label: "No Dana Pensiun",
    required: false,
    example: "DP123456789",
  },

  // === EMERGENCY CONTACT ===
  {
    column: "BA",
    field: "hr.emergency.contactName",
    label: "Nama Kontak*",
    required: true,
    example: "Jane Doe",
  },
  {
    column: "BB",
    field: "hr.emergency.contactPhone",
    label: "Telepon Kontak*",
    required: true,
    example: "081234567891",
  },
  {
    column: "BC",
    field: "hr.emergency.contactPhone2",
    label: "Telepon Kontak 2",
    required: false,
    example: "081234567892",
  },
  {
    column: "BD",
    field: "hr.emergency.relationship",
    label: "Hubungan*",
    required: true,
    example: "Istri",
  },
  {
    column: "BE",
    field: "hr.emergency.address",
    label: "Alamat*",
    required: true,
    example: "Jl. Sudirman No. 123, Jakarta",
  },

  // === SECOND EMERGENCY CONTACT ===
  {
    column: "BE",
    field: "hr.emergency.contactName2",
    label: "Nama Kontak Darurat 2",
    required: false,
    example: "John Smith",
  },
  {
    column: "BF",
    field: "hr.emergency.contactPhone3",
    label: "HP Kontak Darurat 2",
    required: false,
    example: "081234567893",
  },
  {
    column: "BG",
    field: "hr.emergency.relationship2",
    label: "Hubungan Kontak Darurat 2",
    required: false,
    example: "Saudara",
  },
  {
    column: "BH",
    field: "hr.emergency.address2",
    label: "Alamat Kontak Darurat 2",
    required: false,
    example: "Jl. Thamrin No. 456, Jakarta",
  },

  // === POO/POH ===
  {
    column: "BI",
    field: "hr.location.pointOfOrigin",
    label: "Point of Origin (POO)*",
    required: true,
    example: "Jakarta",
  },
  {
    column: "BJ",
    field: "hr.location.pointOfHire",
    label: "Point of Hire (POH)*",
    required: true,
    example: "Taliabu",
  },

  // === UNIFORM AND WORK SHOES ===
  {
    column: "BK",
    field: "hr.uniform.workUniformSize",
    label: "Ukuran Seragam Kerja*",
    required: true,
    example: "L",
    validation: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
  },
  {
    column: "BL",
    field: "hr.uniform.workShoesSize",
    label: "Ukuran Sepatu Kerja*",
    required: true,
    example: "42",
  },

  // === SALARY ===
  {
    column: "BM",
    field: "hr.salary.basic",
    label: "Gaji Pokok",
    required: false,
    example: "5000000",
  },
  {
    column: "BN",
    field: "hr.salary.allowances.transport",
    label: "Tunjangan Transport",
    required: false,
    example: "500000",
  },
  {
    column: "BO",
    field: "hr.salary.allowances.meal",
    label: "Tunjangan Makan",
    required: false,
    example: "300000",
  },
  {
    column: "BP",
    field: "hr.salary.allowances.communication",
    label: "Tunjangan Komunikasi",
    required: false,
    example: "200000",
  },
  {
    column: "BQ",
    field: "hr.salary.allowances.position",
    label: "Tunjangan Jabatan",
    required: false,
    example: "1000000",
  },
  {
    column: "BR",
    field: "hr.salary.allowances.other",
    label: "Tunjangan Lainnya",
    required: false,
    example: "100000",
  },

  // === WORK SCHEDULE ===
  {
    column: "BS",
    field: "hr.workSchedule",
    label: "Jenis Jadwal Kerja*",
    required: true,
    example: "Regular",
    validation: ["Regular", "Shift", "Flexible", "Remote", "Part Time"],
  },

  // === MANAGEMENT ===
  {
    column: "BT",
    field: "hr.manager",
    label: "Manager",
    required: false,
    example: "EMP001",
  },
  {
    column: "BU",
    field: "hr.directSupervisor",
    label: "Atasan Langsung",
    required: false,
    example: "EMP002",
  },

  // === EMPLOYEE STATUS ===
  {
    column: "BV",
    field: "status",
    label: "Status Karyawan*",
    required: true,
    example: "Aktif",
    validation: ["Aktif", "Probation", "Cuti", "Tidak Aktif", "Notice Period"],
  },


  // === FAMILY INFORMATION ===
  {
    column: "BW",
    field: "family.parents.father.name",
    label: "Nama Ayah",
    required: false,
    example: "Robert Doe",
  },
  {
    column: "BX",
    field: "family.parents.mother.name",
    label: "Nama Ibu",
    required: false,
    example: "Maria Doe",
  },
  {
    column: "BY",
    field: "family.spouse.name",
    label: "Nama Pasangan",
    required: false,
    example: "Jane Smith",
  },
  {
    column: "BZ",
    field: "family.spouse.dateOfBirth",
    label: "Tanggal Lahir Pasangan",
    required: false,
    example: "20-05-1992",
  },
  {
    column: "CA",
    field: "family.spouse.occupation",
    label: "Pekerjaan Pasangan",
    required: false,
    example: "Guru",
  },
  {
    column: "CB",
    field: "family.spouse.numberOfChildren",
    label: "Jumlah Anak",
    required: false,
    example: "2",
  },

  // === CHILDREN INFORMATION (1-4) ===
  {
    column: "CC",
    field: "family.children.0.name",
    label: "Nama Anak 1",
    required: false,
    example: "Alice Doe",
  },
  {
    column: "CD",
    field: "family.children.0.dateOfBirth",
    label: "Tanggal Lahir Anak 1",
    required: false,
    example: "10-03-2015",
  },
  {
    column: "CE",
    field: "family.children.0.gender",
    label: "Jenis Kelamin Anak 1",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },

  {
    column: "DK",
    field: "family.children.1.name",
    label: "Nama Anak 2",
    required: false,
    example: "Bob Doe",
  },
  {
    column: "CF",
    field: "family.children.1.dateOfBirth",
    label: "Tanggal Lahir Anak 2",
    required: false,
    example: "15-07-2017",
  },
  {
    column: "CG",
    field: "family.children.1.gender",
    label: "Jenis Kelamin Anak 2",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },

  {
    column: "CH",
    field: "family.children.2.name",
    label: "Nama Anak 3",
    required: false,
    example: "Charlie Doe",
  },
  {
    column: "CI",
    field: "family.children.2.dateOfBirth",
    label: "Tanggal Lahir Anak 3",
    required: false,
    example: "22-11-2019",
  },
  {
    column: "CJ",
    field: "family.children.2.gender",
    label: "Jenis Kelamin Anak 3",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },

  {
    column: "CK",
    field: "family.children.3.name",
    label: "Nama Anak 4",
    required: false,
    example: "Diana Doe",
  },
  {
    column: "CL",
    field: "family.children.3.dateOfBirth",
    label: "Tanggal Lahir Anak 4",
    required: false,
    example: "05-09-2021",
  },
  {
    column: "CM",
    field: "family.children.3.gender",
    label: "Jenis Kelamin Anak 4",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },


  // === SIBLINGS INFORMATION ===
  {
    column: "CN",
    field: "family.siblings.childOrder",
    label: "Anak Ke-",
    required: false,
    example: "2",
  },
  {
    column: "CO",
    field: "family.siblings.totalSiblings",
    label: "Jumlah Saudara Kandung",
    required: false,
    example: "3",
  },
  {
    column: "CP",
    field: "family.siblings.siblingsData.0.name",
    label: "Nama Saudara 1",
    required: false,
    example: "Michael Doe",
  },
  {
    column: "CQ",
    field: "family.siblings.siblingsData.0.dateOfBirth",
    label: "Tanggal Lahir Saudara 1",
    required: false,
    example: "12-08-1988",
  },
  {
    column: "CR",
    field: "family.siblings.siblingsData.0.gender",
    label: "Jenis Kelamin Saudara 1",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CS",
    field: "family.siblings.siblingsData.0.occupation",
    label: "Pekerjaan Saudara 1",
    required: false,
    example: "Dokter",
  },

  // Sibling 2
  {
    column: "CT",
    field: "family.siblings.siblingsData.1.name",
    label: "Nama Saudara 2",
    required: false,
    example: "Sarah Doe",
  },
  {
    column: "CU",
    field: "family.siblings.siblingsData.1.dateOfBirth",
    label: "Tanggal Lahir Saudara 2",
    required: false,
    example: "25-12-1992",
  },
  {
    column: "CV",
    field: "family.siblings.siblingsData.1.gender",
    label: "Jenis Kelamin Saudara 2",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "CW",
    field: "family.siblings.siblingsData.1.occupation",
    label: "Pekerjaan Saudara 2",
    required: false,
    example: "Guru",
  },

  // Sibling 3
  {
    column: "CX",
    field: "family.siblings.siblingsData.2.name",
    label: "Nama Saudara 3",
    required: false,
    example: "David Doe",
  },
  {
    column: "CY",
    field: "family.siblings.siblingsData.2.dateOfBirth",
    label: "Tanggal Lahir Saudara 3",
    required: false,
    example: "18-04-1995",
  },
  {
    column: "CZ",
    field: "family.siblings.siblingsData.2.gender",
    label: "Jenis Kelamin Saudara 3",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "DA",
    field: "family.siblings.siblingsData.2.occupation",
    label: "Pekerjaan Saudara 3",
    required: false,
    example: "Engineer",
  },

  // Sibling 4
  {
    column: "DB",
    field: "family.siblings.siblingsData.3.name",
    label: "Nama Saudara 4",
    required: false,
    example: "Lisa Doe",
  },
  {
    column: "DC",
    field: "family.siblings.siblingsData.3.dateOfBirth",
    label: "Tanggal Lahir Saudara 4",
    required: false,
    example: "30-06-1997",
  },
  {
    column: "DD",
    field: "family.siblings.siblingsData.3.gender",
    label: "Jenis Kelamin Saudara 4",
    required: false,
    example: "Perempuan",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "DE",
    field: "family.siblings.siblingsData.3.occupation",
    label: "Pekerjaan Saudara 4",
    required: false,
    example: "Designer",
  },

  // Sibling 5
  {
    column: "DF",
    field: "family.siblings.siblingsData.4.name",
    label: "Nama Saudara 5",
    required: false,
    example: "Mark Doe",
  },
  {
    column: "DG",
    field: "family.siblings.siblingsData.4.dateOfBirth",
    label: "Tanggal Lahir Saudara 5",
    required: false,
    example: "15-01-2000",
  },
  {
    column: "DH",
    field: "family.siblings.siblingsData.4.gender",
    label: "Jenis Kelamin Saudara 5",
    required: false,
    example: "Laki-laki",
    validation: ["Laki-laki", "Perempuan"],
  },
  {
    column: "DI",
    field: "family.siblings.siblingsData.4.occupation",
    label: "Pekerjaan Saudara 5",
    required: false,
    example: "Student",
  },


];

export class UltraCompleteExcelTemplateService {
  async generateUltraCompleteTemplate(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Employee Data
    const dataSheet = workbook.addWorksheet("Employee_Data");

    // Add headers
    const headerRow = dataSheet.addRow(
      ULTRA_COMPLETE_TEMPLATE_FIELDS.map((field) => field.label)
    );
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE6F3FF" },
    };

    // Add example data
    const exampleRow = dataSheet.addRow(
      ULTRA_COMPLETE_TEMPLATE_FIELDS.map((field) => field.example)
    );
    exampleRow.font = { italic: true };
    exampleRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFF0F8FF" },
    };

    // Auto-fit columns
    dataSheet.columns.forEach((column, index) => {
      const field = ULTRA_COMPLETE_TEMPLATE_FIELDS[index];
      column.width = Math.max(field.label.length, field.example.length) + 3;
    });

    return this.addReferenceAndInstructionSheets(workbook);
  }

  private async addReferenceAndInstructionSheets(
    workbook: ExcelJS.Workbook
  ): Promise<Buffer> {
    // Sheet 2: Reference Data
    const refSheet = workbook.addWorksheet("Reference_Data");

    // Add reference data for dropdowns
    const referenceData = {
      "Jenis Kelamin": ["Laki-laki", "Perempuan"],
      Agama: [
        "Islam",
        "Kristen",
        "Katolik",
        "Hindu",
        "Buddha",
        "Konghucu",
        "Lainnya",
      ],
      "Golongan Darah": ["A", "B", "AB", "O"],
      "Status Pajak": [
        "TK/0",
        "TK/1",
        "TK/2",
        "TK/3",
        "K/0",
        "K/1",
        "K/2",
        "K/3",
      ],
      "Status Pernikahan": [
        "Belum Menikah",
        "Menikah",
        "Cerai Hidup",
        "Cerai Mati",
      ],
      Pendidikan: [
        "SD",
        "SMP",
        "SMA",
        "SMK",
        "D1",
        "D2",
        "D3",
        "S1",
        "S2",
        "S3",
      ],
      "Status Kerja": [
        "PERMANEN",
        "PROBATION",
        "KONTRAK 1",
        "KONTRAK 2",
        "KONTRAK 3",
        "KONTRAK 4",
        "KONTRAK 5",
        "KONTRAK 6",
        "KONTRAK 7",
        "KONTRAK 8",
        "KONTRAK 9",
        "KONTRAK 10",
      ],
      "Status Kelulusan": ["Lulus", "Tidak Lulus", "Sedang Belajar"],
      "Ukuran Seragam": ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
      "Jadwal Kerja": ["Regular", "Shift", "Flexible", "Remote", "Part Time"],
    };

    let currentCol = 1;
    Object.entries(referenceData).forEach(([title, values]) => {
      refSheet.getCell(1, currentCol).value = title;
      refSheet.getCell(1, currentCol).font = { bold: true };

      values.forEach((value, index) => {
        refSheet.getCell(index + 2, currentCol).value = value;
      });

      currentCol++;
    });

    // Convert to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}

export const ultraCompleteExcelTemplateService =
  new UltraCompleteExcelTemplateService();
