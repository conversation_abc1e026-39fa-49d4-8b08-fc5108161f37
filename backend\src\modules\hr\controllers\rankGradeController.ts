import { Request, Response } from "express";
import RankGrade from "../models/RankGrade";

// Get all rank grades
export const getRankGrades = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { search, status } = req.query;

    // Build filter - show all records (active and inactive)
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const rankGrades = await RankGrade.find(filter).sort({ createdAt: -1 });

    res.json({
      success: true,
      data: rankGrades,
      message: "Rank grades retrieved successfully",
      count: rankGrades.length,
    });
  } catch (error) {
    console.error("Error fetching rank grades:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get rank grade by ID
export const getRankGradeById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const rankGrade = await RankGrade.findById(id);

    if (!rankGrade) {
      res.status(404).json({
        success: false,
        message: "Rank grade not found",
      });
      return;
    }

    res.json({
      success: true,
      data: rankGrade,
      message: "Rank grade retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching rank grade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Create new rank grade
export const createRankGrade = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { name, description, isActive = true } = req.body;

    // Check if rank grade already exists (only check active ones)
    const existingRankGrade = await RankGrade.findOne({
      name,
      isActive: true,
    });
    if (existingRankGrade) {
      res.status(400).json({
        success: false,
        message: "Rank grade already exists",
      });
      return;
    }

    const rankGrade = new RankGrade({
      name,
      description,
      isActive, // Set active status
      // Skip createdBy/updatedBy for development
    });

    await rankGrade.save();

    res.status(201).json({
      success: true,
      data: rankGrade,
      message: "Rank grade created successfully",
    });
  } catch (error) {
    console.error("Error creating rank grade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Update rank grade
export const updateRankGrade = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description, isActive } = req.body;

    const rankGrade = await RankGrade.findById(id);
    if (!rankGrade) {
      res.status(404).json({
        success: false,
        message: "Rank grade not found",
      });
      return;
    }

    // Update rank grade
    rankGrade.name = name || rankGrade.name;
    rankGrade.description = description || rankGrade.description;
    if (isActive !== undefined) rankGrade.isActive = isActive; // Update active status
    // Skip updatedBy for development

    await rankGrade.save();

    res.json({
      success: true,
      data: rankGrade,
      message: "Rank grade updated successfully",
    });
  } catch (error) {
    console.error("Error updating rank grade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Delete rank grade (status-based delete)
export const deleteRankGrade = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const rankGrade = await RankGrade.findById(id);
    if (!rankGrade) {
      res.status(404).json({
        success: false,
        message: "Rank grade not found",
      });
      return;
    }

    // Status-based delete
    rankGrade.isActive = false;
    // Skip updatedBy for development

    await rankGrade.save();

    res.json({
      success: true,
      message: "Rank grade deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting rank grade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
