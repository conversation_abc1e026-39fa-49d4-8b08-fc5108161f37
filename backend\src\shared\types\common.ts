import { Document, Types } from 'mongoose';

// Base interfaces
export interface BaseDocument extends Document {
  _id: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimestampFields {
  createdAt: Date;
  updatedAt: Date;
  createdBy?: Types.ObjectId;
  updatedBy?: Types.ObjectId;
}

export interface SoftDeleteFields {
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: Types.ObjectId;
}

// API Response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// User and Permission types
export interface UserRole {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  permissions: Permission[];
  isActive: boolean;
}

export interface Permission {
  _id: Types.ObjectId;
  module: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'approve';
  resource?: string;
  conditions?: Record<string, any>;
}

export interface UserSession {
  userId: Types.ObjectId;
  email: string;
  role: string;
  permissions: string[];
  loginAt: Date;
  lastActivity: Date;
  ipAddress?: string;
  userAgent?: string;
}

// File upload types
export interface FileUpload {
  _id: Types.ObjectId;
  originalName: string;
  filename: string;
  path: string;
  mimetype: string;
  size: number;
  uploadedBy: Types.ObjectId;
  uploadedAt: Date;
  module: string;
  entityId?: Types.ObjectId;
  entityType?: string;
}

// Audit trail types
export interface AuditLog {
  _id: Types.ObjectId;
  entityType: string;
  entityId: Types.ObjectId;
  action: 'create' | 'update' | 'delete' | 'approve' | 'reject';
  changes?: {
    before?: Record<string, any>;
    after?: Record<string, any>;
  };
  userId: Types.ObjectId;
  userEmail: string;
  timestamp: Date;
  module: string;
  ipAddress?: string;
  userAgent?: string;
  reason?: string;
}

// Approval workflow types
export interface ApprovalWorkflow {
  _id: Types.ObjectId;
  entityType: string;
  entityId: Types.ObjectId;
  requestedBy: Types.ObjectId;
  requestedAt: Date;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  approvers: ApprovalStep[];
  currentStep: number;
  completedAt?: Date;
  reason?: string;
}

export interface ApprovalStep {
  stepNumber: number;
  approverId: Types.ObjectId;
  approverEmail: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedAt?: Date;
  comments?: string;
  isRequired: boolean;
}

// Notification types
export interface Notification {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  module: string;
  entityId?: Types.ObjectId;
  entityType?: string;
  isRead: boolean;
  readAt?: Date;
  createdAt: Date;
  expiresAt?: Date;
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
  code?: string;
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Search and filter types
export interface SearchFilter {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'exists';
  value: any;
}

export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
}

// Status enums
export enum RecordStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  ARCHIVED = 'archived'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

// Utility types
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

export type Required<T> = {
  [P in keyof T]-?: T[P];
};

export type Pick<T, K extends keyof T> = {
  [P in K]: T[P];
};

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
