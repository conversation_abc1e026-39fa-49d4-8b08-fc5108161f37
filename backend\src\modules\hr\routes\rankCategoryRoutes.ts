import express from "express";
import { body } from "express-validator";
import {
  getRankCategories,
  getRankCategoryById,
  createRankCategory,
  updateRankCategory,
  deleteRankCategory,
} from "../controllers/rankCategoryController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const rankCategoryValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Rank category name is required")
    .isLength({ min: 2, max: 100 })
    .withMessage("Rank category name must be between 2 and 100 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Description must not exceed 500 characters"),
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean value"),
];

// Routes
router.get("/", devBypass, getRankCategories);
router.get("/:id", devBypass, getRankCategoryById);
router.post("/", devBypass, rankCategoryValidation, createRankCategory);
router.put("/:id", devBypass, rankCategoryValidation, updateRankCategory);
router.delete("/:id", devBypass, deleteRankCategory);

export default router;
