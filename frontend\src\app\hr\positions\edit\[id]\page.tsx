"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "sonner";
import { ArrowLeft, Save, Loader2, Check, ChevronsUpDown } from "lucide-react";
import { useRouter, useParams } from "next/navigation";
import { cn } from "@/lib/utils";

interface Department {
  _id?: string; // For backward compatibility
  id?: string; // Primary ID field from backend transform
  name: string;
  isActive: boolean;
}

const EditPositionPage = () => {
  const router = useRouter();
  const params = useParams();
  const positionId = params.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [open, setOpen] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [formData, setFormData] = useState({
    name: "",
    department: "",
    description: "",
    isActive: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch departments and position data
  useEffect(() => {
    fetchDepartments();
    if (positionId) {
      fetchPosition();
    }
  }, [positionId]);

  const fetchDepartments = async () => {
    try {
      const token = localStorage.getItem("accessToken");
      if (!token) {
        router.push("/login");
        return;
      }

      const response = await fetch("http://localhost:5000/api/hr/departments", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const result = await response.json();
      if (result.success) {
        // Filter only active departments
        const activeDepartments = result.data.filter(
          (dept: any) => dept.isActive
        );
        setDepartments(activeDepartments);
      }
    } catch (error) {
      console.error("Error fetching departments:", error);
      toast.error("Gagal memuat data department");
    }
  };

  const fetchPosition = async () => {
    try {
      setIsLoadingData(true);

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/positions/${positionId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success && result.data) {
        setFormData({
          name: result.data.name || "",
          department:
            result.data.department?.id ||
            result.data.department?._id ||
            result.data.department ||
            "",
          description: result.data.description || "",
          isActive:
            result.data.isActive !== undefined ? result.data.isActive : true,
        });
      } else {
        toast.error("Posisi jabatan tidak ditemukan", {
          description: result.message || "Data tidak dapat dimuat",
        });
        router.push("/hr/positions");
      }
    } catch (error) {
      console.error("Error fetching position:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data posisi jabatan",
      });
      router.push("/hr/positions");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const handleSwitchChange = (field: string, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nama posisi jabatan wajib diisi";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Nama posisi jabatan minimal 2 karakter";
    }

    if (!formData.department) {
      newErrors.department = "Department wajib dipilih";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Periksa kembali form yang Anda isi");
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/positions/${positionId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(formData),
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Posisi jabatan berhasil diperbarui!", {
          description: `${formData.name} telah diperbarui`,
          action: {
            label: "Lihat",
            onClick: () => router.push("/hr/positions"),
          },
        });

        // Delay navigation to show toast
        setTimeout(() => {
          router.push("/hr/positions");
        }, 1000);
      } else {
        if (result.data?.errors) {
          // Handle validation errors from backend
          const backendErrors: Record<string, string> = {};
          result.data.errors.forEach((error: any) => {
            backendErrors[error.field] = error.message;
          });
          setErrors(backendErrors);

          toast.error("Data tidak valid", {
            description: "Periksa kembali form yang Anda isi",
          });
        } else {
          toast.error("Gagal memperbarui posisi jabatan", {
            description: result.message || "Terjadi kesalahan pada server",
          });
        }
      }
    } catch (error) {
      console.error("Error updating position:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">
            Memuat data posisi jabatan...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/hr/positions")}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Posisi Jabatan
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Edit Posisi Jabatan
          </h1>
          <p className="text-gray-600 mt-1">
            Perbarui informasi posisi jabatan
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Posisi Jabatan</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Position Name */}
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Posisi Jabatan <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Masukkan nama posisi jabatan"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              {/* Department */}
              <div className="space-y-2">
                <Label htmlFor="department">
                  Department <span className="text-red-500">*</span>
                </Label>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className={cn(
                        "w-full justify-between",
                        errors.department ? "border-red-500" : ""
                      )}
                    >
                      {formData.department
                        ? departments.find(
                            (dept) =>
                              (dept.id || dept._id) === formData.department
                          )?.name
                        : "Pilih department..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Cari department..." />
                      <CommandList>
                        <CommandEmpty>Department tidak ditemukan.</CommandEmpty>
                        <CommandGroup>
                          {departments.map((dept) => {
                            const deptId = dept.id || dept._id;
                            return (
                              <CommandItem
                                key={deptId}
                                value={dept.name}
                                onSelect={() => {
                                  handleInputChange("department", deptId);
                                  setOpen(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.department === deptId
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {dept.name}
                              </CommandItem>
                            );
                          })}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {errors.department && (
                  <p className="text-sm text-red-500">{errors.department}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Keterangan</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Masukkan keterangan posisi jabatan (opsional)"
                rows={4}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <div className="flex items-center space-x-3">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("isActive", checked)
                  }
                />
                <span className="text-sm text-gray-700">
                  {formData.isActive ? "Aktif" : "Tidak Aktif"}
                </span>
              </div>
              <p className="text-xs text-gray-500">
                Posisi jabatan aktif akan muncul dalam pilihan saat membuat data
                karyawan
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/hr/positions")}
                disabled={isLoading}
              >
                Batal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Perbarui Posisi Jabatan
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditPositionPage;
