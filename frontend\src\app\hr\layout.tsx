"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  Users,
  Building2,
  BarChart3,
  Settings,
  ChevronRight,
  ArrowLeft,
  FolderOpen,
  UserCheck,
  Award,
  Briefcase,
  Tag,
  User,
  LogOut,
  ChevronDown,
  UserPlus,
  Clock,
  Calendar,
  DollarSign,
  TrendingUp,
  FileText,
  Home,
  ChevronLeft,
  Building,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Toaster } from "sonner";

interface HRLayoutProps {
  children: React.ReactNode;
}

const HRLayout = ({ children }: HRLayoutProps) => {
  const pathname = usePathname();
  const router = useRouter();

  // State untuk mengontrol collapse/expand menu
  const [collapsedMenus, setCollapsedMenus] = useState<{
    [key: string]: boolean;
  }>({
    "Master Data": false, // Default terbuka
    "Manajemen Karyawan": false, // Default terbuka
  });

  // Get user info from localStorage (you might want to use a context instead)
  const getUserInfo = () => {
    if (typeof window !== "undefined") {
      const userInfo = localStorage.getItem("userInfo");
      return userInfo ? JSON.parse(userInfo) : null;
    }
    return null;
  };

  const user = getUserInfo();

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("userInfo");

    // Redirect to login
    router.push("/login");
  };

  // Function untuk toggle collapse/expand menu
  const toggleMenu = (menuTitle: string) => {
    setCollapsedMenus((prev) => ({
      ...prev,
      [menuTitle]: !prev[menuTitle],
    }));
  };

  // Function untuk generate breadcrumb
  const generateBreadcrumb = () => {
    const pathSegments = pathname.split("/").filter(Boolean);
    const breadcrumbs = [
      { title: "Dashboard", href: "/dashboard" },
      { title: "HR", href: "/hr" },
    ];

    if (pathSegments.length > 1) {
      // Mapping untuk nama yang lebih user-friendly
      const pathMapping: { [key: string]: string } = {
        divisions: "Divisi",
        departments: "Departemen",
        positions: "Posisi Jabatan",
        "rank-categories": "Kategori Pangkat",
        "rank-grades": "Golongan",
        "rank-subgrades": "Sub Golongan",
        "employment-types": "Jenis Hubungan Kerja",
        tags: "Tags",
        employees: "Karyawan",
        recruitment: "Rekrutmen",
        attendance: "Absensi",
        leave: "Cuti & Izin",
        payroll: "Penggajian",
        performance: "Kinerja",
        documents: "Dokumen",
        reports: "Laporan",
        settings: "Pengaturan",
        add: "Tambah",
        edit: "Edit",
        create: "Buat",
      };

      // Function to check if a string is a MongoDB ObjectId
      const isObjectId = (str: string) => {
        return /^[0-9a-fA-F]{24}$/.test(str);
      };

      for (let i = 2; i < pathSegments.length; i++) {
        const segment = pathSegments[i];

        // Skip ObjectId segments in breadcrumb
        if (isObjectId(segment)) {
          continue;
        }

        const title = pathMapping[segment] || segment;
        const href = "/" + pathSegments.slice(0, i + 1).join("/");
        breadcrumbs.push({ title, href });
      }
    }

    return breadcrumbs;
  };

  const navigationItems = [
    {
      title: "Dashboard HR",
      href: "/hr",
      icon: Home,
      description: "Ringkasan dan statistik HR",
    },
    {
      title: "Master Data",
      icon: FolderOpen,
      description: "Kelola data referensi",
      submenu: [
        {
          title: "Divisi",
          href: "/hr/divisions",
          icon: Building,
          description: "Divisi perusahaan",
        },
        {
          title: "Departemen",
          href: "/hr/departments",
          icon: Building2,
          description: "Departemen perusahaan",
        },
        {
          title: "Posisi Jabatan",
          href: "/hr/positions",
          icon: UserCheck,
          description: "Jabatan karyawan",
        },
        {
          title: "Kategori Pangkat",
          href: "/hr/rank-categories",
          icon: Award,
          description: "Kategori pangkat karyawan",
        },
        {
          title: "Golongan",
          href: "/hr/rank-grades",
          icon: Award,
          description: "Golongan pangkat",
        },
        {
          title: "Sub Golongan",
          href: "/hr/rank-subgrades",
          icon: Award,
          description: "Sub golongan pangkat",
        },
        {
          title: "Jenis Hubungan",
          href: "/hr/employment-types",
          icon: Briefcase,
          description: "Jenis hubungan kerja",
        },
        {
          title: "Tags",
          href: "/hr/tags",
          icon: Tag,
          description: "Tag karyawan",
        },
      ],
    },
    {
      title: "Manajemen Karyawan",
      icon: Users,
      description: "Kelola karyawan dan operasional HR",
      submenu: [
        {
          title: "Karyawan",
          href: "/hr/employees",
          icon: User,
          description: "Data dan profil karyawan",
        },
        {
          title: "Rekrutmen",
          href: "/hr/recruitment",
          icon: UserPlus,
          description: "Proses rekrutmen dan seleksi",
        },
        {
          title: "Absensi",
          href: "/hr/attendance",
          icon: Clock,
          description: "Manajemen kehadiran karyawan",
        },
        {
          title: "Cuti & Izin",
          href: "/hr/leave",
          icon: Calendar,
          description: "Pengajuan dan persetujuan cuti",
        },
        {
          title: "Penggajian",
          href: "/hr/payroll",
          icon: DollarSign,
          description: "Sistem penggajian dan tunjangan",
        },
        {
          title: "Kinerja",
          href: "/hr/performance",
          icon: TrendingUp,
          description: "Evaluasi dan penilaian kinerja",
        },
        {
          title: "Dokumen",
          href: "/hr/documents",
          icon: FileText,
          description: "Manajemen dokumen karyawan",
        },
      ],
    },
    {
      title: "Laporan",
      href: "/hr/reports",
      icon: BarChart3,
      description: "Analitik dan laporan HR",
    },
    {
      title: "Pengaturan",
      href: "/hr/settings",
      icon: Settings,
      description: "Pengaturan modul HR",
    },
  ];

  const isActiveRoute = (href: string) => {
    return pathname === href || pathname.startsWith(href + "/");
  };

  const isActiveParent = (submenu: any[]) => {
    return submenu.some((item) => isActiveRoute(item.href));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali ke Dashboard
              </Link>
              <div className="text-gray-300">|</div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Manajemen SDM
                </h1>
                <p className="text-sm text-gray-600">
                  Modul Sumber Daya Manusia
                </p>
              </div>
            </div>

            {/* User Profile Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 h-auto p-2"
                >
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-sm">
                      {user?.name
                        ? user.name
                            .split(" ")
                            .map((n: string) => n[0])
                            .join("")
                        : "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left hidden sm:block">
                    <div className="text-sm font-medium text-gray-900">
                      {user?.name || "User"}
                    </div>
                    <div className="text-xs text-gray-500">
                      {user?.role || "Role"}
                    </div>
                  </div>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Akun Saya</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push("/profile")}>
                  <User className="w-4 h-4 mr-2" />
                  Profil
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push("/settings")}>
                  <Settings className="w-4 h-4 mr-2" />
                  Pengaturan
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="text-red-600"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Keluar
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Breadcrumb Navigation */}
          {pathname !== "/hr" && (
            <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
              <nav className="flex items-center space-x-2 text-sm">
                {generateBreadcrumb().map((crumb, index) => (
                  <React.Fragment key={crumb.href}>
                    {index > 0 && (
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    )}
                    {index === generateBreadcrumb().length - 1 ? (
                      <span className="text-gray-900 font-medium">
                        {crumb.title}
                      </span>
                    ) : (
                      <Link
                        href={crumb.href}
                        className="text-gray-600 hover:text-gray-900 transition-colors"
                      >
                        {crumb.title}
                      </Link>
                    )}
                  </React.Fragment>
                ))}
              </nav>
            </div>
          )}
        </div>
      </div>

      <div className="flex">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-white border-r border-gray-200 min-h-screen">
          <nav className="p-4 space-y-2">
            {navigationItems.map((item, index) => (
              <div key={index}>
                {item.submenu ? (
                  // Parent with submenu
                  <div className="space-y-1">
                    <button
                      onClick={() => toggleMenu(item.title)}
                      className={cn(
                        "w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors cursor-pointer",
                        isActiveParent(item.submenu)
                          ? "bg-blue-50 text-blue-700"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      <item.icon className="w-5 h-5 mr-3" />
                      <span className="flex-1 text-left">{item.title}</span>
                      <ChevronRight
                        className={cn(
                          "w-4 h-4 transition-transform duration-200",
                          collapsedMenus[item.title]
                            ? "transform rotate-90"
                            : ""
                        )}
                      />
                    </button>

                    {/* Submenu dengan animasi collapse */}
                    <div
                      className={cn(
                        "ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out",
                        collapsedMenus[item.title]
                          ? "max-h-0 opacity-0"
                          : "max-h-[500px] opacity-100"
                      )}
                    >
                      {item.submenu.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          href={subItem.href}
                          className={cn(
                            "flex items-center px-3 py-2 text-sm rounded-lg transition-colors",
                            isActiveRoute(subItem.href)
                              ? "bg-blue-100 text-blue-700 font-medium"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                          )}
                        >
                          <subItem.icon className="w-4 h-4 mr-3" />
                          <div className="flex-1">
                            <div className="font-medium">{subItem.title}</div>
                            <div className="text-xs text-gray-500">
                              {subItem.description}
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  // Direct link
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                      isActiveRoute(item.href)
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    <item.icon className="w-5 h-5 mr-3" />
                    <div className="flex-1">
                      <div>{item.title}</div>
                      <div className="text-xs text-gray-500">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1">{children}</div>
      </div>

      {/* Toast Notifications */}
      <Toaster position="top-right" richColors closeButton duration={4000} />
    </div>
  );
};

export default HRLayout;
