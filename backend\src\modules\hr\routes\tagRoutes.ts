import express from "express";
import { body } from "express-validator";
import {
  getTags,
  getTagById,
  createTag,
  updateTag,
  deleteTag,
} from "../controllers/tagController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const tagValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Tag name is required")
    .isLength({ min: 2, max: 50 })
    .withMessage("Tag name must be between 2 and 50 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Description must not exceed 500 characters"),
  body("color")
    .optional()
    .trim()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .withMessage("Color must be a valid hex color code"),
];

// Routes
router.get("/", devBypass, getTags);
router.get("/:id", devBypass, getTagById);
router.post("/", devBypass, tagValidation, createTag);
router.put("/:id", devBypass, tagValidation, updateTag);
router.delete("/:id", devBypass, deleteTag);

export default router;
