import { Request, Response } from "express";
import { Department } from "../models/Department";

// Get all departments
export const getDepartments = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { search, status } = req.query;

    // Build filter
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { manager: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const departments = await Department.find(filter)
      .populate("division", "name")
      .populate("employeeCount")
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      message: "Departments retrieved successfully",
      data: departments,
      count: departments.length,
    });
  } catch (error) {
    console.error("Error getting departments:", error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve departments",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get department by ID
export const getDepartmentById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const department = await Department.findById(id)
      .populate("division", "name")
      .populate("employeeCount");

    if (!department) {
      res.status(404).json({
        success: false,
        message: "Department not found",
      });
      return;
    }

    // Transform data to include divisionId for frontend
    const departmentData = {
      ...department.toObject(),
      divisionId: department.division ? department.division._id : "",
    };

    res.status(200).json({
      success: true,
      message: "Department retrieved successfully",
      data: departmentData,
    });
  } catch (error) {
    console.error("Error getting department:", error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve department",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Create new department
export const createDepartment = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { name, manager, description, divisionId, isActive } = req.body;

    // Check if department already exists
    const existingDepartment = await Department.findOne({ name });
    if (existingDepartment) {
      res.status(400).json({
        success: false,
        message: "Department with this name already exists",
      });
      return;
    }

    const department = new Department({
      name,
      division: divisionId || undefined,
      manager,
      description,
      isActive: isActive !== undefined ? isActive : true,
      // Skip createdBy/updatedBy for development
    });

    await department.save();

    // Populate division for response
    await department.populate("division", "name");

    res.status(201).json({
      success: true,
      message: "Department created successfully",
      data: department,
    });
  } catch (error) {
    console.error("Error creating department:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create department",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Update department
export const updateDepartment = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, manager, description, divisionId, isActive } = req.body;

    // Check if department exists
    const department = await Department.findById(id);
    if (!department) {
      res.status(404).json({
        success: false,
        message: "Department not found",
      });
      return;
    }

    // Check if name is being changed and if it already exists
    if (name && name !== department.name) {
      const existingDepartment = await Department.findOne({
        name,
        _id: { $ne: id },
      });
      if (existingDepartment) {
        res.status(400).json({
          success: false,
          message: "Department with this name already exists",
        });
        return;
      }
    }

    // Update department
    const updatedDepartment = await Department.findByIdAndUpdate(
      id,
      {
        name,
        division: divisionId || undefined,
        manager,
        description,
        isActive,
      },
      { new: true, runValidators: true }
    ).populate("division", "name");

    res.status(200).json({
      success: true,
      message: "Department updated successfully",
      data: updatedDepartment,
    });
  } catch (error) {
    console.error("Error updating department:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update department",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Delete department (soft delete)
export const deleteDepartment = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const department = await Department.findById(id);
    if (!department) {
      res.status(404).json({
        success: false,
        message: "Department not found",
      });
      return;
    }

    // Soft delete by setting isActive to false
    await Department.findByIdAndUpdate(id, {
      isActive: false,
    });

    res.status(200).json({
      success: true,
      message: "Department deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting department:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete department",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get active departments only
export const getActiveDepartments = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    const departments = await Department.find({ isActive: true })
      .select("name manager")
      .populate("employeeCount")
      .sort({ name: 1 });

    res.status(200).json({
      success: true,
      message: "Active departments retrieved successfully",
      data: departments,
    });
  } catch (error) {
    console.error("Error getting active departments:", error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve active departments",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
