import { Router } from "express";
import {
  getDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getActiveDepartments,
} from "../controllers/departmentController";
import { authenticate } from "../../user-access/middleware/authMiddleware";
import {
  createDepartmentSchema,
  updateDepartmentSchema,
} from "../validation/departmentValidation";

// Create validation middleware function
const validateRequest = (schema: any) => {
  return (req: any, res: any, next: any) => {
    const { error } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map((detail: any) => ({
        field: detail.path.join("."),
        message: detail.message,
        value: detail.context?.value,
      }));

      res.status(400).json({
        success: false,
        message: "Data tidak valid",
        error: "VALIDATION_ERROR",
        data: { errors },
        timestamp: new Date(),
      });
      return;
    }

    next();
  };
};

const router = Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Public routes (with authentication)
router.get("/", devBypass, getDepartments);
router.get("/active", devBypass, getActiveDepartments);
router.get("/:id", devBypass, getDepartmentById);

// Protected routes (require authentication)
router.post(
  "/",
  devBypass,
  validateRequest(createDepartmentSchema),
  createDepartment
);

router.put(
  "/:id",
  devBypass,
  validateRequest(updateDepartmentSchema),
  updateDepartment
);

router.delete("/:id", devBypass, deleteDepartment);

export default router;
