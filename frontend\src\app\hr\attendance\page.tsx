"use client";

import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Clock,
  QrCode,
  MapPin,
  Camera,
  BarChart3,
  Calendar,
} from "lucide-react";

const AttendancePage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Absensi</h1>
        <p className="text-gray-600 mt-1">
          Sistem manajemen kehadiran karyawan modern dengan teknologi QR Code
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <CardContent className="p-8 text-center">
          <Clock className="w-16 h-16 text-green-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Sistem Absensi Modern
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem absensi yang canggih dengan teknologi QR Code, GPS tracking,
            dan photo verification untuk memastikan akurasi kehadiran karyawan.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            🚀 Priority High - Phase 2A Development
          </div>
        </CardContent>
      </Card>

      {/* Key Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <QrCode className="w-5 h-5 mr-2 text-blue-600" />
              QR Code Attendance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Clock in/out menggunakan QR Code untuk kemudahan dan akurasi
              tinggi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <MapPin className="w-5 h-5 mr-2 text-red-600" />
              GPS Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Verifikasi lokasi kehadiran dengan teknologi GPS untuk memastikan
              karyawan berada di tempat kerja
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Camera className="w-5 h-5 mr-2 text-purple-600" />
              Photo Verification
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Verifikasi identitas dengan foto selfie saat clock in untuk
              keamanan tambahan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
              Real-time Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Dashboard real-time untuk monitoring kehadiran dan pola absensi
              karyawan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Calendar className="w-5 h-5 mr-2 text-orange-600" />
              Shift Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Kelola jadwal shift karyawan dengan fleksibilitas tinggi dan
              notifikasi otomatis
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Clock className="w-5 h-5 mr-2 text-indigo-600" />
              Overtime Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Tracking otomatis jam lembur dengan perhitungan kompensasi yang
              akurat
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Technical Specifications */}
      <Card>
        <CardHeader>
          <CardTitle>Spesifikasi Teknis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Mobile Features</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Progressive Web App (PWA)</li>
                <li>• Offline capability</li>
                <li>• Push notifications</li>
                <li>• Camera integration</li>
                <li>• GPS location services</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">Security Features</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Encrypted QR codes</li>
                <li>• Biometric verification</li>
                <li>• Location validation</li>
                <li>• Anti-spoofing measures</li>
                <li>• Audit trail logging</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Implementation Plan */}
      <Card>
        <CardHeader>
          <CardTitle>Rencana Implementasi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <div className="font-medium">
                  Week 1-2: Core Attendance System
                </div>
                <div className="text-sm text-gray-600">
                  Basic clock in/out, database design, API endpoints
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Week 3-4: QR Code Integration</div>
                <div className="text-sm text-gray-600">
                  QR code generation, scanning, validation
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Week 5-6: Advanced Features</div>
                <div className="text-sm text-gray-600">
                  GPS tracking, photo verification, analytics
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendancePage;
