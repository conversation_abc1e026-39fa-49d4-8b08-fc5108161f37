import express from "express";
import { body } from "express-validator";
import {
  getRankGrades,
  getRankGradeById,
  createRankGrade,
  updateRankGrade,
  deleteRankGrade,
} from "../controllers/rankGradeController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const rankGradeValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Rank grade name is required")
    .isLength({ min: 1, max: 100 })
    .withMessage("Rank grade name must be between 1 and 100 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Description must not exceed 500 characters"),
];

// Routes
router.get("/", devBypass, getRankGrades);
router.get("/:id", devBypass, getRankGradeById);
router.post("/", devBypass, rankGradeValidation, createRankGrade);
router.put("/:id", devBypass, rankGradeValidation, updateRankGrade);
router.delete("/:id", devBypass, deleteRankGrade);

export default router;
