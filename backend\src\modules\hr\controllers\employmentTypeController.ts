import { Request, Response } from "express";
import EmploymentType from "../models/EmploymentType";

// Get all employment types
export const getEmploymentTypes = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    // Show all records (active and inactive) - consistent with other modules
    const employmentTypes = await EmploymentType.find({}).sort({
      createdAt: -1,
    });

    res.json({
      success: true,
      data: employmentTypes,
      message: "Employment types retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching employment types:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Get employment type by ID
export const getEmploymentTypeById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const employmentType = await EmploymentType.findById(id);

    if (!employmentType) {
      res.status(404).json({
        success: false,
        message: "Employment type not found",
      });
      return;
    }

    res.json({
      success: true,
      data: employmentType,
      message: "Employment type retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching employment type:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Create new employment type
export const createEmploymentType = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { name, description, isActive = true } = req.body;

    // Check if employment type name already exists (only check active ones)
    const existingEmploymentType = await EmploymentType.findOne({
      name,
      isActive: true,
    });

    if (existingEmploymentType) {
      res.status(400).json({
        success: false,
        message: "Employment type already exists",
      });
      return;
    }

    const employmentType = new EmploymentType({
      name,
      description,
      isActive, // Set active status
      // Skip createdBy/updatedBy for development
    });

    await employmentType.save();

    res.status(201).json({
      success: true,
      data: employmentType,
      message: "Employment type created successfully",
    });
  } catch (error) {
    console.error("Error creating employment type:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Update employment type
export const updateEmploymentType = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description, isActive } = req.body;

    // Check if employment type exists
    const employmentType = await EmploymentType.findById(id);
    if (!employmentType) {
      res.status(404).json({
        success: false,
        message: "Employment type not found",
      });
      return;
    }

    // Update employment type
    employmentType.name = name || employmentType.name;
    employmentType.description = description || employmentType.description;
    if (isActive !== undefined) employmentType.isActive = isActive; // Update active status
    // Skip updatedBy for development

    await employmentType.save();

    res.json({
      success: true,
      data: employmentType,
      message: "Employment type updated successfully",
    });
  } catch (error) {
    console.error("Error updating employment type:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

// Delete employment type (status-based delete)
export const deleteEmploymentType = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const employmentType = await EmploymentType.findById(id);
    if (!employmentType) {
      res.status(404).json({
        success: false,
        message: "Employment type not found",
      });
      return;
    }

    // Status-based delete
    employmentType.isActive = false;
    // Skip updatedBy for development

    await employmentType.save();

    res.json({
      success: true,
      message: "Employment type deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting employment type:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};
