import { Router } from 'express';
import { login, refreshToken, logout, getProfile } from '../controllers/authController';
import { authenticate } from '../middleware/authMiddleware';
import { validateLogin, validateRefreshToken } from '../middleware/validation';

const router = Router();

/**
 * @route   POST /api/auth/login
 * @desc    User login
 * @access  Public
 */
router.post('/login', validateLogin, login);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', validateRefreshToken, refreshToken);

/**
 * @route   POST /api/auth/logout
 * @desc    User logout
 * @access  Private
 */
router.post('/logout', authenticate, logout);

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', authenticate, getProfile);

/**
 * @route   GET /api/auth/verify
 * @desc    Verify token validity
 * @access  Private
 */
router.get('/verify', authenticate, (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Token valid',
    data: {
      valid: true,
      user: {
        id: req.user!._id,
        email: req.user!.email,
        role: req.userRole!.name
      }
    },
    timestamp: new Date()
  });
});

export default router;
