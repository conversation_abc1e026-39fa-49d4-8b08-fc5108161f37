"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Import components
import EmployeeProfileHeader from "@/components/hr/EmployeeProfileHeader";
import EmployeePersonalInfo from "@/components/hr/EmployeePersonalInfo";
import EmployeeHRInfo from "@/components/hr/EmployeeHRInfo";
import EmployeeFamilyInfo from "@/components/hr/EmployeeFamilyInfo";

// Import services
import { employeeService, Employee } from "@/services/employeeService";
import { masterDataService } from "@/services/masterDataService";

interface EmployeeProfilePageProps {}

export default function EmployeeProfilePage({}: EmployeeProfilePageProps) {
  const params = useParams();
  const router = useRouter();
  const employeeId = params.id as string;

  // State management
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditingHeader, setIsEditingHeader] = useState(false);
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [isEditingHR, setIsEditingHR] = useState(false);
  const [isEditingFamily, setIsEditingFamily] = useState(false);

  // Master data state
  const [masterData, setMasterData] = useState({
    departments: [],
    positions: [],
    tags: [],
    employmentTypes: [],
    rankCategories: [],
    rankGrades: [],
    rankSubgrades: [],
  });

  // Fetch employee data
  const fetchEmployee = async () => {
    try {
      setLoading(true);
      const response = await employeeService.getEmployeeById(employeeId);
      setEmployee(response.data);
    } catch (error) {
      console.error("Error fetching employee:", error);
      toast.error("Gagal memuat data karyawan", {
        description: "Silakan coba lagi atau hubungi administrator",
      });
      router.push("/hr/employees");
    } finally {
      setLoading(false);
    }
  };

  // Fetch master data from API
  const fetchMasterData = async () => {
    try {
      console.log("Fetching master data from API...");
      const masterData = await masterDataService.getAllMasterData();
      console.log("Master data fetched successfully:", masterData);
      setMasterData(masterData);

      // Show success message if we got some data
      if (
        masterData.departments.length > 0 ||
        masterData.positions.length > 0
      ) {
        toast.success("Master data berhasil dimuat dari API");
      } else {
        console.log("No data from API, using fallback...");
        throw new Error("No data received from API");
      }
    } catch (error) {
      console.warn("API not available, using fallback data:", error);

      // Always use fallback data when API fails
      const fallbackData = {
        departments: [
          { _id: "1", name: "Human Resources", isActive: true },
          { _id: "2", name: "Information Technology", isActive: true },
          { _id: "3", name: "Finance & Accounting", isActive: true },
          { _id: "4", name: "Operations", isActive: true },
          { _id: "5", name: "Marketing & Sales", isActive: true },
        ],
        positions: [
          {
            _id: "1",
            name: "Manager",
            department: { _id: "2", name: "Information Technology" },
            isActive: true,
          },
          {
            _id: "2",
            name: "Senior Software Engineer",
            department: { _id: "2", name: "Information Technology" },
            isActive: true,
          },
          {
            _id: "3",
            name: "Software Engineer",
            department: { _id: "2", name: "Information Technology" },
            isActive: true,
          },
          {
            _id: "4",
            name: "Junior Software Engineer",
            department: { _id: "2", name: "Information Technology" },
            isActive: true,
          },
          {
            _id: "5",
            name: "HR Manager",
            department: { _id: "1", name: "Human Resources" },
            isActive: true,
          },
        ],
        tags: [
          { _id: "1", name: "Remote Worker", color: "#3B82F6", isActive: true },
          { _id: "2", name: "Team Lead", color: "#10B981", isActive: true },
          { _id: "3", name: "New Hire", color: "#F59E0B", isActive: true },
          { _id: "4", name: "Senior", color: "#8B5CF6", isActive: true },
        ],
        employmentTypes: [
          { _id: "1", name: "Karyawan Tetap", isActive: true },
          { _id: "2", name: "Karyawan Kontrak", isActive: true },
          { _id: "3", name: "Karyawan Paruh Waktu", isActive: true },
          { _id: "4", name: "Magang", isActive: true },
        ],
        rankCategories: [
          { _id: "1", name: "Staff", isActive: true },
          { _id: "2", name: "Supervisor", isActive: true },
          { _id: "3", name: "Manager", isActive: true },
          { _id: "4", name: "Senior Manager", isActive: true },
        ],
        rankGrades: [
          { _id: "1", name: "Grade I", isActive: true },
          { _id: "2", name: "Grade II", isActive: true },
          { _id: "3", name: "Grade III", isActive: true },
          { _id: "4", name: "Grade IV", isActive: true },
        ],
        rankSubgrades: [
          { _id: "1", name: "Sub Grade A", isActive: true },
          { _id: "2", name: "Sub Grade B", isActive: true },
          { _id: "3", name: "Sub Grade C", isActive: true },
        ],
      };

      setMasterData(fallbackData);
      toast.warning("Menggunakan data fallback", {
        description: "API tidak tersedia, menggunakan data contoh",
      });
    }
  };

  // Load data on component mount
  useEffect(() => {
    if (employeeId) {
      fetchEmployee();
      fetchMasterData();
    }
  }, [employeeId]);

  // Handle header save
  const handleHeaderSave = async (data: any) => {
    try {
      const updateData = {
        personal: {
          fullName: data.fullName,
          employeeId: data.employeeId,
          phone: data.phone,
          profilePhoto: data.profilePhoto,
        },
        hr: {
          division: data.division,
          department: data.department,
          position: data.position,
          tags: data.selectedTags,
          companyEmail: data.email, // Email dari header disimpan sebagai companyEmail
        },
      };

      await employeeService.updateEmployee(employeeId, updateData);
      setIsEditingHeader(false);
      fetchEmployee(); // Refresh data
    } catch (error) {
      console.error("Error updating employee header:", error);
    }
  };

  // Handle personal info save
  const handlePersonalSave = async (data: any) => {
    try {
      const updateData = {
        personal: data,
      };

      await employeeService.updateEmployee(employeeId, updateData);
      setIsEditingPersonal(false);
      fetchEmployee(); // Refresh data
    } catch (error) {
      console.error("Error updating personal info:", error);
    }
  };

  // Handle HR info save
  const handleHRSave = async (data: any) => {
    try {
      const updateData = {
        hr: data,
      };

      await employeeService.updateEmployee(employeeId, updateData);
      setIsEditingHR(false);
      fetchEmployee(); // Refresh data
    } catch (error) {
      console.error("Error updating HR info:", error);
    }
  };

  // Handle family info save
  const handleFamilySave = async (data: any) => {
    try {
      const updateData = {
        family: data,
      };

      await employeeService.updateEmployee(employeeId, updateData);
      setIsEditingFamily(false);
      fetchEmployee(); // Refresh data
    } catch (error) {
      console.error("Error updating family info:", error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Memuat profil karyawan...</span>
        </div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">
            Karyawan tidak ditemukan
          </h2>
          <p className="text-gray-600 mb-4">
            Data karyawan yang Anda cari tidak tersedia
          </p>
          <Button onClick={() => router.push("/hr/employees")}>
            Kembali ke Daftar Karyawan
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push("/hr/employees")}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Profil Karyawan</h1>
          <p className="text-gray-600">Kelola informasi lengkap karyawan</p>
        </div>
      </div>

      {/* Employee Profile Header */}
      <EmployeeProfileHeader
        employee={employee}
        divisions={masterData.divisions}
        departments={masterData.departments}
        positions={masterData.positions}
        tags={masterData.tags}
        onSave={handleHeaderSave}
        isEditing={isEditingHeader}
        onEditToggle={() => setIsEditingHeader(!isEditingHeader)}
      />

      {/* Tabs for different sections */}
      <Tabs defaultValue="personal" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="personal">Informasi Personal</TabsTrigger>
          <TabsTrigger value="hr">Informasi HR</TabsTrigger>
          <TabsTrigger value="family">Informasi Keluarga</TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          <EmployeePersonalInfo
            data={employee.personal}
            onSave={handlePersonalSave}
            isEditing={isEditingPersonal}
            onEditToggle={() => setIsEditingPersonal(!isEditingPersonal)}
          />
        </TabsContent>

        <TabsContent value="hr">
          <EmployeeHRInfo
            data={employee.hr}
            onSave={handleHRSave}
            isEditing={isEditingHR}
            onEditToggle={() => setIsEditingHR(!isEditingHR)}
            departments={masterData.departments}
            positions={masterData.positions}
            tags={masterData.tags}
            employmentTypes={masterData.employmentTypes}
            rankCategories={masterData.rankCategories}
            rankGrades={masterData.rankGrades}
            rankSubgrades={masterData.rankSubgrades}
          />
        </TabsContent>

        <TabsContent value="family">
          <EmployeeFamilyInfo
            data={employee.family}
            onSave={handleFamilySave}
            isEditing={isEditingFamily}
            onEditToggle={() => setIsEditingFamily(!isEditingFamily)}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
