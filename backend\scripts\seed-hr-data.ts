import mongoose from "mongoose";
import { connectDatabase } from "../src/config/database";
import { Department } from "../src/modules/hr/models/Department";
import Position from "../src/modules/hr/models/Position";
import RankCategory from "../src/modules/hr/models/RankCategory";
import RankGrade from "../src/modules/hr/models/RankGrade";
import RankSubgrade from "../src/modules/hr/models/RankSubgrade";
import EmploymentType from "../src/modules/hr/models/EmploymentType";
import Tag from "../src/modules/hr/models/Tag";
import { Employee } from "../src/modules/hr/models/Employee";

const seedDepartments = async () => {
  console.log("🌱 Seeding departments...");

  const departments = [
    {
      name: "Human Resources",
      manager: "Siti Nurhaliza",
      description:
        "Mengelola sumber daya manusia, rekrutmen, dan pengembangan karyawan",
    },
    {
      name: "Information Technology",
      manager: "<PERSON>",
      description:
        "Mengelola infrastruktur IT, pengembangan sistem, dan dukungan teknis",
    },
    {
      name: "Finance & Accounting",
      manager: "Dewi Sartika",
      description:
        "Mengelola keuangan perusahaan, akuntansi, dan pelaporan keuangan",
    },
    {
      name: "Operations",
      manager: "Budi Santoso",
      description: "Mengelola operasional harian, produksi, dan logistik",
    },
    {
      name: "Marketing & Sales",
      manager: "Rina Kusuma",
      description: "Mengelola pemasaran, penjualan, dan hubungan pelanggan",
    },
  ];

  for (const deptData of departments) {
    const existingDept = await Department.findOne({ name: deptData.name });

    if (!existingDept) {
      const department = new Department(deptData);
      await department.save();
      console.log(`✅ Created department: ${deptData.name}`);
    } else {
      console.log(`⚠️ Department already exists: ${deptData.name}`);
    }
  }
};

const seedPositions = async () => {
  console.log("🌱 Seeding positions...");

  // Get departments first
  const hrDept = await Department.findOne({ name: "Human Resources" });
  const itDept = await Department.findOne({ name: "Information Technology" });
  const financeDept = await Department.findOne({
    name: "Finance & Accounting",
  });
  const opsDept = await Department.findOne({ name: "Operations" });
  const marketingDept = await Department.findOne({ name: "Marketing & Sales" });

  if (!hrDept || !itDept || !financeDept || !opsDept || !marketingDept) {
    console.log("❌ Departments not found. Please seed departments first.");
    return;
  }

  const positions = [
    // HR Positions
    {
      name: "HR Manager",
      department: hrDept._id,
      description: "Mengelola operasional HR dan kebijakan karyawan",
    },
    {
      name: "HR Specialist",
      department: hrDept._id,
      description: "Menangani rekrutmen, training, dan administrasi HR",
    },
    {
      name: "Payroll Officer",
      department: hrDept._id,
      description: "Mengelola penggajian dan tunjangan karyawan",
    },

    // IT Positions
    {
      name: "IT Manager",
      department: itDept._id,
      description: "Mengelola infrastruktur IT dan tim teknologi",
    },
    {
      name: "Software Developer",
      department: itDept._id,
      description: "Mengembangkan aplikasi dan sistem perusahaan",
    },
    {
      name: "System Administrator",
      department: itDept._id,
      description: "Mengelola server, jaringan, dan keamanan sistem",
    },
    {
      name: "Database Administrator",
      department: itDept._id,
      description: "Mengelola database dan backup data",
    },

    // Finance Positions
    {
      name: "Finance Manager",
      department: financeDept._id,
      description: "Mengelola keuangan dan pelaporan finansial",
    },
    {
      name: "Accountant",
      department: financeDept._id,
      description: "Menangani pembukuan dan laporan keuangan",
    },
    {
      name: "Finance Analyst",
      department: financeDept._id,
      description: "Menganalisis laporan keuangan dan budget",
    },

    // Operations Positions
    {
      name: "Operations Manager",
      department: opsDept._id,
      description: "Mengelola operasional harian dan produksi",
    },
    {
      name: "Production Supervisor",
      department: opsDept._id,
      description: "Mengawasi proses produksi dan quality control",
    },
    {
      name: "Logistics Coordinator",
      department: opsDept._id,
      description: "Mengelola distribusi dan supply chain",
    },

    // Marketing Positions
    {
      name: "Marketing Manager",
      department: marketingDept._id,
      description: "Mengelola strategi pemasaran dan promosi",
    },
    {
      name: "Sales Executive",
      department: marketingDept._id,
      description: "Menangani penjualan dan hubungan pelanggan",
    },
    {
      name: "Digital Marketing Specialist",
      department: marketingDept._id,
      description: "Mengelola pemasaran digital dan media sosial",
    },
  ];

  for (const posData of positions) {
    const existingPos = await Position.findOne({
      name: posData.name,
      department: posData.department,
    });

    if (!existingPos) {
      const position = new Position(posData);
      await position.save();
      console.log(`✅ Created position: ${posData.name}`);
    } else {
      console.log(`⚠️ Position already exists: ${posData.name}`);
    }
  }
};

const seedRankCategories = async () => {
  console.log("🌱 Seeding rank categories...");

  const rankCategories = [
    {
      name: "Manajemen",
      description: "Kategori pangkat untuk posisi manajemen dan kepemimpinan",
    },
    {
      name: "Staff",
      description: "Kategori pangkat untuk staff operasional dan administratif",
    },
    {
      name: "Supervisor",
      description: "Kategori pangkat untuk supervisor dan koordinator",
    },
    {
      name: "Specialist",
      description: "Kategori pangkat untuk spesialis dan ahli teknis",
    },
  ];

  for (const categoryData of rankCategories) {
    const existing = await RankCategory.findOne({ name: categoryData.name });

    if (!existing) {
      const category = new RankCategory(categoryData);
      await category.save();
      console.log(`✅ Created rank category: ${categoryData.name}`);
    } else {
      console.log(`⚠️ Rank category already exists: ${categoryData.name}`);
    }
  }
};

const seedRankGrades = async () => {
  console.log("🌱 Seeding rank grades...");

  const rankGrades = [
    {
      name: "Grade I",
      description: "Golongan tingkat pertama untuk entry level",
    },
    {
      name: "Grade II",
      description: "Golongan tingkat kedua untuk junior level",
    },
    {
      name: "Grade III",
      description: "Golongan tingkat ketiga untuk senior level",
    },
    {
      name: "Grade IV",
      description: "Golongan tingkat keempat untuk expert level",
    },
    {
      name: "Grade V",
      description: "Golongan tingkat kelima untuk management level",
    },
  ];

  for (const gradeData of rankGrades) {
    const existing = await RankGrade.findOne({ name: gradeData.name });

    if (!existing) {
      const grade = new RankGrade(gradeData);
      await grade.save();
      console.log(`✅ Created rank grade: ${gradeData.name}`);
    } else {
      console.log(`⚠️ Rank grade already exists: ${gradeData.name}`);
    }
  }
};

const seedRankSubgrades = async () => {
  console.log("🌱 Seeding rank subgrades...");

  const rankSubgrades = [
    {
      name: "Sub Grade A",
      description: "Sub golongan tingkat A",
    },
    {
      name: "Sub Grade B",
      description: "Sub golongan tingkat B",
    },
    {
      name: "Sub Grade C",
      description: "Sub golongan tingkat C",
    },
    {
      name: "Sub Grade D",
      description: "Sub golongan tingkat D",
    },
  ];

  for (const subgradeData of rankSubgrades) {
    const existing = await RankSubgrade.findOne({ name: subgradeData.name });

    if (!existing) {
      const subgrade = new RankSubgrade(subgradeData);
      await subgrade.save();
      console.log(`✅ Created rank subgrade: ${subgradeData.name}`);
    } else {
      console.log(`⚠️ Rank subgrade already exists: ${subgradeData.name}`);
    }
  }
};

const seedEmploymentTypes = async () => {
  console.log("🌱 Seeding employment types...");

  const employmentTypes = [
    {
      name: "Karyawan Tetap",
      description: "Karyawan dengan status tetap dan kontrak tidak terbatas",
    },
    {
      name: "Karyawan Kontrak",
      description: "Karyawan dengan kontrak waktu tertentu (PKWT)",
    },
    {
      name: "Freelancer",
      description: "Pekerja lepas dengan project basis",
    },
    {
      name: "Magang",
      description: "Mahasiswa atau fresh graduate yang sedang magang",
    },
    {
      name: "Part Time",
      description: "Karyawan dengan waktu kerja paruh waktu",
    },
  ];

  for (const typeData of employmentTypes) {
    const existing = await EmploymentType.findOne({ name: typeData.name });

    if (!existing) {
      const type = new EmploymentType(typeData);
      await type.save();
      console.log(`✅ Created employment type: ${typeData.name}`);
    } else {
      console.log(`⚠️ Employment type already exists: ${typeData.name}`);
    }
  }
};

const seedTags = async () => {
  console.log("🌱 Seeding tags...");

  const tags = [
    {
      name: "Urgent",
      description:
        "Tag untuk hal-hal yang urgent dan memerlukan perhatian segera",
      color: "#EF4444",
    },
    {
      name: "Important",
      description: "Tag untuk hal-hal yang penting",
      color: "#F59E0B",
    },
    {
      name: "Normal",
      description: "Tag untuk hal-hal dengan prioritas normal",
      color: "#3B82F6",
    },
    {
      name: "Low Priority",
      description: "Tag untuk hal-hal dengan prioritas rendah",
      color: "#6B7280",
    },
    {
      name: "Training",
      description: "Tag untuk kegiatan training dan pengembangan",
      color: "#10B981",
    },
    {
      name: "Meeting",
      description: "Tag untuk meeting dan rapat",
      color: "#8B5CF6",
    },
  ];

  for (const tagData of tags) {
    const existing = await Tag.findOne({ name: tagData.name });

    if (!existing) {
      const tag = new Tag(tagData);
      await tag.save();
      console.log(`✅ Created tag: ${tagData.name}`);
    } else {
      console.log(`⚠️ Tag already exists: ${tagData.name}`);
    }
  }
};

const seedEmployees = async () => {
  console.log("🌱 Seeding sample employees...");

  // Get master data references
  const hrDept = await Department.findOne({ name: "Human Resources" });
  const itDept = await Department.findOne({ name: "Information Technology" });
  const financeDept = await Department.findOne({
    name: "Finance & Accounting",
  });

  const hrManagerPos = await Position.findOne({ name: "HR Manager" });
  const itManagerPos = await Position.findOne({ name: "IT Manager" });
  const accountantPos = await Position.findOne({ name: "Accountant" });

  const permanentType = await EmploymentType.findOne({
    name: "Karyawan Tetap",
  });

  const managementCategory = await RankCategory.findOne({ name: "Manajemen" });
  const staffCategory = await RankCategory.findOne({ name: "Staff" });

  const gradeIII = await RankGrade.findOne({ name: "Grade III" });
  const gradeIV = await RankGrade.findOne({ name: "Grade IV" });

  const subGradeA = await RankSubgrade.findOne({ name: "Sub Grade A" });
  const subGradeB = await RankSubgrade.findOne({ name: "Sub Grade B" });

  const employees = [
    {
      personal: {
        employeeId: "PSG001",
        fullName: "Siti Nurhaliza",
        gender: "Perempuan",
        placeOfBirth: "Jakarta",
        dateOfBirth: new Date("1985-03-15"),
        email: "<EMAIL>",
        phone: "08**********",
        religion: "Islam",
        bloodType: "A",
        familyCardNumber: "317**********123",
        idCardNumber: "317**********123",
        taxNumber: "**********12345",
        taxStatus: "K/1",

        // Contact Information
        contact: {
          mobilePhone1: "08**********",
          mobilePhone2: "08********91",
          homePhone: "021-1234567",
          email: "<EMAIL>",
        },

        // ID Card Address
        idCardAddress: {
          street: "Jl. Sudirman No. 123",
          city: "Jakarta",
          province: "DKI Jakarta",
        },

        // Current Address
        currentAddress: {
          street: "Jl. Sudirman No. 123",
          city: "Jakarta",
          province: "DKI Jakarta",
        },

        // Marital Information
        maritalInfo: {
          status: "Menikah",
        },

        // Bank Account
        bankAccount: {
          bankName: "Bank Mandiri",
          accountNumber: "**********",
          accountHolder: "Siti Nurhaliza",
        },
      },
      hr: {
        department: hrDept?._id,
        position: hrManagerPos?._id,
        rankCategory: managementCategory?._id,
        rankGrade: gradeIV?._id,
        rankSubgrade: subGradeA?._id,

        // Contract Information
        contract: {
          employmentType: permanentType?._id,
          hireDate: new Date("2020-01-15"),
        },

        // Education
        education: {
          certificateLevel: "S1",
          fieldOfStudy: "Manajemen SDM",
          schoolName: "Universitas Indonesia",
          schoolCity: "Jakarta",
        },

        // Emergency Contact
        emergency: {
          contactName: "Ahmad Nurhaliza",
          contactPhone: "08********99",
          contactPhone2: "021-********",
          relationship: "Suami",
          address: "Jl. Sudirman No. 123, Jakarta",
        },

        // Location
        location: {
          pointOfOrigin: "Jakarta",
          pointOfHire: "Jakarta",
        },

        // Uniform
        uniform: {
          workUniformSize: "L",
          workShoesSize: "39",
        },

        // Salary
        salary: {
          basic: 15000000,
          allowances: {
            transport: 1000000,
            meal: 500000,
            communication: 300000,
            position: 2000000,
          },
          total: 18800000,
        },
      },
      status: "Aktif",
    },
    {
      personal: {
        employeeId: "PSG002",
        fullName: "Ahmad Fauzi",
        gender: "Laki-laki",
        placeOfBirth: "Bandung",
        dateOfBirth: new Date("1988-07-22"),
        email: "<EMAIL>",
        phone: "08********91",
        religion: "Islam",
        bloodType: "B",
        familyCardNumber: "327**********123",
        idCardNumber: "327**********123",
        taxNumber: "**********12346",
        taxStatus: "K/2",

        // Contact Information
        contact: {
          mobilePhone1: "08********91",
          mobilePhone2: "08********92",
          homePhone: "022-1234567",
          email: "<EMAIL>",
        },

        // ID Card Address
        idCardAddress: {
          street: "Jl. Asia Afrika No. 456",
          city: "Bandung",
          province: "Jawa Barat",
        },

        // Current Address
        currentAddress: {
          street: "Jl. Asia Afrika No. 456",
          city: "Bandung",
          province: "Jawa Barat",
        },

        // Marital Information
        maritalInfo: {
          status: "Menikah",
        },

        // Bank Account
        bankAccount: {
          bankName: "Bank BCA",
          accountNumber: "**********",
          accountHolder: "Ahmad Fauzi",
        },
      },
      hr: {
        department: itDept?._id,
        position: itManagerPos?._id,
        rankCategory: managementCategory?._id,
        rankGrade: gradeIV?._id,
        rankSubgrade: subGradeA?._id,

        // Contract Information
        contract: {
          employmentType: permanentType?._id,
          hireDate: new Date("2019-06-01"),
        },

        // Education
        education: {
          certificateLevel: "S1",
          fieldOfStudy: "Teknik Informatika",
          schoolName: "Institut Teknologi Bandung",
          schoolCity: "Bandung",
        },

        // Emergency Contact
        emergency: {
          contactName: "Siti Fauzi",
          contactPhone: "08********98",
          contactPhone2: "022-********",
          relationship: "Istri",
          address: "Jl. Asia Afrika No. 456, Bandung",
        },

        // Location
        location: {
          pointOfOrigin: "Bandung",
          pointOfHire: "Bandung",
        },

        // Uniform
        uniform: {
          workUniformSize: "XL",
          workShoesSize: "42",
        },

        // Salary
        salary: {
          basic: 16000000,
          allowances: {
            transport: 1000000,
            meal: 500000,
            communication: 400000,
            position: 2500000,
          },
          total: 20400000,
        },
      },
      status: "Aktif",
    },
    {
      personal: {
        employeeId: "PSG003",
        fullName: "Dewi Sartika",
        gender: "Perempuan",
        placeOfBirth: "Surabaya",
        dateOfBirth: new Date("1990-11-08"),
        email: "<EMAIL>",
        phone: "08********92",
        religion: "Islam",
        bloodType: "O",
        familyCardNumber: "357**********123",
        idCardNumber: "357**********123",
        taxNumber: "**********12347",
        taxStatus: "TK/0",

        // Contact Information
        contact: {
          mobilePhone1: "08********92",
          mobilePhone2: "08********93",
          homePhone: "031-1234567",
          email: "<EMAIL>",
        },

        // ID Card Address
        idCardAddress: {
          street: "Jl. Pemuda No. 789",
          city: "Surabaya",
          province: "Jawa Timur",
        },

        // Current Address
        currentAddress: {
          street: "Jl. Pemuda No. 789",
          city: "Surabaya",
          province: "Jawa Timur",
        },

        // Marital Information
        maritalInfo: {
          status: "Belum Menikah",
        },

        // Bank Account
        bankAccount: {
          bankName: "Bank BRI",
          accountNumber: "**********",
          accountHolder: "Dewi Sartika",
        },
      },
      hr: {
        department: financeDept?._id,
        position: accountantPos?._id,
        rankCategory: staffCategory?._id,
        rankGrade: gradeIII?._id,
        rankSubgrade: subGradeB?._id,

        // Contract Information
        contract: {
          employmentType: permanentType?._id,
          hireDate: new Date("2021-03-10"),
        },

        // Education
        education: {
          certificateLevel: "S1",
          fieldOfStudy: "Akuntansi",
          schoolName: "Universitas Airlangga",
          schoolCity: "Surabaya",
        },

        // Emergency Contact
        emergency: {
          contactName: "Budi Sartika",
          contactPhone: "08********97",
          contactPhone2: "031-********",
          relationship: "Ayah",
          address: "Jl. Pemuda No. 789, Surabaya",
        },

        // Location
        location: {
          pointOfOrigin: "Surabaya",
          pointOfHire: "Surabaya",
        },

        // Uniform
        uniform: {
          workUniformSize: "M",
          workShoesSize: "37",
        },

        // Salary
        salary: {
          basic: 12000000,
          allowances: {
            transport: 800000,
            meal: 400000,
            communication: 200000,
            position: 1000000,
          },
          total: 14400000,
        },
      },
      status: "Aktif",
    },
  ];

  for (const empData of employees) {
    const existing = await Employee.findOne({
      "personal.employeeId": empData.personal.employeeId,
    });

    if (!existing) {
      const employee = new Employee(empData);
      await employee.save();
      console.log(`✅ Created employee: ${empData.personal.fullName}`);
    } else {
      console.log(`⚠️ Employee already exists: ${empData.personal.fullName}`);
    }
  }
};

const seedHRData = async () => {
  try {
    console.log("🚀 Starting HR data seeding...");
    console.log("📍 Environment:", process.env.NODE_ENV || "development");

    // Connect to database
    await connectDatabase();

    // Seed HR master data
    await seedDepartments();
    await seedPositions();
    await seedRankCategories();
    await seedRankGrades();
    await seedRankSubgrades();
    await seedEmploymentTypes();
    await seedTags();

    // Seed sample employees
    await seedEmployees();

    console.log("✅ HR data seeding completed successfully!");
    console.log("\n📋 HR Data Created:");
    console.log(
      "┌─────────────────────┬───────────────────────────────────────┐"
    );
    console.log(
      "│ Module              │ Records Created                       │"
    );
    console.log(
      "├─────────────────────┼───────────────────────────────────────┤"
    );
    console.log(
      "│ Departments         │ 5 departments                         │"
    );
    console.log(
      "│ Positions           │ 16 positions across all departments   │"
    );
    console.log(
      "│ Rank Categories     │ 4 categories                          │"
    );
    console.log(
      "│ Rank Grades         │ 5 grades                              │"
    );
    console.log(
      "│ Rank Subgrades      │ 4 subgrades                           │"
    );
    console.log(
      "│ Employment Types    │ 5 types                               │"
    );
    console.log(
      "│ Tags                │ 6 tags with colors                    │"
    );
    console.log(
      "│ Sample Employees    │ 3 employees with complete data        │"
    );
    console.log(
      "└─────────────────────┴───────────────────────────────────────┘"
    );
  } catch (error) {
    console.error("❌ Error seeding HR data:", error);
  } finally {
    await mongoose.connection.close();
    console.log("🔒 Database connection closed");
    process.exit(0);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedHRData();
}

export default seedHRData;
