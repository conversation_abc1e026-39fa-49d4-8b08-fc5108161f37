"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker";

interface FamilyInfoData {
  // Enhanced family structure
  spouse: {
    name: string;
    dateOfBirth: string;
    marriageDate: string;
    lastEducation: string;
    occupation: string;
    numberOfChildren: number;
  };
  
  // Children Identity
  children: any[];
  
  // Parents Information
  parents: {
    father: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      occupation: string;
      description: string;
    };
    mother: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      occupation: string;
      description: string;
    };
  };
  
  // Siblings Information
  siblings: {
    childOrder: number;
    totalSiblings: number;
    siblingsData: any[];
  };
  
  // In-laws Information
  inLaws: {
    fatherInLaw: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      description: string;
    };
    motherInLaw: {
      name: string;
      dateOfBirth: string;
      lastEducation: string;
      description: string;
    };
  };
  
  // Legacy emergency contact for backward compatibility
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    address: string;
  };
}

interface EditEmployeeFamilyInfoProps {
  data: FamilyInfoData;
  onUpdate: (data: FamilyInfoData) => void;
}

const educationLevels = [
  "SD",
  "SMP", 
  "SMA",
  "SMK",
  "D1",
  "D2",
  "D3",
  "S1",
  "S2",
  "S3",
];

export default function EditEmployeeFamilyInfo({
  data,
  onUpdate,
}: EditEmployeeFamilyInfoProps) {
  const handleInputChange = (field: string, value: any) => {
    const keys = field.split(".");
    let newData = { ...data };

    if (keys.length === 1) {
      newData[field as keyof FamilyInfoData] = value;
    } else if (keys.length === 2) {
      const [level1, level2] = keys;
      newData[level1 as keyof FamilyInfoData] = {
        ...newData[level1 as keyof FamilyInfoData],
        [level2]: value,
      } as any;
    } else if (keys.length === 3) {
      const [level1, level2, level3] = keys;
      newData[level1 as keyof FamilyInfoData] = {
        ...newData[level1 as keyof FamilyInfoData],
        [level2]: {
          ...(newData[level1 as keyof FamilyInfoData] as any)?.[level2],
          [level3]: value,
        },
      } as any;
    }

    onUpdate(newData);
  };

  const renderFormGroup = (title: string, children: React.ReactNode) => (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 border-b pb-2">{title}</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{children}</div>
    </div>
  );

  const renderField = (
    label: string,
    field: string,
    type: "text" | "select" | "date" | "number" = "text",
    options?: string[],
    required = false
  ) => (
    <div>
      <Label htmlFor={field}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      {type === "select" ? (
        <Select
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
              : (data[field as keyof FamilyInfoData] as string)
          }
          onValueChange={(value) => handleInputChange(field, value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={`Pilih ${label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {options?.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : type === "date" ? (
        <EnhancedDatePicker
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data)
                ? new Date(
                    field.split(".").reduce((obj, key) => obj?.[key], data)
                  )
                : undefined
              : undefined
          }
          onChange={(date) =>
            handleInputChange(field, date ? date.toISOString() : "")
          }
          placeholder="Pilih tanggal"
          minYear={1950}
          maxYear={new Date().getFullYear()}
        />
      ) : (
        <Input
          id={field}
          type={type}
          value={
            field.includes(".")
              ? field.split(".").reduce((obj, key) => obj?.[key], data) || ""
              : (data[field as keyof FamilyInfoData] as string) || ""
          }
          onChange={(e) =>
            handleInputChange(
              field,
              type === "number" ? Number(e.target.value) : e.target.value
            )
          }
          placeholder={`Masukkan ${label.toLowerCase()}`}
        />
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Spouse Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Pasangan</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderFormGroup(
            "Data Pasangan",
            <>
              {renderField("Nama Pasangan", "spouse.name", "text")}
              {renderField("Tanggal Lahir", "spouse.dateOfBirth", "date")}
              {renderField("Tanggal Menikah", "spouse.marriageDate", "date")}
              {renderField(
                "Pendidikan Terakhir",
                "spouse.lastEducation",
                "select",
                educationLevels
              )}
              {renderField("Pekerjaan", "spouse.occupation", "text")}
              {renderField("Jumlah Anak", "spouse.numberOfChildren", "number")}
            </>
          )}
        </CardContent>
      </Card>

      {/* Parents Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Orang Tua</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderFormGroup(
            "Data Ayah",
            <>
              {renderField("Nama Ayah", "parents.father.name", "text")}
              {renderField(
                "Tanggal Lahir Ayah",
                "parents.father.dateOfBirth",
                "date"
              )}
              {renderField(
                "Pendidikan Terakhir Ayah",
                "parents.father.lastEducation",
                "select",
                educationLevels
              )}
              {renderField("Pekerjaan Ayah", "parents.father.occupation", "text")}
              {renderField("Keterangan Ayah", "parents.father.description", "text")}
              <div></div> {/* Empty div for grid alignment */}
            </>
          )}

          <Separator />

          {renderFormGroup(
            "Data Ibu",
            <>
              {renderField("Nama Ibu", "parents.mother.name", "text")}
              {renderField(
                "Tanggal Lahir Ibu",
                "parents.mother.dateOfBirth",
                "date"
              )}
              {renderField(
                "Pendidikan Terakhir Ibu",
                "parents.mother.lastEducation",
                "select",
                educationLevels
              )}
              {renderField("Pekerjaan Ibu", "parents.mother.occupation", "text")}
              {renderField("Keterangan Ibu", "parents.mother.description", "text")}
              <div></div> {/* Empty div for grid alignment */}
            </>
          )}
        </CardContent>
      </Card>

      {/* Siblings Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Saudara Kandung</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderFormGroup(
            "Data Saudara",
            <>
              {renderField("Anak ke-", "siblings.childOrder", "number")}
              {renderField("Jumlah Saudara", "siblings.totalSiblings", "number")}
            </>
          )}
        </CardContent>
      </Card>

      {/* In-laws Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Mertua</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderFormGroup(
            "Data Ayah Mertua",
            <>
              {renderField("Nama Ayah Mertua", "inLaws.fatherInLaw.name", "text")}
              {renderField(
                "Tanggal Lahir Ayah Mertua",
                "inLaws.fatherInLaw.dateOfBirth",
                "date"
              )}
              {renderField(
                "Pendidikan Terakhir Ayah Mertua",
                "inLaws.fatherInLaw.lastEducation",
                "select",
                educationLevels
              )}
              {renderField(
                "Keterangan Ayah Mertua",
                "inLaws.fatherInLaw.description",
                "text"
              )}
            </>
          )}

          <Separator />

          {renderFormGroup(
            "Data Ibu Mertua",
            <>
              {renderField("Nama Ibu Mertua", "inLaws.motherInLaw.name", "text")}
              {renderField(
                "Tanggal Lahir Ibu Mertua",
                "inLaws.motherInLaw.dateOfBirth",
                "date"
              )}
              {renderField(
                "Pendidikan Terakhir Ibu Mertua",
                "inLaws.motherInLaw.lastEducation",
                "select",
                educationLevels
              )}
              {renderField(
                "Keterangan Ibu Mertua",
                "inLaws.motherInLaw.description",
                "text"
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Emergency Contact (Legacy) */}
      <Card>
        <CardHeader>
          <CardTitle>Kontak Darurat</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderFormGroup(
            "Informasi Kontak Darurat",
            <>
              {renderField(
                "Nama Kontak Darurat",
                "emergencyContact.name",
                "text"
              )}
              {renderField(
                "Hubungan",
                "emergencyContact.relationship",
                "text"
              )}
              {renderField(
                "Nomor Telepon",
                "emergencyContact.phone",
                "text"
              )}
              {renderField("Alamat", "emergencyContact.address", "text")}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
