import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/psg-sisinfo';

async function connectDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function cleanupDatabase() {
  try {
    console.log('🧹 Starting database cleanup...\n');

    // Get database instance
    const db = mongoose.connection.db;
    
    // List all collections
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log('📊 Found collections:', collectionNames);
    console.log('');

    // Count existing data
    const counts: Record<string, number> = {};
    for (const collectionName of collectionNames) {
      const collection = db.collection(collectionName);
      counts[collectionName] = await collection.countDocuments();
    }

    console.log('📊 Current data counts:');
    Object.entries(counts).forEach(([collection, count]) => {
      console.log(`   ${collection}: ${count} records`);
    });
    console.log('');

    const totalRecords = Object.values(counts).reduce((sum, count) => sum + count, 0);
    
    if (totalRecords === 0) {
      console.log('✅ Database is already empty!');
      return;
    }

    console.log('⚠️  WARNING: This will delete ALL data from the database!');
    console.log('   This action cannot be undone.');
    console.log(`   Total records to be deleted: ${totalRecords}\n`);

    // Delete all data from all collections
    console.log('🗑️  Deleting all collections...\n');

    let totalDeleted = 0;
    for (const collectionName of collectionNames) {
      const collection = db.collection(collectionName);
      const result = await collection.deleteMany({});
      console.log(`✅ Deleted ${result.deletedCount} records from ${collectionName}`);
      totalDeleted += result.deletedCount;
    }

    console.log(`\n🎉 Database cleanup completed successfully!`);
    console.log(`📊 Total records deleted: ${totalDeleted}`);
    console.log('📊 All collections are now empty and ready for real data.');

    // Verify cleanup
    const finalCounts: Record<string, number> = {};
    for (const collectionName of collectionNames) {
      const collection = db.collection(collectionName);
      finalCounts[collectionName] = await collection.countDocuments();
    }

    console.log('\n📊 Final data counts:');
    Object.entries(finalCounts).forEach(([collection, count]) => {
      console.log(`   ${collection}: ${count} records`);
    });

    const totalRemaining = Object.values(finalCounts).reduce((sum, count) => sum + count, 0);
    
    if (totalRemaining === 0) {
      console.log('\n✅ SUCCESS: Database is completely clean!');
    } else {
      console.log(`\n⚠️  WARNING: ${totalRemaining} records still remain in database`);
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await cleanupDatabase();
    
    console.log('\n🚀 Database is ready for real data testing!');
    console.log('   You can now:');
    console.log('   1. Create real departments, positions, etc.');
    console.log('   2. Add real employee data');
    console.log('   3. Test all CRUD operations with actual data');
    console.log('   4. Verify system behavior with real-world scenarios');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the cleanup
main();
