import mongoose from "mongoose";
import Division from "../modules/hr/models/Division";
import { connectDatabase } from "../config/database";

const divisions = [
  {
    name: "Support",
    description: "Divisi Support dan <PERSON>",
    isActive: true,
  },
  {
    name: "Operations",
    description: "Divisi Operasional dan Produksi",
    isActive: true,
  },
  {
    name: "Finance",
    description: "Divisi Keuangan dan Akuntansi",
    isActive: true,
  },
  {
    name: "Human Resources",
    description: "Divisi Sumber Daya Manusia",
    isActive: true,
  },
  {
    name: "Information Technology",
    description: "Divisi Teknologi Informasi",
    isActive: true,
  },
  {
    name: "Marketing",
    description: "Divisi Pemasaran dan <PERSON>",
    isActive: true,
  },
  {
    name: "Legal",
    description: "Divisi Hukum da<PERSON>",
    isActive: true,
  },
  {
    name: "Research & Development",
    description: "Divisi Penelitian dan Pengembangan",
    isActive: true,
  },
];

async function seedDivisions() {
  try {
    console.log("🌱 Starting division seeding...");

    // Connect to database
    await connectDatabase();
    console.log("✅ Connected to database");

    // Clear existing divisions
    await Division.deleteMany({});
    console.log("🗑️  Cleared existing divisions");

    // Insert new divisions
    const createdDivisions = await Division.insertMany(divisions);
    console.log(`✅ Created ${createdDivisions.length} divisions:`);

    createdDivisions.forEach((division, index) => {
      console.log(`   ${index + 1}. ${division.name} (ID: ${division._id})`);
    });

    console.log("🎉 Division seeding completed successfully!");
  } catch (error) {
    console.error("❌ Error seeding divisions:", error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  }
}

// Run seeder if this file is executed directly
if (require.main === module) {
  seedDivisions();
}

export default seedDivisions;
