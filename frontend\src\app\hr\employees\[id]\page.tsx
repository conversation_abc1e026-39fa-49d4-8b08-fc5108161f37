"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building2,
  User,
  Briefcase,
  Users,
  DollarSign,
  Shield,
  X,
  ZoomIn,
} from "lucide-react";
import { useRouter, useParams } from "next/navigation";

interface Employee {
  id: number;
  employeeId: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  employmentType: string;
  joinDate: string;
  status: string;
  avatar: string | null;
  salary: string;
  // Additional details
  dateOfBirth: string;
  gender: string;
  maritalStatus: string;
  address: string;
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
}

const EmployeeDetailPage = () => {
  const router = useRouter();
  const params = useParams();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [photoPreviewOpen, setPhotoPreviewOpen] = useState(false);

  // Debug log for photo preview state changes
  useEffect(() => {
    console.log("Photo preview state changed:", photoPreviewOpen);
  }, [photoPreviewOpen]);

  useEffect(() => {
    const fetchEmployee = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("accessToken");
        if (!token) {
          router.push("/login");
          return;
        }

        const response = await fetch(
          `http://localhost:5000/api/hr/employees/${params.id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch employee");
        }

        const result = await response.json();
        if (result.success && result.data) {
          // Map backend data structure to frontend interface
          const emp = result.data;
          const mappedEmployee = {
            id: emp._id,
            employeeId: emp.personal?.employeeId || "Tidak tersedia",
            name: emp.personal?.fullName || "Nama tidak tersedia",
            email:
              emp.hr?.companyEmail || emp.personal?.email || "Tidak tersedia",
            phone: emp.personal?.phone || "Tidak tersedia",
            department: emp.hr?.department?.name || "Tidak tersedia",
            position: emp.hr?.position?.name || "Tidak tersedia",
            employmentType:
              emp.hr?.contract?.employmentType?.name || "Tidak tersedia",
            joinDate: emp.hr?.contract?.hireDate || emp.hr?.hireDate,
            status: emp.status || "Tidak tersedia",
            avatar: emp.personal?.profilePhoto,
            salary: emp.hr?.salary?.total
              ? `Rp ${emp.hr.salary.total.toLocaleString("id-ID")}`
              : "Tidak tersedia",
            // Additional details
            dateOfBirth: emp.personal?.dateOfBirth,
            gender: emp.personal?.gender || "Tidak tersedia",
            maritalStatus:
              emp.personal?.maritalInfo?.status || "Tidak tersedia",
            address: emp.personal?.currentAddress?.street
              ? `${emp.personal.currentAddress.street}, ${emp.personal.currentAddress.city}, ${emp.personal.currentAddress.province}`
              : "Alamat tidak tersedia",
            emergencyContact: {
              name: emp.hr?.emergency?.contactName || "Tidak tersedia",
              relationship: emp.hr?.emergency?.relationship || "Tidak tersedia",
              phone: emp.hr?.emergency?.contactPhone || "Tidak tersedia",
            },
          };
          setEmployee(mappedEmployee);
        } else {
          throw new Error(result.message || "Failed to load employee data");
        }
      } catch (error) {
        console.error("Error fetching employee:", error);
        setEmployee(null);
      } finally {
        setIsLoading(false);
      }
    };

    // Set mock token if not exists (for development)
    if (typeof window !== "undefined" && !localStorage.getItem("accessToken")) {
      localStorage.setItem("accessToken", "mock-token-for-development");
    }

    fetchEmployee();
  }, [params.id, router]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Aktif":
        return "bg-green-100 text-green-800";
      case "Tidak Aktif":
        return "bg-red-100 text-red-800";
      case "Berhenti":
        return "bg-orange-100 text-orange-800";
      case "Resign":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case "Karyawan Tetap":
        return "bg-blue-100 text-blue-800";
      case "Karyawan Kontrak":
        return "bg-purple-100 text-purple-800";
      case "Karyawan Paruh Waktu":
        return "bg-yellow-100 text-yellow-800";
      case "Magang":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Karyawan Tidak Ditemukan
            </h1>
            <p className="text-gray-600 mt-1">
              Data karyawan yang Anda cari tidak tersedia
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Detail Karyawan
            </h1>
            <p className="text-gray-600 mt-1">Informasi lengkap karyawan</p>
          </div>
        </div>
        <Button
          onClick={() => router.push(`/hr/employees/edit/${employee.id}`)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Edit className="w-4 h-4 mr-2" />
          Edit
        </Button>
      </div>

      {/* Employee Profile Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start space-x-6">
            {/* Square Profile Photo - Clickable for Preview */}
            <div
              className="relative group w-32 h-32 border-3 border-white shadow-lg rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer"
              style={{ userSelect: "none" }}
              onMouseEnter={() => console.log("Mouse entered photo area")}
              onMouseLeave={() => console.log("Mouse left photo area")}
              onMouseDown={(e) => {
                e.preventDefault();
                console.log("Mouse down on photo container");
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(
                  "Photo container clicked, avatar:",
                  employee.avatar
                );
                console.log("Photo preview state:", photoPreviewOpen);
                if (employee.avatar && employee.avatar.trim() !== "") {
                  console.log("Setting photo preview to true");
                  setPhotoPreviewOpen(true);
                } else {
                  console.log("No avatar or empty avatar");
                }
              }}
            >
              {employee.avatar && employee.avatar.trim() !== "" ? (
                <img
                  src={employee.avatar}
                  alt={employee.name}
                  className="w-full h-full object-cover pointer-events-none"
                  onError={(e) => {
                    console.log("Image failed to load:", employee.avatar);
                    // Hide the image and show initials instead
                    e.currentTarget.style.display = "none";
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      parent.innerHTML = `<span class="text-2xl font-semibold text-white">${
                        employee.name
                          ? employee.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()
                              .slice(0, 2)
                          : "NN"
                      }</span>`;
                    }
                  }}
                  onLoad={() => {
                    console.log("Image loaded successfully:", employee.avatar);
                  }}
                />
              ) : (
                <span className="text-2xl font-semibold text-white">
                  {employee.name
                    ? employee.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase()
                        .slice(0, 2)
                    : "NN"}
                </span>
              )}

              {/* Hover overlay with zoom icon - only show if there's an image */}
              {employee.avatar && employee.avatar.trim() !== "" && (
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 pointer-events-none">
                  <ZoomIn className="w-8 h-8 text-white" />
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h2 className="text-2xl font-bold text-gray-900">
                  {employee.name || "Nama tidak tersedia"}
                </h2>
                <Badge className={getStatusColor(employee.status)}>
                  {employee.status || "Tidak tersedia"}
                </Badge>
                <Badge
                  variant="outline"
                  className={getEmploymentTypeColor(employee.employmentType)}
                >
                  {employee.employmentType || "Tidak tersedia"}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">ID:</span>
                  <span>{employee.employeeId || "Tidak tersedia"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Building2 className="w-4 h-4" />
                  <span>{employee.department || "Tidak tersedia"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Briefcase className="w-4 h-4" />
                  <span>{employee.position || "Tidak tersedia"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>{employee.email || "Tidak tersedia"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4" />
                  <span>{employee.phone || "Tidak tersedia"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>
                    Bergabung:{" "}
                    {employee.joinDate
                      ? new Date(employee.joinDate).toLocaleDateString("id-ID")
                      : "Tidak tersedia"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="personal" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="personal" className="flex items-center space-x-2">
            <User className="w-4 h-4" />
            <span>Data Pribadi</span>
          </TabsTrigger>
          <TabsTrigger
            value="employment"
            className="flex items-center space-x-2"
          >
            <Briefcase className="w-4 h-4" />
            <span>Kepegawaian</span>
          </TabsTrigger>
          <TabsTrigger value="family" className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>Keluarga</span>
          </TabsTrigger>
          <TabsTrigger
            value="documents"
            className="flex items-center space-x-2"
          >
            <Shield className="w-4 h-4" />
            <span>Dokumen</span>
          </TabsTrigger>
        </TabsList>

        {/* Personal Information */}
        <TabsContent value="personal">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Pribadi</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Tanggal Lahir
                    </label>
                    <p className="text-gray-900">
                      {employee.dateOfBirth
                        ? new Date(employee.dateOfBirth).toLocaleDateString(
                            "id-ID"
                          )
                        : "Tidak tersedia"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Jenis Kelamin
                    </label>
                    <p className="text-gray-900">
                      {employee.gender || "Tidak tersedia"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Status Pernikahan
                    </label>
                    <p className="text-gray-900">
                      {employee.maritalStatus || "Tidak tersedia"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Kewarganegaraan
                    </label>
                    <p className="text-gray-900">Indonesia</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Alamat</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-2">
                  <MapPin className="w-4 h-4 text-gray-400 mt-1" />
                  <p className="text-gray-900">
                    {employee.address || "Alamat tidak tersedia"}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Employment Information */}
        <TabsContent value="employment">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Pekerjaan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Departemen
                    </label>
                    <p className="text-gray-900">
                      {employee.department || "Tidak tersedia"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Posisi
                    </label>
                    <p className="text-gray-900">
                      {employee.position || "Tidak tersedia"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Jenis Kepegawaian
                    </label>
                    <p className="text-gray-900">
                      {employee.employmentType || "Tidak tersedia"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Tanggal Bergabung
                    </label>
                    <p className="text-gray-900">
                      {employee.joinDate
                        ? new Date(employee.joinDate).toLocaleDateString(
                            "id-ID"
                          )
                        : "Tidak tersedia"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Kompensasi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Gaji
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {employee.salary || "Tidak tersedia"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Family Information */}
        <TabsContent value="family">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Kontak Darurat</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Nama
                  </label>
                  <p className="text-gray-900">
                    {employee.emergencyContact?.name || "Tidak tersedia"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Hubungan
                  </label>
                  <p className="text-gray-900">
                    {employee.emergencyContact?.relationship ||
                      "Tidak tersedia"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Nomor Telepon
                  </label>
                  <p className="text-gray-900">
                    {employee.emergencyContact?.phone || "Tidak tersedia"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents */}
        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Dokumen Karyawan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  Fitur dokumen akan segera tersedia
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Photo Preview Dialog */}
      <Dialog open={photoPreviewOpen} onOpenChange={setPhotoPreviewOpen}>
        <DialogContent className="max-w-4xl w-full p-0 bg-black/95">
          <DialogHeader className="absolute top-4 left-4 z-10">
            <DialogTitle className="text-white text-lg">
              Foto Profil - {employee?.name || "Karyawan"}
            </DialogTitle>
          </DialogHeader>

          {/* Close button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
            onClick={() => setPhotoPreviewOpen(false)}
          >
            <X className="w-6 h-6" />
          </Button>

          {/* Large photo preview */}
          <div className="flex items-center justify-center min-h-[70vh] p-8">
            {employee?.avatar && employee.avatar.trim() !== "" ? (
              <img
                src={employee.avatar}
                alt={employee.name}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                style={{ maxHeight: "80vh", maxWidth: "90vw" }}
                onError={(e) => {
                  console.log("Preview image failed to load:", employee.avatar);
                  // Show fallback initials if image fails to load
                  const fallbackDiv = document.createElement("div");
                  fallbackDiv.className =
                    "w-96 h-96 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center";
                  fallbackDiv.innerHTML = `<span class="text-6xl font-semibold text-white">${
                    employee?.name
                      ? employee.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()
                          .slice(0, 2)
                      : "NN"
                  }</span>`;
                  e.currentTarget.parentElement?.replaceChild(
                    fallbackDiv,
                    e.currentTarget
                  );
                }}
              />
            ) : (
              <div className="w-96 h-96 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-6xl font-semibold text-white">
                  {employee?.name
                    ? employee.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase()
                        .slice(0, 2)
                    : "NN"}
                </span>
              </div>
            )}
          </div>

          {/* Employee info overlay */}
          <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between text-white">
              <div>
                <h3 className="text-xl font-semibold">{employee?.name}</h3>
                <p className="text-gray-300">
                  {employee?.position} • {employee?.department}
                </p>
                <p className="text-gray-400 text-sm">
                  ID: {employee?.employeeId}
                </p>
              </div>
              <Badge className="bg-white/20 text-white border-white/30">
                {employee?.status}
              </Badge>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmployeeDetailPage;
