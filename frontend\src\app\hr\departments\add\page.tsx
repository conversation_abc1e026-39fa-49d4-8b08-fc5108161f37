"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { toast } from "sonner";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";

interface Division {
  _id: string;
  name: string;
  isActive: boolean;
}

const AddDepartmentPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [loadingDivisions, setLoadingDivisions] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    manager: "",
    description: "",
    divisionId: "",
    isActive: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [divisions, setDivisions] = useState<Division[]>([]);

  useEffect(() => {
    fetchDivisions();
  }, []);

  const fetchDivisions = async () => {
    try {
      setLoadingDivisions(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch("http://localhost:5000/api/hr/divisions", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log("Divisions API response:", result);

      if (result.success && Array.isArray(result.data)) {
        // Filter hanya divisi yang aktif
        const activeDivisions = result.data.filter(
          (division: Division) => division.isActive === true
        );
        console.log("Active divisions:", activeDivisions);
        setDivisions(activeDivisions);

        if (activeDivisions.length === 0) {
          toast.error("Tidak ada divisi aktif", {
            description: "Silakan tambahkan divisi terlebih dahulu",
          });
        }
      } else {
        console.error("Failed to fetch divisions:", result);
        toast.error("Gagal memuat data divisi", {
          description: result.message || "Format data tidak valid",
        });
      }
    } catch (error) {
      console.error("Error fetching divisions:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data divisi",
      });
    } finally {
      setLoadingDivisions(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleSwitchChange = (field: string, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSelectChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user selects
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nama departemen wajib diisi";
    }

    if (formData.name.trim().length > 100) {
      newErrors.name = "Nama departemen maksimal 100 karakter";
    }

    if (!formData.divisionId) {
      newErrors.divisionId = "Divisi wajib dipilih";
    }

    if (formData.manager.trim().length > 100) {
      newErrors.manager = "Nama manager maksimal 100 karakter";
    }

    if (formData.description.trim().length > 500) {
      newErrors.description = "Deskripsi maksimal 500 karakter";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch("http://localhost:5000/api/hr/departments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Department berhasil ditambahkan!", {
          description: `${formData.name} telah ditambahkan ke sistem`,
          action: {
            label: "Lihat",
            onClick: () => router.push("/hr/departments"),
          },
        });

        // Delay navigation to show toast
        setTimeout(() => {
          router.push("/hr/departments");
        }, 1000);
      } else {
        if (result.data?.errors) {
          // Handle validation errors from backend
          const backendErrors: Record<string, string> = {};
          result.data.errors.forEach((error: any) => {
            backendErrors[error.field] = error.message;
          });
          setErrors(backendErrors);

          toast.error("Data tidak valid", {
            description: "Periksa kembali form yang Anda isi",
          });
        } else {
          toast.error("Gagal menyimpan department", {
            description: result.message || "Terjadi kesalahan pada server",
          });
        }
      }
    } catch (error) {
      console.error("Error creating department:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/hr/departments")}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Departemen
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Tambah Departemen
          </h1>
          <p className="text-gray-600 mt-1">Buat departemen baru</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Departemen</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Department Name */}
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Departemen <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Masukkan nama departemen"
                  className={cn("h-11", errors.name ? "border-red-500" : "")}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              {/* Division */}
              <div className="space-y-2">
                <Label>
                  Divisi <span className="text-red-500">*</span>
                </Label>
                <SearchableSelect
                  value={formData.divisionId}
                  onValueChange={(value) =>
                    handleSelectChange("divisionId", value)
                  }
                  placeholder={
                    loadingDivisions ? "Memuat divisi..." : "Pilih divisi"
                  }
                  searchPlaceholder="Cari divisi..."
                  emptyMessage="Tidak ada divisi aktif ditemukan."
                  disabled={loadingDivisions}
                  className={errors.divisionId ? "border-red-500" : ""}
                  options={divisions.map((division) => ({
                    value: division._id || "",
                    label: division.name || "Unknown Division",
                  }))}
                />
                {errors.divisionId && (
                  <p className="text-sm text-red-500">{errors.divisionId}</p>
                )}
              </div>
            </div>

            {/* Manager - Full width */}
            <div className="space-y-2">
              <Label htmlFor="manager">Nama Manager</Label>
              <Input
                id="manager"
                name="manager"
                value={formData.manager}
                onChange={handleInputChange}
                placeholder="Masukkan nama manager (opsional)"
                className={cn("h-11", errors.manager ? "border-red-500" : "")}
              />
              {errors.manager && (
                <p className="text-sm text-red-500">{errors.manager}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Keterangan</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Masukkan keterangan departemen (opsional)"
                rows={4}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <div className="flex items-center space-x-3">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("isActive", checked)
                  }
                />
                <span className="text-sm text-gray-700">
                  {formData.isActive ? "Aktif" : "Tidak Aktif"}
                </span>
              </div>
              <p className="text-xs text-gray-500">
                Departemen aktif akan muncul dalam pilihan saat membuat data
                karyawan
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/hr/departments")}
                disabled={loading}
              >
                Batal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Simpan Departemen
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddDepartmentPage;
