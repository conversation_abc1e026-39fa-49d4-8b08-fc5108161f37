"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { useRouter, useParams } from "next/navigation";

const EditEmploymentTypePage = () => {
  const router = useRouter();
  const params = useParams();
  const employmentTypeId = params.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isActive: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch employment type data
  useEffect(() => {
    if (employmentTypeId) {
      fetchEmploymentType();
    }
  }, [employmentTypeId]);

  const fetchEmploymentType = async () => {
    try {
      setIsLoadingData(true);

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/employment-types/${employmentTypeId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success && result.data) {
        setFormData({
          name: result.data.name,
          description: result.data.description || "",
          isActive:
            result.data.isActive !== undefined ? result.data.isActive : true,
        });
      } else {
        toast.error("Jenis hubungan kerja tidak ditemukan", {
          description: result.message || "Data tidak tersedia",
        });
        router.push("/hr/employment-types");
      }
    } catch (error) {
      console.error("Error fetching employment type:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data jenis hubungan kerja",
      });
      router.push("/hr/employment-types");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nama jenis hubungan kerja wajib diisi";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Nama jenis hubungan kerja minimal 2 karakter";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Periksa kembali form yang Anda isi");
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/employment-types/${employmentTypeId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            name: formData.name.trim(),
            description: formData.description.trim() || undefined,
            isActive: formData.isActive,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Jenis hubungan kerja berhasil diperbarui!", {
          description: `${formData.name} telah diperbarui`,
          action: {
            label: "Lihat",
            onClick: () => router.push("/hr/employment-types"),
          },
        });

        setTimeout(() => {
          router.push("/hr/employment-types");
        }, 1000);
      } else {
        toast.error("Gagal memperbarui jenis hubungan kerja", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error updating employment type:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">
            Memuat data jenis hubungan kerja...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/hr/employment-types")}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Jenis Hubungan Kerja
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Edit Jenis Hubungan Kerja
          </h1>
          <p className="text-gray-600 mt-1">
            Perbarui informasi jenis hubungan kerja
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informasi Jenis Hubungan Kerja</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Jenis Hubungan Kerja{" "}
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Masukkan nama jenis hubungan kerja"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Keterangan</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Masukkan keterangan jenis hubungan kerja (opsional)"
                rows={4}
              />
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleInputChange("isActive", checked)
                  }
                />
                <Label htmlFor="isActive" className="text-sm">
                  {formData.isActive ? "Aktif" : "Tidak Aktif"}
                </Label>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/hr/employment-types")}
                disabled={isLoading}
              >
                Batal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Perbarui Jenis Hubungan Kerja
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditEmploymentTypePage;
