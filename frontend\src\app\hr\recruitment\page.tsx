"use client";

import React from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import {
  UserPlus,
  Briefcase,
  Users,
  FileText,
  Calendar,
  TrendingUp,
} from "lucide-react";

const RecruitmentPage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Rekrutmen</h1>
        <p className="text-gray-600 mt-1">
          <PERSON><PERSON>la proses rekrutmen dan seleksi karyawan baru
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-8 text-center">
          <UserPlus className="w-16 h-16 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem rekrutmen yang komprehensif untuk mengelola seluruh proses
            hiring, dari posting lowongan hingga onboarding karyawan baru.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            🚀 Segera Hadir - Phase 2 Development
          </div>
        </CardContent>
      </Card>

      {/* Planned Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Briefcase className="w-5 h-5 mr-2 text-blue-600" />
              Job Posting
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Buat dan kelola posting lowongan kerja dengan template yang dapat
              disesuaikan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Users className="w-5 h-5 mr-2 text-green-600" />
              Candidate Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Kelola database kandidat dengan sistem tracking yang komprehensif
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Calendar className="w-5 h-5 mr-2 text-purple-600" />
              Interview Scheduling
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Jadwalkan dan kelola proses interview dengan sistem kalender
              terintegrasi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <FileText className="w-5 h-5 mr-2 text-orange-600" />
              Application Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Lacak status aplikasi kandidat dari tahap awal hingga keputusan
              hiring
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="w-5 h-5 mr-2 text-red-600" />
              Analytics & Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Analisis performa rekrutmen dengan metrics dan insights yang
              mendalam
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <UserPlus className="w-5 h-5 mr-2 text-indigo-600" />
              Onboarding
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Proses onboarding yang terstruktur untuk karyawan baru
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Development Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Timeline Pengembangan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Phase 1: Master Data</div>
                <div className="text-sm text-gray-600">
                  ✅ Complete - 7 master data modules
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <div className="font-medium">Phase 2: Employee Management</div>
                <div className="text-sm text-gray-600">
                  🚧 In Progress - Employee CRUD, Self Service Portal
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Phase 3: Recruitment System</div>
                <div className="text-sm text-gray-600">
                  📋 Planned - Q2 2024
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RecruitmentPage;
