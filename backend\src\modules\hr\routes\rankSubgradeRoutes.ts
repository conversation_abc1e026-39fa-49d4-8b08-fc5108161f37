import express from "express";
import { body } from "express-validator";
import {
  getRankSubgrades,
  getRankSubgradeById,
  createRankSubgrade,
  updateRankSubgrade,
  deleteRankSubgrade,
} from "../controllers/rankSubgradeController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const rankSubgradeValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Rank subgrade name is required")
    .isLength({ min: 1, max: 100 })
    .withMessage("Rank subgrade name must be between 1 and 100 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Description must not exceed 500 characters"),
];

// Routes
router.get("/", devBypass, getRankSubgrades);
router.get("/:id", devBypass, getRankSubgradeById);
router.post("/", devBypass, rankSubgradeValidation, createRankSubgrade);
router.put("/:id", devBypass, rankSubgradeValidation, updateRankSubgrade);
router.delete("/:id", devBypass, deleteRankSubgrade);

export default router;
