"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Users,
  Plus,
  Edit,
  Eye,
  Mail,
  Phone,
  Building2,
  Calendar,
  Loader2,
  Upload,
} from "lucide-react";
import { useRouter } from "next/navigation";
import EmployeeFilters from "@/components/hr/EmployeeFilters";
import { employeeService, Employee } from "@/services/employeeService";
import { toast } from "sonner";

interface FilterState {
  search: string;
  department: string;
  position: string;
  employmentType: string;
  status: string;
  joinDateFrom: string;
  joinDateTo: string;
}

const EmployeesPage = () => {
  const router = useRouter();
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    department: "",
    position: "",
    employmentType: "",
    status: "",
    joinDateFrom: "",
    joinDateTo: "",
  });

  // State for API data
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    current: 1,
    pages: 1,
    total: 0,
    limit: 10,
  });

  // Fetch employees from API
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await employeeService.getEmployees({
        page: pagination.current,
        limit: pagination.limit,
        search: filters.search || undefined,
        department: filters.department || undefined,
        position: filters.position || undefined,
        status: filters.status || undefined,
      });

      setEmployees(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error("Error fetching employees:", error);
      toast.error("Gagal memuat data karyawan", {
        description: "Silakan coba lagi atau hubungi administrator",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load employees on component mount and when filters change
  useEffect(() => {
    fetchEmployees();
  }, [
    filters.search,
    filters.department,
    filters.position,
    filters.status,
    pagination.current,
  ]);

  // Transform API data to display format
  const displayEmployees = employees.map((emp) => {
    return {
      id: emp._id,
      employeeId: emp.personal?.employeeId || "N/A",
      name:
        emp.personal?.fullName ||
        `${emp.personal?.firstName || ""} ${
          emp.personal?.lastName || ""
        }`.trim() ||
        "N/A",
      email: emp.hr?.companyEmail || emp.personal?.email || "N/A",
      phone: emp.personal?.phone || "N/A",
      division: emp.hr?.division?.name || "N/A",
      department: emp.hr?.department?.name || "N/A",
      position: emp.hr?.position?.name || "N/A",
      employmentType: emp.hr?.contract?.employmentType?.name || "N/A",
      joinDate: emp.hr?.contract?.hireDate || emp.hr?.hireDate,
      status: emp.status,
      avatar: emp.personal?.profilePhoto,
      salary: emp.hr?.salary?.total
        ? `Rp ${emp.hr.salary.total.toLocaleString("id-ID")}`
        : "N/A",
    };
  });

  const handleView = (id: string) => {
    router.push(`/hr/employees/${id}`);
  };

  const handleEdit = (id: string) => {
    router.push(`/hr/employees/edit/${id}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Aktif":
        return "bg-green-100 text-green-800";
      case "Tidak Aktif":
        return "bg-red-100 text-red-800";
      case "Berhenti":
        return "bg-orange-100 text-orange-800";
      case "Resign":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case "Karyawan Tetap":
        return "bg-blue-100 text-blue-800";
      case "Karyawan Kontrak":
        return "bg-purple-100 text-purple-800";
      case "Karyawan Paruh Waktu":
        return "bg-yellow-100 text-yellow-800";
      case "Magang":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Data Karyawan</h1>
            <p className="text-gray-600 mt-1">
              Kelola informasi karyawan PT. Prima Sarana Gemilang
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              onClick={() => router.push("/hr/employees/bulk-import")}
              variant="outline"
              className="border-blue-600 text-blue-600 hover:bg-blue-50"
            >
              <Upload className="w-4 h-4 mr-2" />
              Bulk Import
            </Button>
            <Button
              onClick={() => router.push("/hr/employees/create")}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Tambah Karyawan
            </Button>
          </div>
        </div>

        {/* Navigation Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/hr")}
            className="text-blue-600 hover:text-blue-800 p-0 h-auto"
          >
            Dashboard HR
          </Button>
          <span>/</span>
          <span className="text-gray-900 font-medium">Daftar Karyawan</span>
        </div>

        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <EmployeeFilters
                onFiltersChange={setFilters}
                totalResults={pagination.total}
              />
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">
                Memuat data karyawan...
              </span>
            </div>
          )}

          {/* Employees List */}
          {!loading && (
            <div className="space-y-3">
              {displayEmployees.map((employee) => (
                <Card
                  key={employee.id}
                  className="hover:shadow-lg transition-all duration-200 border border-gray-200 bg-white"
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 flex-1">
                        <div className="w-16 h-16 ring-2 ring-blue-100 rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                          {employee.avatar &&
                          employee.avatar.startsWith("data:") ? (
                            <img
                              src={employee.avatar}
                              alt={employee.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // Hide broken image and show fallback
                                e.currentTarget.style.display = "none";
                                const parent = e.currentTarget.parentElement;
                                if (parent) {
                                  parent.innerHTML = `<span class="text-white font-semibold text-lg">${employee.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}</span>`;
                                }
                              }}
                            />
                          ) : (
                            <span className="text-white font-semibold text-lg">
                              {employee.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </span>
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          {/* Name and Status Row */}
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-base font-bold text-gray-900 truncate">
                              {employee.name}
                            </h3>
                            <Badge
                              className={`${getStatusColor(
                                employee.status
                              )} text-xs font-medium px-2 py-1`}
                            >
                              {employee.status}
                            </Badge>
                            <Badge
                              variant="outline"
                              className={`${getEmploymentTypeColor(
                                employee.employmentType
                              )} text-xs font-medium px-2 py-1`}
                            >
                              {employee.employmentType}
                            </Badge>
                          </div>

                          {/* Information Grid */}
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-x-4 gap-y-1 text-sm">
                            <div className="flex items-center space-x-2">
                              <span className="text-gray-500 font-medium">
                                ID:
                              </span>
                              <span className="text-gray-900 font-semibold">
                                {employee.employeeId}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Building2 className="w-4 h-4 text-blue-600 flex-shrink-0" />
                              <span className="text-gray-900 font-medium truncate">
                                {employee.division}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-green-500 rounded-full flex-shrink-0"></div>
                              <span className="text-gray-900 font-medium truncate">
                                {employee.department}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-purple-500 rounded-full flex-shrink-0"></div>
                              <span className="text-gray-900 font-medium truncate">
                                {employee.position}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
                              <span className="text-gray-600 truncate">
                                {employee.email}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Phone className="w-4 h-4 text-gray-400 flex-shrink-0" />
                              <span className="text-gray-600">
                                {employee.phone}
                              </span>
                            </div>
                          </div>

                          {/* Join Date */}
                          <div className="flex items-center space-x-2 mt-1 text-sm">
                            <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
                            <span className="text-gray-500">
                              Bergabung:{" "}
                              {employee.joinDate &&
                              !isNaN(new Date(employee.joinDate).getTime())
                                ? new Date(
                                    employee.joinDate
                                  ).toLocaleDateString("id-ID")
                                : "Tidak tersedia"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-1 ml-4">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleView(employee.id)}
                              className="hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-colors px-3"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Lihat detail karyawan</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(employee.id)}
                              className="hover:bg-green-50 hover:border-green-300 hover:text-green-700 transition-colors px-3"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Edit informasi karyawan</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && displayEmployees.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Tidak ada karyawan ditemukan
                </h3>
                <p className="text-gray-600 mb-4">
                  {filters.search ||
                  Object.values(filters).some((f) => f !== "")
                    ? "Coba sesuaikan kata kunci pencarian atau filter Anda."
                    : "Mulai dengan menambahkan karyawan pertama."}
                </p>
                {!filters.search &&
                  !Object.values(filters).some((f) => f !== "") && (
                    <Button onClick={() => router.push("/hr/employees/create")}>
                      <Plus className="w-4 h-4 mr-2" />
                      Tambah Karyawan
                    </Button>
                  )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default EmployeesPage;
