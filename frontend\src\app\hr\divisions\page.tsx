"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Building,
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Division {
  _id?: string;
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const DivisionsPage: React.FC = () => {
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    fetchDivisions();
  }, []);

  const fetchDivisions = async () => {
    try {
      setIsLoading(true);

      const token = localStorage.getItem("accessToken");
      let apiSuccess = false;

      if (token) {
        try {
          const response = await fetch(
            "http://localhost:5000/api/hr/divisions",
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          const result = await response.json();

          if (result.success && Array.isArray(result.data)) {
            console.log("API data loaded successfully:", result.data);
            setDivisions(result.data);
            apiSuccess = true;
          }
        } catch (apiError) {
          console.warn("API call failed:", apiError);
        }
      }

      // If API failed, show error message
      if (!apiSuccess) {
        console.log("API failed to load divisions data");
        toast.error("Gagal memuat data divisi", {
          description: "Tidak dapat terhubung ke server. Silakan coba lagi.",
        });
        setDivisions([]);
      }
    } catch (error) {
      console.error("Error fetching divisions:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data divisi dari server",
      });
      setDivisions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredDivisions = divisions.filter(
    (division) =>
      division.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (division.description &&
        division.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleEdit = (division: Division) => {
    const id = division.id || division._id;
    router.push(`/hr/divisions/edit/${id}`);
  };

  const handleDelete = async (division: Division) => {
    const id = division.id || division._id;
    const name = division.name;
    try {
      // Show loading toast
      const loadingToast = toast.loading("Menghapus divisi...", {
        description: `Sedang menghapus ${name}`,
      });

      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.dismiss(loadingToast);
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/divisions/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success) {
        // Refresh data
        fetchDivisions();
      } else {
        toast.dismiss(loadingToast);
        toast.error("Gagal menghapus divisi", {
          description: result.message || "Terjadi kesalahan pada server",
        });
        return;
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success("Divisi berhasil dihapus!", {
        description: `${name} telah dinonaktifkan dari sistem`,
        action: {
          label: "Undo",
          onClick: () => {
            toast.info("Fitur undo akan segera tersedia");
          },
        },
      });
    } catch (error) {
      console.error("Error deleting division:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat menghapus divisi",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Divisi</h1>
            <p className="text-gray-600 mt-1">Kelola divisi perusahaan</p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => router.push("/hr/divisions/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Divisi
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Buat divisi baru</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari divisi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">
                {filteredDivisions.length} divisi
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Memuat data divisi...</p>
            </CardContent>
          </Card>
        )}

        {/* Divisions Grid */}
        {!isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDivisions.map((division) => (
              <Card
                key={division.id || division._id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Building className="w-6 h-6 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          {division.name}
                        </CardTitle>
                      </div>
                    </div>
                    <Badge
                      variant={division.isActive ? "default" : "secondary"}
                      className={
                        division.isActive ? "bg-green-100 text-green-800" : ""
                      }
                    >
                      {division.isActive ? "Aktif" : "Tidak Aktif"}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">
                    {division.description || "Tidak ada keterangan"}
                  </p>

                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    Dibuat:{" "}
                    {new Date(division.createdAt).toLocaleDateString("id-ID")}
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(division)}
                          className="flex-1"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit informasi divisi</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(division)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Hapus divisi</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredDivisions.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada divisi
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm
                  ? "Coba sesuaikan kata kunci pencarian."
                  : "Mulai dengan membuat divisi pertama."}
              </p>
              {!searchTerm && (
                <Button onClick={() => router.push("/hr/divisions/add")}>
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Divisi
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </TooltipProvider>
  );
};

export default DivisionsPage;
