import { Request, Response } from "express";
import RankSubgrade from "../models/RankSubgrade";

// Get all rank subgrades
export const getRankSubgrades = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { search, status } = req.query;

    // Build filter - show all records (active and inactive)
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const rankSubgrades = await RankSubgrade.find(filter).sort({
      createdAt: -1,
    });

    res.json({
      success: true,
      data: rankSubgrades,
      message: "Rank subgrades retrieved successfully",
      count: rankSubgrades.length,
    });
  } catch (error) {
    console.error("Error fetching rank subgrades:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const getRankSubgradeById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const rankSubgrade = await RankSubgrade.findById(id);

    if (!rankSubgrade) {
      res.status(404).json({
        success: false,
        message: "Rank subgrade not found",
      });
      return;
    }

    res.json({
      success: true,
      data: rankSubgrade,
      message: "Rank subgrade retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching rank subgrade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const createRankSubgrade = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { name, description, isActive = true } = req.body;

    // Check if rank subgrade already exists (only check active ones)
    const existingRankSubgrade = await RankSubgrade.findOne({
      name,
      isActive: true,
    });
    if (existingRankSubgrade) {
      res.status(400).json({
        success: false,
        message: "Rank subgrade already exists",
      });
      return;
    }

    const rankSubgrade = new RankSubgrade({
      name,
      description,
      isActive, // Set active status
      // Skip createdBy/updatedBy for development
    });

    await rankSubgrade.save();

    res.status(201).json({
      success: true,
      data: rankSubgrade,
      message: "Rank subgrade created successfully",
    });
  } catch (error) {
    console.error("Error creating rank subgrade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const updateRankSubgrade = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description, isActive } = req.body;

    const rankSubgrade = await RankSubgrade.findById(id);
    if (!rankSubgrade) {
      res.status(404).json({
        success: false,
        message: "Rank subgrade not found",
      });
      return;
    }

    // Update rank subgrade
    rankSubgrade.name = name || rankSubgrade.name;
    rankSubgrade.description = description || rankSubgrade.description;
    if (isActive !== undefined) rankSubgrade.isActive = isActive; // Update active status
    // Skip updatedBy for development

    await rankSubgrade.save();

    res.json({
      success: true,
      data: rankSubgrade,
      message: "Rank subgrade updated successfully",
    });
  } catch (error) {
    console.error("Error updating rank subgrade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const deleteRankSubgrade = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const rankSubgrade = await RankSubgrade.findById(id);
    if (!rankSubgrade) {
      res.status(404).json({
        success: false,
        message: "Rank subgrade not found",
      });
      return;
    }

    // Status-based delete
    rankSubgrade.isActive = false;
    // Skip updatedBy for development

    await rankSubgrade.save();

    res.json({
      success: true,
      message: "Rank subgrade deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting rank subgrade:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
