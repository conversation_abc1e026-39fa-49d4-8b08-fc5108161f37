import express from "express";
import { body } from "express-validator";
import {
  getPositions,
  getPositionById,
  createPosition,
  updatePosition,
  deletePosition,
} from "../controllers/positionController";
import { authenticate } from "../../user-access/middleware/authMiddleware";

const router = express.Router();

// Development bypass middleware
const devBypass = (req: any, res: any, next: any) => {
  // For development, bypass authentication
  if (
    process.env.NODE_ENV === "development" ||
    req.headers.authorization?.includes("mock-token")
  ) {
    req.user = { _id: "000000000000000000000000" }; // Valid ObjectId for dev
    return next();
  }
  return authenticate(req, res, next);
};

// Validation rules
const positionValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Position name is required")
    .isLength({ min: 2, max: 100 })
    .withMessage("Position name must be between 2 and 100 characters"),
  body("department")
    .notEmpty()
    .withMessage("Department is required")
    .isMongoId()
    .withMessage("Invalid department ID"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Description must not exceed 500 characters"),
];

// Routes
router.get("/", devBypass, getPositions);
router.get("/:id", devBypass, getPositionById);
router.post("/", devBypass, positionValidation, createPosition);
router.put("/:id", devBypass, positionValidation, updatePosition);
router.delete("/:id", devBypass, deletePosition);

export default router;
