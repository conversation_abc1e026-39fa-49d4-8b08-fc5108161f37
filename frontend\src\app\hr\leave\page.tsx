"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Bell,
  Users,
} from "lucide-react";

const LeavePage = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Cuti & Izin</h1>
        <p className="text-gray-600 mt-1">
          Sistem manajemen cuti dan izin dengan workflow persetujuan otomatis
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="bg-gradient-to-r from-purple-50 to-violet-50 border-purple-200">
        <CardContent className="p-8 text-center">
          <Calendar className="w-16 h-16 text-purple-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Man<PERSON><PERSON>en Cuti & Izin
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Sistem cuti yang komprehensif dengan workflow persetujuan
            bertingkat, tracking saldo cuti, dan integrasi kalender untuk
            planning yang optimal.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
            🚀 Priority High - Phase 2A Development
          </div>
        </CardContent>
      </Card>

      {/* Key Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <FileText className="w-5 h-5 mr-2 text-blue-600" />
              Leave Request
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Pengajuan cuti yang mudah dengan form digital dan upload dokumen
              pendukung
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
              Approval Workflow
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Workflow persetujuan bertingkat dari supervisor hingga HR dengan
              notifikasi real-time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Clock className="w-5 h-5 mr-2 text-orange-600" />
              Leave Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Tracking saldo cuti real-time dengan perhitungan otomatis
              berdasarkan kebijakan perusahaan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Calendar className="w-5 h-5 mr-2 text-purple-600" />
              Calendar Integration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Integrasi kalender untuk visualisasi jadwal cuti tim dan planning
              yang optimal
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Bell className="w-5 h-5 mr-2 text-red-600" />
              Smart Notifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Notifikasi otomatis untuk pengajuan, persetujuan, dan reminder
              saldo cuti
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Users className="w-5 h-5 mr-2 text-indigo-600" />
              Team Planning
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm">
              Planning cuti tim dengan conflict detection dan coverage planning
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Leave Types */}
      <Card>
        <CardHeader>
          <CardTitle>Jenis Cuti & Izin</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900">Cuti Tahunan</h4>
              <p className="text-sm text-blue-700 mt-1">12 hari per tahun</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900">Cuti Sakit</h4>
              <p className="text-sm text-green-700 mt-1">Dengan surat dokter</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-900">Cuti Melahirkan</h4>
              <p className="text-sm text-purple-700 mt-1">3 bulan</p>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-900">Cuti Menikah</h4>
              <p className="text-sm text-orange-700 mt-1">3 hari</p>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <h4 className="font-medium text-red-900">Cuti Duka</h4>
              <p className="text-sm text-red-700 mt-1">2-3 hari</p>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900">Izin Khusus</h4>
              <p className="text-sm text-gray-700 mt-1">Sesuai kebijakan</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Process */}
      <Card>
        <CardHeader>
          <CardTitle>Alur Persetujuan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div className="flex-1">
                <div className="font-medium">Pengajuan Karyawan</div>
                <div className="text-sm text-gray-600">
                  Karyawan mengajukan cuti melalui sistem
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div className="flex-1">
                <div className="font-medium">Review Supervisor</div>
                <div className="text-sm text-gray-600">
                  Supervisor langsung melakukan review dan persetujuan
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div className="flex-1">
                <div className="font-medium">Approval HR</div>
                <div className="text-sm text-gray-600">
                  HR melakukan final approval dan update saldo cuti
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">
                4
              </div>
              <div className="flex-1">
                <div className="font-medium">Notifikasi & Calendar Update</div>
                <div className="text-sm text-gray-600">
                  Sistem mengirim notifikasi dan update kalender tim
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LeavePage;
