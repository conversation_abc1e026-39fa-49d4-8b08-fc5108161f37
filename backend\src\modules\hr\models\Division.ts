import mongoose, { Document, Schema } from "mongoose";

export interface IDivision extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const DivisionSchema = new Schema<IDivision>(
  {
    name: {
      type: String,
      required: [true, "Nama divisi wajib diisi"],
      trim: true,
      maxlength: [100, "Nama divisi maksimal 100 karakter"],
      unique: true,
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Keterangan maksimal 500 karakter"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for better performance
DivisionSchema.index({ name: 1 });
DivisionSchema.index({ isActive: 1 });
DivisionSchema.index({ createdAt: -1 });

// Pre-save middleware to ensure name uniqueness (case-insensitive)
DivisionSchema.pre("save", async function (next) {
  if (this.isModified("name")) {
    const existingDivision = await mongoose.models.Division.findOne({
      name: { $regex: new RegExp(`^${this.name}$`, "i") },
      _id: { $ne: this._id },
    });

    if (existingDivision) {
      const error = new Error("Nama divisi sudah ada");
      return next(error);
    }
  }
  next();
});

const Division = mongoose.model<IDivision>("Division", DivisionSchema);

export default Division;
