"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Tag,
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  Loader2,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface TagItem {
  id: string;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const TagsPage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [tags, setTags] = useState<TagItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch tags from API
  const fetchTags = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        router.push("/login");
        return;
      }

      const response = await fetch("http://localhost:5000/api/hr/tags", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (result.success) {
        setTags(result.data);
      } else {
        setError(result.message || "Gagal memuat data tags");
        toast.error("Gagal memuat data tags");
      }
    } catch (error) {
      console.error("Error fetching tags:", error);
      setError("Gagal memuat data tags");
      toast.error("Gagal memuat data tags");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  const filteredTags = tags.filter(
    (tag) =>
      tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tag.description &&
        tag.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleEdit = (id: string) => {
    router.push(`/hr/tags/edit/${id}`);
  };

  const handleDelete = async (id: string, name: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Menghapus tag...", {
        description: `Sedang menghapus ${name}`,
      });

      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.dismiss(loadingToast);
        router.push("/login");
        return;
      }

      const response = await fetch(`http://localhost:5000/api/hr/tags/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const result = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success("Tag berhasil dihapus!", {
          description: `${name} telah dinonaktifkan dari sistem`,
          action: {
            label: "Undo",
            onClick: () => {
              toast.info("Fitur undo akan segera tersedia");
            },
          },
        });
        fetchTags(); // Refresh data
      } else {
        toast.error("Gagal menghapus tag", {
          description: result.message || "Terjadi kesalahan",
        });
      }
    } catch (error) {
      console.error("Error deleting tag:", error);
      toast.error("Gagal menghapus tag", {
        description: "Terjadi kesalahan saat menghapus tag",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tags</h1>
            <p className="text-gray-600 mt-1">Kelola tag karyawan</p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => router.push("/hr/tags/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Tag
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Buat tag baru</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari tag..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">{filteredTags.length} tag</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <Card>
            <CardContent className="p-12 text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-gray-600">Memuat data tags...</p>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {error && !loading && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-red-500 mb-4">
                <Tag className="w-12 h-12 mx-auto mb-2" />
                <h3 className="text-lg font-medium mb-2">Gagal Memuat Data</h3>
                <p className="text-sm">{error}</p>
              </div>
              <Button onClick={fetchTags} variant="outline">
                Coba Lagi
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Tags Grid */}
        {!loading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTags.map((tag) => (
              <Card key={tag.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: tag.color + "20" }}
                      >
                        <Tag className="w-6 h-6" style={{ color: tag.color }} />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{tag.name}</CardTitle>
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: tag.color + "20",
                            color: tag.color,
                            border: `1px solid ${tag.color}40`,
                          }}
                        >
                          {tag.name}
                        </Badge>
                      </div>
                    </div>
                    <Badge
                      variant={tag.isActive ? "default" : "secondary"}
                      className={
                        tag.isActive ? "bg-green-100 text-green-800" : ""
                      }
                    >
                      {tag.isActive ? "Aktif" : "Tidak Aktif"}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">
                    {tag.description || "Tidak ada deskripsi"}
                  </p>

                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    Dibuat:{" "}
                    {new Date(tag.createdAt).toLocaleDateString("id-ID")}
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(tag.id)}
                          className="flex-1"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit tag</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(tag.id, tag.name)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Hapus tag</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredTags.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Tag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada tag
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm
                  ? "Coba sesuaikan kata kunci pencarian."
                  : "Mulai dengan membuat tag pertama."}
              </p>
              {!searchTerm && (
                <Button onClick={() => router.push("/hr/tags/add")}>
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Tag
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </TooltipProvider>
  );
};

export default TagsPage;
