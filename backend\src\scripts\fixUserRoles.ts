import mongoose from 'mongoose';
import { connectDatabase } from '../config/database';

async function fixUserRoles() {
  try {
    console.log('🔧 Fixing user roles...\n');

    // Get database instance
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    const rolesCollection = db.collection('roles');
    
    // Get all roles
    const roles = await rolesCollection.find({}).toArray();
    console.log('🎭 Available roles:');
    roles.forEach((role, index) => {
      console.log(`   ${index + 1}. ${role.name} (${role._id})`);
    });
    
    // Get all users with string roles
    const usersWithStringRoles = await usersCollection.find({
      role: { $type: "string" }
    }).toArray();
    
    console.log(`\n👤 Found ${usersWithStringRoles.length} users with string roles`);
    
    if (usersWithStringRoles.length === 0) {
      console.log('✅ All users already have proper ObjectId roles');
      return;
    }
    
    // Fix each user
    for (const user of usersWithStringRoles) {
      console.log(`\n🔧 Fixing user: ${user.employeeId || user.email}`);
      console.log(`   Current role: ${user.role} (${typeof user.role})`);
      
      // Find matching role
      const matchingRole = roles.find(role => role.name === user.role);
      
      if (matchingRole) {
        // Update user with ObjectId role
        const result = await usersCollection.updateOne(
          { _id: user._id },
          { 
            $set: { 
              role: matchingRole._id,
              updatedAt: new Date()
            }
          }
        );
        
        console.log(`   ✅ Updated to: ${matchingRole._id} (ObjectId)`);
        console.log(`   Modified: ${result.modifiedCount} document(s)`);
      } else {
        console.log(`   ❌ No matching role found for: ${user.role}`);
      }
    }
    
    // Verify fix
    console.log('\n📊 Verification:');
    const usersStillWithStringRoles = await usersCollection.countDocuments({
      role: { $type: "string" }
    });
    
    const usersWithObjectIdRoles = await usersCollection.countDocuments({
      role: { $type: "objectId" }
    });
    
    console.log(`   Users with string roles: ${usersStillWithStringRoles}`);
    console.log(`   Users with ObjectId roles: ${usersWithObjectIdRoles}`);
    
    if (usersStillWithStringRoles === 0) {
      console.log('\n🎉 All user roles fixed successfully!');
    } else {
      console.log('\n⚠️ Some users still have string roles');
    }

  } catch (error) {
    console.error('❌ Error fixing user roles:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await fixUserRoles();
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the fix
main();
