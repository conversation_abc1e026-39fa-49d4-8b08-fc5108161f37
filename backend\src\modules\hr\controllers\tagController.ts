import { Request, Response } from "express";
import Tag from "../models/Tag";

export const getTags = async (req: Request, res: Response): Promise<void> => {
  try {
    const { search, status } = req.query;

    // Build filter - show all records (active and inactive)
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status !== undefined) {
      filter.isActive = status === "active";
    }

    const tags = await Tag.find(filter).sort({ createdAt: -1 });

    res.json({
      success: true,
      data: tags,
      message: "Tags retrieved successfully",
      count: tags.length,
    });
  } catch (error) {
    console.error("Error fetching tags:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const getTagById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const tag = await Tag.findById(id);

    if (!tag) {
      res.status(404).json({
        success: false,
        message: "Tag not found",
      });
      return;
    }

    res.json({
      success: true,
      data: tag,
      message: "Tag retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching tag:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const createTag = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, color, description, isActive } = req.body;

    // Check if tag already exists
    const existingTag = await Tag.findOne({ name });
    if (existingTag) {
      res.status(400).json({
        success: false,
        message: "Tag already exists",
      });
      return;
    }

    const tag = new Tag({
      name,
      color,
      description,
      isActive: isActive !== undefined ? isActive : true,
      // Skip createdBy/updatedBy for development
    });

    await tag.save();

    res.status(201).json({
      success: true,
      data: tag,
      message: "Tag created successfully",
    });
  } catch (error) {
    console.error("Error creating tag:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const updateTag = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, color, description, isActive } = req.body;

    const tag = await Tag.findById(id);
    if (!tag) {
      res.status(404).json({
        success: false,
        message: "Tag not found",
      });
      return;
    }

    // Update tag
    tag.name = name || tag.name;
    tag.color = color || tag.color;
    tag.description = description || tag.description;
    if (isActive !== undefined) {
      tag.isActive = isActive;
    }
    // Skip updatedBy for development

    await tag.save();

    res.json({
      success: true,
      data: tag,
      message: "Tag updated successfully",
    });
  } catch (error) {
    console.error("Error updating tag:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const deleteTag = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const tag = await Tag.findById(id);
    if (!tag) {
      res.status(404).json({
        success: false,
        message: "Tag not found",
      });
      return;
    }

    // Status-based delete (set isActive to false)
    tag.isActive = false;
    // Skip deletedBy for development

    await tag.save();

    res.json({
      success: true,
      message: "Tag deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting tag:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
