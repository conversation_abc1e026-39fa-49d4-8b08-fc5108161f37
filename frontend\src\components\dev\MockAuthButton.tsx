"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function MockAuthButton() {
  const setMockToken = () => {
    // Set a mock token for development
    localStorage.setItem("accessToken", "mock-development-token");
    toast.success("Mock auth token set for development");
    
    // Reload the page to trigger API calls with token
    window.location.reload();
  };

  const clearToken = () => {
    localStorage.removeItem("accessToken");
    toast.info("Auth token cleared");
    window.location.reload();
  };

  const checkToken = () => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      toast.info(`Token exists: ${token.substring(0, 20)}...`);
    } else {
      toast.warning("No auth token found");
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 p-4 bg-white border rounded-lg shadow-lg">
      <div className="text-sm font-medium text-gray-700">Development Tools</div>
      <Button size="sm" onClick={setMockToken} variant="outline">
        Set Mock Token
      </Button>
      <Button size="sm" onClick={clearToken} variant="outline">
        Clear Token
      </Button>
      <Button size="sm" onClick={checkToken} variant="outline">
        Check Token
      </Button>
    </div>
  );
}
