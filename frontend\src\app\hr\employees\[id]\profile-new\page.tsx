"use client";

import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  Calendar,
  Building2,
  User,
  Users,
  Briefcase,
  Download,
  Printer,
  Share2,
  MoreVertical,
  Award,
  Clock,
  DollarSign,
  Heart,
  GraduationCap,
  FileText,
  Settings,
  CreditCard,
  Smartphone,
  Baby,
  Users2,
  X,
  ZoomIn,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { format } from "date-fns";
import { id } from "date-fns/locale";

export default function EmployeeProfileNewPage() {
  const params = useParams();
  const router = useRouter();
  const employeeId = params.id as string;

  // State management
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [photoPreviewOpen, setPhotoPreviewOpen] = useState(false);

  // Mock employee data for demonstration
  const employee = {
    _id: employeeId,
    personal: {
      employeeId: "PSG001",
      fullName: "Ahmad Fauzi Rahman",
      firstName: "Ahmad",
      lastName: "Fauzi Rahman",
      email: "<EMAIL>",
      phone: "+62 812-3456-7890",
      gender: "Laki-laki",
      placeOfBirth: "Jakarta",
      dateOfBirth: "1990-05-15",
      religion: "Islam",
      bloodType: "A",
      familyCardNumber: "31710********901",
      idCardNumber: "31710********901",
      taxNumber: "12.345.678.9-012.000",
      bpjsTkNumber: "********901",
      nikKkNumber: "31710********901",
      taxStatus: "K/1",
      currentAddress: {
        street: "Jl. Sudirman No. 123",
        city: "Jakarta Selatan",
        province: "DKI Jakarta",
      },
      idCardAddress: {
        street: "Jl. Sudirman No. 123",
        city: "Jakarta Selatan",
        province: "DKI Jakarta",
      },
      contact: {
        mobilePhone1: "+62 812-3456-7890",
        mobilePhone2: "+62 821-9876-5432",
        homePhone1: "021-********",
        homePhone2: "",
      },
      maritalInfo: {
        status: "Menikah",
        spouseName: "Siti Nurhaliza",
        spouseJob: "Guru",
        numberOfChildren: 2,
      },
      bankAccount: {
        accountNumber: "********90",
        accountHolder: "Ahmad Fauzi Rahman",
        bankName: "Bank Mandiri",
      },
      profilePhoto: "/api/placeholder/150/150",
    },
    hr: {
      department: "Information Technology",
      position: "Senior Software Engineer",
      tags: ["Remote Worker", "Team Lead", "Senior"],
      contract: {
        employmentType: "Karyawan Tetap",
        hireDate: "2020-01-15",
        contractDate: "2020-01-15",
        contractEndDate: "",
      },
      education: {
        certificateLevel: "S1",
        fieldOfStudy: "Teknik Informatika",
        schoolName: "Universitas Indonesia",
        schoolCity: "Jakarta",
        description: "Cum Laude",
      },
      rank: {
        rankCategory: "Senior Staff",
        rankGrade: "Grade III",
        rankSubgrade: "Sub Grade A",
        pensionFundNumber: "PSG123456",
      },
      emergency: {
        contactName: "Siti Nurhaliza",
        contactPhone: "+62 821-9876-5432",
        relationship: "Istri",
        address: "Jl. Sudirman No. 123, Jakarta Selatan",
      },
      location: {
        pointOfOrigin: "Jakarta",
        pointOfHire: "Jakarta",
      },
      uniform: {
        workUniformSize: "L",
        workShoesSize: "42",
      },
      salary: {
        basic: 15000000,
        allowances: {
          transport: 1000000,
          meal: 500000,
          communication: 300000,
          position: 2000000,
          other: 500000,
        },
      },
      workSchedule: "Regular",
      supervisor: "Budi Santoso",
    },
    family: {
      spouse: {
        name: "Siti Nurhaliza",
        dateOfBirth: "1992-08-20",
        lastEducation: "S1",
        occupation: "Guru",
        numberOfChildren: 2,
      },
      children: [
        {
          name: "Aisyah Rahman",
          gender: "Perempuan",
          dateOfBirth: "2015-03-10",
        },
        {
          name: "Muhammad Rahman",
          gender: "Laki-laki",
          dateOfBirth: "2018-07-22",
        },
      ],
      siblings: {
        childOrder: 2,
        totalSiblings: 3,
        siblingsData: [
          {
            name: "Fatimah Rahman",
            gender: "Perempuan",
            dateOfBirth: "1988-02-14",
            lastEducation: "S1",
            occupation: "Dokter",
            description: "Kakak pertama",
          },
        ],
      },
      inLaws: {
        fatherInLaw: {
          name: "H. Abdullah",
          dateOfBirth: "1960-12-05",
          lastEducation: "SMA",
          description: "Pensiunan PNS",
        },
        motherInLaw: {
          name: "Hj. Khadijah",
          dateOfBirth: "1965-04-18",
          lastEducation: "SMA",
          description: "Ibu Rumah Tangga",
        },
      },
    },
    status: "Active",
    createdAt: "2020-01-15T00:00:00.000Z",
    updatedAt: "2024-01-15T00:00:00.000Z",
  };

  // Simulate loading
  useEffect(() => {
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd MMMM yyyy", { locale: id });
  };

  // Calculate total salary
  const totalSalary =
    employee.hr.salary.basic +
    Object.values(employee.hr.salary.allowances).reduce(
      (sum, allowance) => sum + allowance,
      0
    );

  // Calculate years of service
  const yearsOfService =
    new Date().getFullYear() -
    new Date(employee.hr.contract.hireDate).getFullYear();

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat data karyawan...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/hr/employees")}
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali ke Daftar Karyawan
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Profil Karyawan
                </h1>
                <p className="text-sm text-gray-500">
                  Detail lengkap informasi karyawan
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Printer className="w-4 h-4 mr-2" />
                Print
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Profil
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 className="w-4 h-4 mr-2" />
                    Bagikan
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Settings className="w-4 h-4 mr-2" />
                    Pengaturan
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Employee Summary */}
          <div className="lg:col-span-1 space-y-6">
            {/* Profile Card */}
            <Card className="overflow-hidden">
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 text-white text-center">
                {/* Square Profile Photo - Clickable for Preview */}
                <div className="relative group">
                  <div
                    className="w-32 h-32 mx-auto mb-4 border-4 border-white/20 rounded-lg overflow-hidden bg-white/10 flex items-center justify-center cursor-pointer transition-all duration-200 hover:scale-105 hover:border-white/40"
                    onClick={() =>
                      employee.personal.profilePhoto &&
                      employee.personal.profilePhoto.trim() !== "" &&
                      setPhotoPreviewOpen(true)
                    }
                  >
                    {employee.personal.profilePhoto &&
                    employee.personal.profilePhoto.trim() !== "" ? (
                      <img
                        src={employee.personal.profilePhoto}
                        alt={employee.personal.fullName}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.log(
                            "Image failed to load:",
                            employee.personal.profilePhoto
                          );
                          // Hide the image and show initials instead
                          e.currentTarget.style.display = "none";
                          const parent = e.currentTarget.parentElement;
                          if (parent) {
                            parent.innerHTML = `<span class="text-2xl font-semibold text-white">${employee.personal.fullName
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()
                              .slice(0, 2)}</span>`;
                          }
                        }}
                      />
                    ) : (
                      <span className="text-2xl font-semibold text-white">
                        {employee.personal.fullName
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()
                          .slice(0, 2)}
                      </span>
                    )}
                  </div>

                  {/* Hover overlay with zoom icon - only show if there's an image */}
                  {employee.personal.profilePhoto &&
                    employee.personal.profilePhoto.trim() !== "" && (
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 mb-4">
                        <ZoomIn className="w-8 h-8 text-white" />
                      </div>
                    )}
                </div>
                <h2 className="text-xl font-bold mb-1">
                  {employee.personal.fullName}
                </h2>
                <p className="text-blue-100 mb-2">{employee.hr.position}</p>
                <Badge
                  variant="secondary"
                  className="bg-white/20 text-white border-white/30"
                >
                  {employee.personal.employeeId}
                </Badge>
              </div>

              <CardContent className="p-6 space-y-4">
                <div className="flex items-center gap-3 text-sm">
                  <Building2 className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">
                    {employee.hr.department}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">
                    {employee.personal.email}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">
                    {employee.personal.phone}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">
                    Bergabung {formatDate(employee.hr.contract.hireDate)}
                  </span>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Tags</h4>
                  <div className="flex flex-wrap gap-1">
                    {employee.hr.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Statistik Cepat</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-gray-600">Masa Kerja</span>
                  </div>
                  <span className="font-medium">{yearsOfService} tahun</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600">Total Gaji</span>
                  </div>
                  <span className="font-medium">
                    {formatCurrency(totalSalary)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Heart className="w-4 h-4 text-red-600" />
                    <span className="text-sm text-gray-600">Status</span>
                  </div>
                  <Badge
                    variant="outline"
                    className="text-green-600 border-green-600"
                  >
                    {employee.status}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-600" />
                    <span className="text-sm text-gray-600">Keluarga</span>
                  </div>
                  <span className="font-medium">
                    {employee.personal.maritalInfo.status} (
                    {employee.personal.maritalInfo.numberOfChildren} anak)
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="space-y-6"
            >
              <TabsList className="grid w-full grid-cols-4 bg-white border border-gray-200">
                <TabsTrigger
                  value="overview"
                  className="flex items-center gap-2"
                >
                  <User className="w-4 h-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="personal"
                  className="flex items-center gap-2"
                >
                  <User className="w-4 h-4" />
                  Personal
                </TabsTrigger>
                <TabsTrigger value="hr" className="flex items-center gap-2">
                  <Briefcase className="w-4 h-4" />
                  HR & Karir
                </TabsTrigger>
                <TabsTrigger value="family" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Keluarga
                </TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                {/* Key Information Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-blue-100 rounded-lg">
                          <CreditCard className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Nomor Induk</p>
                          <p className="text-lg font-semibold">
                            {employee.personal.employeeId}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-green-100 rounded-lg">
                          <Calendar className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Tanggal Lahir</p>
                          <p className="text-lg font-semibold">
                            {formatDate(employee.personal.dateOfBirth)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-purple-100 rounded-lg">
                          <Award className="w-6 h-6 text-purple-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">
                            Jenis Kepegawaian
                          </p>
                          <p className="text-lg font-semibold">
                            {employee.hr.contract.employmentType}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Recent Activity & Quick Actions */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Informasi Kontak
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Mail className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Email</p>
                          <p className="font-medium">
                            {employee.personal.email}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Smartphone className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">HP Utama</p>
                          <p className="font-medium">
                            {employee.personal.contact.mobilePhone1}
                          </p>
                        </div>
                      </div>
                      {employee.personal.contact.mobilePhone2 && (
                        <div className="flex items-center gap-3">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">HP Kedua</p>
                            <p className="font-medium">
                              {employee.personal.contact.mobilePhone2}
                            </p>
                          </div>
                        </div>
                      )}
                      <div className="flex items-center gap-3">
                        <Location className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Alamat</p>
                          <p className="font-medium">
                            {employee.personal.currentAddress.street},{" "}
                            {employee.personal.currentAddress.city}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Work className="w-5 h-5" />
                        Informasi Pekerjaan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Building2 className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Departemen</p>
                          <p className="font-medium">
                            {employee.hr.department}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <UserCheck className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Posisi</p>
                          <p className="font-medium">{employee.hr.position}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Shield className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Supervisor</p>
                          <p className="font-medium">
                            {employee.hr.supervisor}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Jadwal Kerja</p>
                          <p className="font-medium">
                            {employee.hr.workSchedule}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Personal Information Tab */}
              <TabsContent value="personal" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Employee Biodata */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="w-5 h-5" />
                        Data Karyawan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Nama Lengkap</p>
                          <p className="font-medium">
                            {employee.personal.fullName}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Jenis Kelamin</p>
                          <p className="font-medium">
                            {employee.personal.gender}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Tempat Lahir</p>
                          <p className="font-medium">
                            {employee.personal.placeOfBirth}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Tanggal Lahir</p>
                          <p className="font-medium">
                            {formatDate(employee.personal.dateOfBirth)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Identification */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CreditCard className="w-5 h-5" />
                        Identifikasi
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Agama</p>
                          <p className="font-medium">
                            {employee.personal.religion}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">
                            Golongan Darah
                          </p>
                          <p className="font-medium">
                            {employee.personal.bloodType}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Nomor KK</p>
                          <p className="font-medium">
                            {employee.personal.familyCardNumber}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Nomor KTP</p>
                          <p className="font-medium">
                            {employee.personal.idCardNumber}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">NPWP</p>
                          <p className="font-medium">
                            {employee.personal.taxNumber}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Status Pajak</p>
                          <p className="font-medium">
                            {employee.personal.taxStatus}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Address Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Home className="w-5 h-5" />
                        Alamat Domisili
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="font-medium">
                          {employee.personal.currentAddress.street}
                        </p>
                        <p className="text-gray-600">
                          {employee.personal.currentAddress.city},{" "}
                          {employee.personal.currentAddress.province}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Contact Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Phone className="w-5 h-5" />
                        Informasi Kontak
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">HP 1</p>
                        <p className="font-medium">
                          {employee.personal.contact.mobilePhone1}
                        </p>
                      </div>
                      {employee.personal.contact.mobilePhone2 && (
                        <div>
                          <p className="text-sm text-gray-600">HP 2</p>
                          <p className="font-medium">
                            {employee.personal.contact.mobilePhone2}
                          </p>
                        </div>
                      )}
                      {employee.personal.contact.homePhone1 && (
                        <div>
                          <p className="text-sm text-gray-600">Telepon Rumah</p>
                          <p className="font-medium">
                            {employee.personal.contact.homePhone1}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Marital Status */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Heart className="w-5 h-5" />
                        Status Pernikahan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">Status</p>
                        <p className="font-medium">
                          {employee.personal.maritalInfo.status}
                        </p>
                      </div>
                      {employee.personal.maritalInfo.spouseName && (
                        <div>
                          <p className="text-sm text-gray-600">Nama Pasangan</p>
                          <p className="font-medium">
                            {employee.personal.maritalInfo.spouseName}
                          </p>
                        </div>
                      )}
                      <div>
                        <p className="text-sm text-gray-600">Jumlah Anak</p>
                        <p className="font-medium">
                          {employee.personal.maritalInfo.numberOfChildren} anak
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Bank Account */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CreditCard className="w-5 h-5" />
                        Rekening Bank
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">Nama Bank</p>
                        <p className="font-medium">
                          {employee.personal.bankAccount.bankName}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Nomor Rekening</p>
                        <p className="font-medium">
                          {employee.personal.bankAccount.accountNumber}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Nama Pemegang</p>
                        <p className="font-medium">
                          {employee.personal.bankAccount.accountHolder}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* HR Information Tab */}
              <TabsContent value="hr" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Contract Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Informasi Kontrak
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 gap-3">
                        <div>
                          <p className="text-sm text-gray-600">
                            Jenis Hubungan Kerja
                          </p>
                          <p className="font-medium">
                            {employee.hr.contract.employmentType}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Tanggal Masuk</p>
                          <p className="font-medium">
                            {formatDate(employee.hr.contract.hireDate)}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">
                            Tanggal Kontrak
                          </p>
                          <p className="font-medium">
                            {formatDate(employee.hr.contract.contractDate)}
                          </p>
                        </div>
                        {employee.hr.contract.contractEndDate && (
                          <div>
                            <p className="text-sm text-gray-600">
                              Tanggal Akhir Kontrak
                            </p>
                            <p className="font-medium">
                              {formatDate(employee.hr.contract.contractEndDate)}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Education */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <GraduationCap className="w-5 h-5" />
                        Pendidikan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">
                          Tingkat Pendidikan
                        </p>
                        <p className="font-medium">
                          {employee.hr.education.certificateLevel}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Bidang Studi</p>
                        <p className="font-medium">
                          {employee.hr.education.fieldOfStudy}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">
                          Nama Sekolah/Universitas
                        </p>
                        <p className="font-medium">
                          {employee.hr.education.schoolName}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Kota</p>
                        <p className="font-medium">
                          {employee.hr.education.schoolCity}
                        </p>
                      </div>
                      {employee.hr.education.description && (
                        <div>
                          <p className="text-sm text-gray-600">Keterangan</p>
                          <p className="font-medium">
                            {employee.hr.education.description}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Rank and Grade */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Award className="w-5 h-5" />
                        Pangkat dan Golongan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">
                          Kategori Pangkat
                        </p>
                        <p className="font-medium">
                          {employee.hr.rank.rankCategory}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Golongan</p>
                        <p className="font-medium">
                          {employee.hr.rank.rankGrade}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Sub Golongan</p>
                        <p className="font-medium">
                          {employee.hr.rank.rankSubgrade}
                        </p>
                      </div>
                      {employee.hr.rank.pensionFundNumber && (
                        <div>
                          <p className="text-sm text-gray-600">
                            No Dana Pensiun
                          </p>
                          <p className="font-medium">
                            {employee.hr.rank.pensionFundNumber}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Emergency Contact */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Shield className="w-5 h-5" />
                        Kontak Darurat
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">Nama Kontak</p>
                        <p className="font-medium">
                          {employee.hr.emergency.contactName}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Nomor Telepon</p>
                        <p className="font-medium">
                          {employee.hr.emergency.contactPhone}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Hubungan</p>
                        <p className="font-medium">
                          {employee.hr.emergency.relationship}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Alamat</p>
                        <p className="font-medium">
                          {employee.hr.emergency.address}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Salary Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-5 h-5" />
                        Informasi Gaji
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">Gaji Pokok</p>
                        <p className="font-medium">
                          {formatCurrency(employee.hr.salary.basic)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">
                          Tunjangan Transport
                        </p>
                        <p className="font-medium">
                          {formatCurrency(
                            employee.hr.salary.allowances.transport
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Tunjangan Makan</p>
                        <p className="font-medium">
                          {formatCurrency(employee.hr.salary.allowances.meal)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">
                          Tunjangan Jabatan
                        </p>
                        <p className="font-medium">
                          {formatCurrency(
                            employee.hr.salary.allowances.position
                          )}
                        </p>
                      </div>
                      <Separator />
                      <div>
                        <p className="text-sm text-gray-600">Total Gaji</p>
                        <p className="text-lg font-bold text-green-600">
                          {formatCurrency(totalSalary)}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Work Details */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="w-5 h-5" />
                        Detail Pekerjaan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">Point of Origin</p>
                        <p className="font-medium">
                          {employee.hr.location.pointOfOrigin}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Point of Hire</p>
                        <p className="font-medium">
                          {employee.hr.location.pointOfHire}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Ukuran Seragam</p>
                        <p className="font-medium">
                          {employee.hr.uniform.workUniformSize}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Ukuran Sepatu</p>
                        <p className="font-medium">
                          {employee.hr.uniform.workShoesSize}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Family Information Tab */}
              <TabsContent value="family" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Spouse Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Heart className="w-5 h-5" />
                        Informasi Pasangan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600">Nama Pasangan</p>
                        <p className="font-medium">
                          {employee.family.spouse.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Tanggal Lahir</p>
                        <p className="font-medium">
                          {formatDate(employee.family.spouse.dateOfBirth)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">
                          Pendidikan Terakhir
                        </p>
                        <p className="font-medium">
                          {employee.family.spouse.lastEducation}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Pekerjaan</p>
                        <p className="font-medium">
                          {employee.family.spouse.occupation}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Children Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Baby className="w-5 h-5" />
                        Informasi Anak
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {employee.family.children.length > 0 ? (
                        <div className="space-y-4">
                          {employee.family.children.map((child, index) => (
                            <div
                              key={index}
                              className="border-l-4 border-blue-200 pl-4"
                            >
                              <h4 className="font-medium">Anak {index + 1}</h4>
                              <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                                <div>
                                  <p className="text-gray-600">Nama</p>
                                  <p className="font-medium">{child.name}</p>
                                </div>
                                <div>
                                  <p className="text-gray-600">Jenis Kelamin</p>
                                  <p className="font-medium">{child.gender}</p>
                                </div>
                                <div className="col-span-2">
                                  <p className="text-gray-600">Tanggal Lahir</p>
                                  <p className="font-medium">
                                    {formatDate(child.dateOfBirth)}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-center py-4">
                          Tidak ada data anak
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  {/* Siblings Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users2 className="w-5 h-5" />
                        Saudara Kandung
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Anak Ke</p>
                          <p className="font-medium">
                            {employee.family.siblings.childOrder}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">
                            Jumlah Saudara
                          </p>
                          <p className="font-medium">
                            {employee.family.siblings.totalSiblings}
                          </p>
                        </div>
                      </div>

                      {employee.family.siblings.siblingsData.length > 0 && (
                        <div className="mt-4">
                          <h5 className="font-medium mb-3">
                            Detail Saudara Kandung
                          </h5>
                          <div className="space-y-3">
                            {employee.family.siblings.siblingsData.map(
                              (sibling, index) => (
                                <div
                                  key={index}
                                  className="border rounded-lg p-3 bg-gray-50"
                                >
                                  <div className="grid grid-cols-2 gap-2 text-sm">
                                    <div>
                                      <p className="text-gray-600">Nama</p>
                                      <p className="font-medium">
                                        {sibling.name}
                                      </p>
                                    </div>
                                    <div>
                                      <p className="text-gray-600">Pekerjaan</p>
                                      <p className="font-medium">
                                        {sibling.occupation}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* In-laws Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        Orang Tua Mertua
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Father in Law */}
                      <div className="border-l-4 border-green-200 pl-4">
                        <h5 className="font-medium mb-2">Ayah Mertua</h5>
                        <div className="space-y-2 text-sm">
                          <div>
                            <p className="text-gray-600">Nama</p>
                            <p className="font-medium">
                              {employee.family.inLaws.fatherInLaw?.name}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Tanggal Lahir</p>
                            <p className="font-medium">
                              {formatDate(
                                employee.family.inLaws.fatherInLaw
                                  ?.dateOfBirth || ""
                              )}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Keterangan</p>
                            <p className="font-medium">
                              {employee.family.inLaws.fatherInLaw?.description}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Mother in Law */}
                      <div className="border-l-4 border-pink-200 pl-4">
                        <h5 className="font-medium mb-2">Ibu Mertua</h5>
                        <div className="space-y-2 text-sm">
                          <div>
                            <p className="text-gray-600">Nama</p>
                            <p className="font-medium">
                              {employee.family.inLaws.motherInLaw?.name}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Tanggal Lahir</p>
                            <p className="font-medium">
                              {formatDate(
                                employee.family.inLaws.motherInLaw
                                  ?.dateOfBirth || ""
                              )}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Keterangan</p>
                            <p className="font-medium">
                              {employee.family.inLaws.motherInLaw?.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Photo Preview Dialog */}
      <Dialog open={photoPreviewOpen} onOpenChange={setPhotoPreviewOpen}>
        <DialogContent className="max-w-4xl w-full p-0 bg-black/95">
          <DialogHeader className="absolute top-4 left-4 z-10">
            <DialogTitle className="text-white text-lg">
              Foto Profil - {employee.personal.fullName}
            </DialogTitle>
          </DialogHeader>

          {/* Close button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
            onClick={() => setPhotoPreviewOpen(false)}
          >
            <X className="w-6 h-6" />
          </Button>

          {/* Large photo preview */}
          <div className="flex items-center justify-center min-h-[70vh] p-8">
            {employee.personal.profilePhoto &&
            employee.personal.profilePhoto.trim() !== "" ? (
              <img
                src={employee.personal.profilePhoto}
                alt={employee.personal.fullName}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                style={{ maxHeight: "80vh", maxWidth: "90vw" }}
              />
            ) : (
              <div className="w-96 h-96 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-6xl font-semibold text-white">
                  {employee.personal.fullName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()
                    .slice(0, 2)}
                </span>
              </div>
            )}
          </div>

          {/* Employee info overlay */}
          <div className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between text-white">
              <div>
                <h3 className="text-xl font-semibold">
                  {employee.personal.fullName}
                </h3>
                <p className="text-gray-300">
                  {employee.hr.position} • {employee.hr.department}
                </p>
                <p className="text-gray-400 text-sm">
                  ID: {employee.personal.employeeId}
                </p>
              </div>
              <Badge className="bg-white/20 text-white border-white/30">
                {employee.status}
              </Badge>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
