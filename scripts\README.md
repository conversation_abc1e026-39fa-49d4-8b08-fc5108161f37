# BIS Service Manager

Script untuk mengelola backend dan frontend service BIS dengan pengecekan otomatis untuk mencegah duplikasi service.

## 🚀 Fitur

- ✅ **Pengecekan Otomatis**: Mengecek apakah service sudah berjalan sebelum memulai
- 🔍 **Status Monitoring**: Melihat status real-time service yang berjalan
- 🛑 **Smart Stop**: Menghentikan service berdasarkan port yang digunakan
- 🔄 **Restart**: Restart service dengan aman
- 💀 **Kill All**: Menghentikan semua Node.js process jika terjadi masalah

## 📋 Cara Penggunaan

### Menggunakan NPM Scripts (Recommended)

```bash
# Mulai kedua service (backend + frontend)
npm run dev
# atau
npm run service:start

# Lihat status service
npm run service:status

# Hentikan kedua service
npm run service:stop

# Restart kedua service
npm run service:restart

# Kill semua Node.js process (emergency)
npm run service:kill
```

### Menggunakan Batch Script Langsung

```cmd
# Mulai kedua service
scripts\service-check.bat start

# Lihat status
scripts\service-check.bat status

# Hentikan service
scripts\service-check.bat stop

# Restart service
scripts\service-check.bat restart

# Kill semua Node.js process
scripts\service-check.bat kill
```

## 🔧 Konfigurasi Port

Script ini menggunakan port default:

- **Backend**: Port 5000
- **Frontend**: Port 3000

Jika Anda mengubah port di konfigurasi aplikasi, pastikan untuk mengupdate juga di script:

- `scripts/service-check.bat` (baris 4-5)

## 📊 Output Status

Script akan menampilkan status seperti ini:

```
📊 STATUS SERVICE BIS
====================
🟢 Backend: RUNNING (PID: 12345, Port: 5000)
🟢 Frontend: RUNNING (PID: 67890, Port: 3001)
```

atau

```
📊 STATUS SERVICE BIS
====================
🔴 Backend: STOPPED (Port: 5000)
🔴 Frontend: STOPPED (Port: 3001)
```

## 🛠️ Troubleshooting

### Service Tidak Bisa Dihentikan

Jika service tidak bisa dihentikan dengan cara normal:

```bash
# Gunakan kill-all untuk force stop semua Node.js process
npm run service:kill
```

### Port Sudah Digunakan

Jika mendapat error "port already in use":

1. Cek status service:

   ```bash
   npm run service:status
   ```

2. Hentikan service yang berjalan:

   ```bash
   npm run service:stop
   ```

3. Atau gunakan kill-all jika perlu:
   ```bash
   npm run service:kill
   ```

### PowerShell Execution Policy Error

Jika mendapat error execution policy di Windows:

```powershell
# Jalankan sebagai Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 🔄 Workflow Development

Workflow yang disarankan untuk development:

1. **Mulai Development**:

   ```bash
   npm run dev
   ```

2. **Cek Status** (jika perlu):

   ```bash
   npm run service:status
   ```

3. **Restart** (jika ada perubahan konfigurasi):

   ```bash
   npm run service:restart
   ```

4. **Selesai Development**:
   ```bash
   npm run service:stop
   ```

## ⚠️ Catatan Penting

- Script ini dirancang khusus untuk Windows
- Pastikan PowerShell tersedia di sistem
- Script akan membuka terminal baru untuk setiap service
- Gunakan `service:kill` hanya dalam keadaan darurat
- Selalu cek status sebelum memulai service baru

## 🆘 Emergency Commands

Jika terjadi masalah dan service tidak merespons:

```bash
# Kill semua Node.js process
npm run service:kill

# Tunggu beberapa detik, lalu cek status
npm run service:status

# Mulai ulang service
npm run dev
```
