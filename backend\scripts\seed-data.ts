import mongoose from "mongoose";
import { connectDatabase } from "../src/config/database";
import { Role } from "../src/modules/user-access/models/Role";
import { User } from "../src/modules/user-access/models/User";

const defaultRoles = [
  {
    name: "Super Admin",
    description: "Administrator sistem dengan akses penuh ke semua modul",
    level: 1,
    isSystem: true,
    permissions: [
      {
        module: "hr",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "inventory",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "mess",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "building",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "user-access",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: false,
        },
      },
    ],
  },
  {
    name: "HR Manager",
    description:
      "Manager HR dengan akses penuh ke modul HR dan terbatas ke modul lain",
    level: 2,
    isSystem: false,
    permissions: [
      {
        module: "hr",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "inventory",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "mess",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: false,
          approve: true,
        },
      },
      {
        module: "building",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "user-access",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: false,
        },
      },
    ],
  },
  {
    name: "Inventory Manager",
    description: "Manager Inventory dengan akses penuh ke modul Inventory",
    level: 2,
    isSystem: false,
    permissions: [
      {
        module: "hr",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "inventory",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: true,
        },
      },
      {
        module: "mess",
        actions: {
          create: false,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
      {
        module: "building",
        actions: {
          create: false,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
      {
        module: "user-access",
        actions: {
          create: false,
          read: false,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: true,
          approve: false,
        },
      },
    ],
  },
  {
    name: "Staff",
    description: "Staff dengan akses terbatas untuk operasional harian",
    level: 3,
    isSystem: false,
    permissions: [
      {
        module: "hr",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "inventory",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
      {
        module: "mess",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "building",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "user-access",
        actions: {
          create: false,
          read: false,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
    ],
  },
  {
    name: "Employee",
    description: "Karyawan dengan akses minimal untuk melihat data personal",
    level: 4,
    isSystem: false,
    permissions: [
      {
        module: "hr",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "inventory",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "mess",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "building",
        actions: {
          create: false,
          read: true,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "user-access",
        actions: {
          create: false,
          read: false,
          update: false,
          delete: false,
          approve: false,
        },
      },
      {
        module: "chatting",
        actions: {
          create: true,
          read: true,
          update: true,
          delete: false,
          approve: false,
        },
      },
    ],
  },
];

const seedRoles = async () => {
  console.log("🌱 Seeding roles...");

  for (const roleData of defaultRoles) {
    const existingRole = await Role.findOne({ name: roleData.name });

    if (!existingRole) {
      const role = new Role(roleData);
      await role.save();
      console.log(`✅ Created role: ${roleData.name}`);
    } else {
      console.log(`⚠️ Role already exists: ${roleData.name}`);
    }
  }
};

const seedAdminUser = async () => {
  console.log("🌱 Seeding admin user...");

  const adminEmail = "<EMAIL>";
  const existingAdmin = await User.findOne({ email: adminEmail });

  if (!existingAdmin) {
    const superAdminRole = await Role.findOne({ name: "Super Admin" });

    if (!superAdminRole) {
      throw new Error("Super Admin role not found. Please seed roles first.");
    }

    const adminUser = new User({
      email: adminEmail,
      password: "admin123",
      firstName: "Super",
      lastName: "Administrator",
      employeeId: "ADM001",
      role: superAdminRole._id,
      permissions: superAdminRole.getPermissionsList(),
      isActive: true,
      isEmailVerified: true,
      emailVerifiedAt: new Date(),
    });

    await adminUser.save();
    console.log("✅ Created admin user:");
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: admin123`);
    console.log(`   Employee ID: ADM001`);
  } else {
    console.log("⚠️ Admin user already exists");
  }
};

const seedTestUsers = async () => {
  console.log("🌱 Seeding test users...");

  const testUsers = [
    {
      email: "<EMAIL>",
      password: "hrmanager123",
      firstName: "HR",
      lastName: "Manager",
      employeeId: "HR001",
      roleName: "HR Manager",
    },
    {
      email: "<EMAIL>",
      password: "inventory123",
      firstName: "Inventory",
      lastName: "Manager",
      employeeId: "INV001",
      roleName: "Inventory Manager",
    },
    {
      email: "<EMAIL>",
      password: "staff123",
      firstName: "Staff",
      lastName: "Operasional",
      employeeId: "STF001",
      roleName: "Staff",
    },
    {
      email: "<EMAIL>",
      password: "employee123",
      firstName: "Karyawan",
      lastName: "Biasa",
      employeeId: "EMP001",
      roleName: "Employee",
    },
  ];

  for (const userData of testUsers) {
    const existingUser = await User.findOne({ email: userData.email });

    if (!existingUser) {
      const role = await Role.findOne({ name: userData.roleName });

      if (!role) {
        console.log(`❌ Role not found: ${userData.roleName}`);
        continue;
      }

      const user = new User({
        email: userData.email,
        password: userData.password,
        firstName: userData.firstName,
        lastName: userData.lastName,
        employeeId: userData.employeeId,
        role: role._id,
        permissions: role.getPermissionsList(),
        isActive: true,
        isEmailVerified: true,
        emailVerifiedAt: new Date(),
      });

      await user.save();
      console.log(`✅ Created user: ${userData.email} (${userData.roleName})`);
    } else {
      console.log(`⚠️ User already exists: ${userData.email}`);
    }
  }
};

const seedData = async () => {
  try {
    console.log("🚀 Starting data seeding...");
    console.log("📍 Environment:", process.env.NODE_ENV || "development");

    // Connect to database
    await connectDatabase();

    // Seed data
    await seedRoles();
    await seedAdminUser();
    await seedTestUsers();

    console.log("✅ Data seeding completed successfully!");
    console.log("\n📋 Test Accounts Created:");
    console.log(
      "┌─────────────────────────────────┬──────────────┬───────────────┐"
    );
    console.log(
      "│ Email                           │ Password     │ Role          │"
    );
    console.log(
      "├─────────────────────────────────┼──────────────┼───────────────┤"
    );
    console.log(
      "│ <EMAIL>           │ admin123     │ Super Admin   │"
    );
    console.log(
      "│ <EMAIL>      │ hrmanager123 │ HR Manager    │"
    );
    console.log(
      "│ <EMAIL>│ inventory123 │ Inventory Mgr │"
    );
    console.log(
      "│ <EMAIL>           │ staff123     │ Staff         │"
    );
    console.log(
      "│ <EMAIL>        │ employee123  │ Employee      │"
    );
    console.log(
      "└─────────────────────────────────┴──────────────┴───────────────┘"
    );
  } catch (error) {
    console.error("❌ Error seeding data:", error);
  } finally {
    await mongoose.connection.close();
    console.log("🔒 Database connection closed");
    process.exit(0);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedData();
}

export default seedData;
