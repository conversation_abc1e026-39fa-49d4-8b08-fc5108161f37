"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface FormData {
  name: string;
  description: string;
  isActive: boolean;
}

interface FormErrors {
  name?: string;
  description?: string;
}

const AddDivisionPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    isActive: true,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nama divisi wajib diisi";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Nama divisi minimal 2 karakter";
    } else if (formData.name.trim().length > 100) {
      newErrors.name = "Nama divisi maksimal 100 karakter";
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "Keterangan maksimal 500 karakter";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Periksa kembali form yang Anda isi", {
        description: "Ada beberapa field yang belum valid",
      });
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem("accessToken");

      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        router.push("/login");
        return;
      }

      const response = await fetch("http://localhost:5000/api/hr/divisions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          isActive: formData.isActive,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Divisi berhasil dibuat!", {
          description: `${formData.name} telah ditambahkan ke sistem`,
          action: {
            label: "Lihat",
            onClick: () => router.push("/hr/divisions"),
          },
        });

        // Delay navigation to show toast
        setTimeout(() => {
          router.push("/hr/divisions");
        }, 1000);
      } else {
        if (result.data?.errors) {
          // Handle validation errors from backend
          const backendErrors: Record<string, string> = {};
          result.data.errors.forEach((error: any) => {
            backendErrors[error.field] = error.message;
          });
          setErrors(backendErrors);

          toast.error("Data tidak valid", {
            description: "Periksa kembali form yang Anda isi",
          });
        } else {
          toast.error("Gagal menyimpan divisi", {
            description: result.message || "Terjadi kesalahan pada server",
          });
        }
      }
    } catch (error) {
      console.error("Error creating division:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat terhubung ke server",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleSwitchChange = (field: string, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/hr/divisions")}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali ke Divisi
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tambah Divisi</h1>
          <p className="text-gray-600 mt-1">Buat divisi baru</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Divisi</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Nama Divisi */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Nama Divisi <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Masukkan nama divisi"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            {/* Keterangan */}
            <div className="space-y-2">
              <Label htmlFor="description">Keterangan</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Masukkan keterangan divisi (opsional)"
                rows={4}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <div className="flex items-center space-x-3">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("isActive", checked)
                  }
                />
                <span className="text-sm text-gray-700">
                  {formData.isActive ? "Aktif" : "Tidak Aktif"}
                </span>
              </div>
              <p className="text-xs text-gray-500">
                Divisi aktif akan muncul dalam pilihan saat membuat data
                karyawan
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/hr/divisions")}
                disabled={isLoading}
              >
                Batal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Simpan Divisi
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddDivisionPage;
