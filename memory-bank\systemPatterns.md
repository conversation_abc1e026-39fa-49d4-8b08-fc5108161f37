# System Patterns - Bebang Information System (BIS)

## Architecture Overview

### Modular Monolith Pattern

```
Frontend (Next.js)     Backend (Node.js/Express)     Database (MongoDB)
├── modules/           ├── modules/                   └── psg-sisinfo
│   ├── hr/           │   ├── hr/                        ├── users
│   ├── inventory/    │   ├── inventory/                 ├── employees
│   ├── mess/         │   ├── mess/                      ├── inventory
│   ├── building/     │   ├── building/                  ├── mess
│   ├── user-access/  │   ├── user-access/               ├── buildings
│   └── chatting/     │   └── chatting/                  └── chats
└── shared/           └── shared/
```

### Key Design Patterns

#### 1. Module Independence Pattern

- Setiap modul memiliki struktur folder terpisah
- Shared components dan utilities di folder `shared/`
- Module dapat dikembangkan dan di-deploy secara independen
- Cross-module communication melalui well-defined APIs

#### 2. Role-Based Access Control (RBAC) Pattern

```
User → Role → Permissions → Module Access
```

- Dynamic menu generation berdasarkan user role
- Granular permissions (Create, Read, Update, Delete)
- Module-level dan feature-level access control

#### 3. Audit Trail Pattern

- Semua perubahan data dicatat dengan timestamp dan user
- Approval workflow untuk operasi sensitif
- Immutable log entries untuk compliance

#### 4. Event-Driven Communication

- Internal events untuk cross-module communication
- Real-time notifications untuk chat dan updates
- Webhook-style integration points

## Component Relationships

### Frontend Architecture

```
App Layout
├── Authentication Guard
├── Navigation (Dynamic based on role)
├── Module Router
│   ├── HR Module
│   ├── Inventory Module
│   ├── Mess Module
│   ├── Building Module
│   ├── User Access Module
│   └── Chat Module
└── Shared Components
    ├── QR Code Scanner/Generator
    ├── File Upload
    ├── Data Tables
    ├── Forms
    └── Notifications
```

### Backend Architecture

```
API Gateway
├── Authentication Middleware
├── Authorization Middleware
├── Module Routes
│   ├── /api/hr/*
│   ├── /api/inventory/*
│   ├── /api/mess/*
│   ├── /api/building/*
│   ├── /api/user-access/*
│   └── /api/chat/*
├── Shared Services
│   ├── Database Service
│   ├── File Upload Service
│   ├── QR Code Service
│   ├── Notification Service
│   └── Audit Service
└── WebSocket Server (for real-time chat)
```

## Critical Implementation Paths

### 1. Authentication & Authorization Flow

```
Login → JWT Token → Role Verification → Module Access → Permission Check
```

### 2. Data Flow Pattern

```
Frontend Request → API Validation → Business Logic → Database → Audit Log → Response
```

### 3. File Upload Pattern

```
Client Upload → Validation → Storage → Database Reference → QR Code Generation (if applicable)
```

### 4. Real-time Communication Pattern

```
User Action → WebSocket Event → Broadcast to Relevant Users → UI Update
```

### 5. Cross-Module Integration Pattern

```
Module A Request → Shared Service → Module B Data → Aggregated Response
```

## Database Design Patterns

### 1. Document-Based Design (MongoDB)

- Embedded documents untuk related data yang sering diakses bersama
- References untuk data yang shared across modules
- Indexing strategy untuk performance optimization

### 2. Audit Trail Schema

```javascript
{
  _id: ObjectId,
  entityType: String, // 'employee', 'inventory', etc.
  entityId: ObjectId,
  action: String, // 'create', 'update', 'delete'
  changes: Object, // before/after values
  userId: ObjectId,
  timestamp: Date,
  module: String
}
```

### 3. Permission Schema

```javascript
{
  _id: ObjectId,
  role: String,
  module: String,
  permissions: {
    create: Boolean,
    read: Boolean,
    update: Boolean,
    delete: Boolean,
    approve: Boolean
  }
}
```

## Integration Patterns

### HR ↔ Inventory Integration

- Employee asset allocation tracking
- Asset return on employee termination
- Asset history per employee

### HR ↔ Mess Integration

- Room allocation based on employee data
- Check-in/out tracking
- Housing allowance calculation

### Inventory ↔ Building Integration

- Office asset tracking per room
- Maintenance request integration
- Asset depreciation tracking

### Universal Chat Integration

- Context-aware chat (discuss specific records)
- File sharing within conversations
- Notification integration across all modules

## Proven Implementation Patterns ✅

### Complete Employee Management System Success

- **Employee CRUD Pattern**: Full Create, Read, Update, Delete operations with comprehensive data structure
- **Advanced Search Pattern**: Multi-criteria search with real-time filtering across all employee fields
- **Dashboard Pattern**: Professional statistics dashboard with metrics, charts, and insights
- **Multi-step Form Pattern**: Tabbed forms (Personal, HR, Family) with photo upload and validation
- **Filter Component Pattern**: Advanced filtering with popover interface and visual indicators
- **Data Visualization Pattern**: Employee statistics with department distribution and monthly summaries
- **Professional UI Pattern**: Modern, responsive design with Indonesian localization
- **Component Architecture Pattern**: Reusable components (EmployeeStats, EmployeeFilters) with proper separation
- **Master Data Integration Pattern**: Division field implementation with ObjectId references to master data collections
- **Dynamic Field Addition Pattern**: Adding new required fields (division) to existing employee structure with full CRUD support

### Complete HR Module Success with Database Migration

- **API Pattern**: RESTful endpoints with proper HTTP status codes and real data integration
- **Database Migration Pattern**: Complete migration from static/hardcoded data to MongoDB database
- **Data Persistence Pattern**: All data survives backend restarts with zero data loss
- **CRUD Pattern**: Full Create, Read, Update, Delete operations for all 7 master data modules
- **Search & Filter Pattern**: Advanced search and filtering capabilities across all master data
- **Soft Delete Pattern**: Data archiving with isDeleted flags instead of hard deletion
- **Validation Pattern**: Comprehensive validation with detailed error messages in Indonesian
- **Error Handling**: Consistent error response format across all endpoints
- **Authentication Pattern**: JWT middleware with role-based permissions and logout functionality
- **Frontend Integration**: React hooks for API calls with modern notification feedback
- **UI Pattern**: shadcn/ui components with custom Tabs and Popover components
- **Data Flow**: Frontend → API → Database → Response with toast notifications
- **Navigation Pattern**: Enhanced hierarchical navigation (Master Data → Employee Management)
- **Notification Pattern**: Toast notifications with Sonner library for professional feedback
- **Confirmation Pattern**: AlertDialog replacing browser confirm() for better UX
- **Internationalization**: 100% Indonesian language interface with business terminology
- **No Static Data Policy**: Zero tolerance for hardcoded data - all data from database
- **Status-Based Delete Pattern**: `isActive: false` pattern for data integrity instead of hard delete
- **Modern Notification Pattern**: Toast notifications (loading/success/error) replacing browser confirm()
- **Field Consistency Pattern**: isActive Switch component in all Add/Edit forms across modules
- **Delete Notification Consistency**: Unified delete experience across all HR master data modules
- **Notification System Standardization**: Consistent notification patterns with standardized text, Undo buttons, and direct delete functionality across all 7 master data modules
- **Division Field Integration Pattern**: Complete implementation of division field in employee header with master data sourcing, validation, and CRUD operations
- **SearchableSelect Pattern**: Dropdown components with search functionality for master data selection (divisions, departments, positions)
- **Active Data Filtering Pattern**: Automatic filtering of master data to show only active records (isActive: true) in form dropdowns
- **Professional Form Layout Pattern**: Organized row-based structure with logical field grouping and visual separators for enhanced user experience
- **Component Size Optimization Pattern**: Compact, space-efficient design with reduced component sizes while maintaining readability and professional appearance
- **Department Field Dynamic Pattern**: Department selection dependent on division choice with proper data filtering and validation

### Enhanced Navigation Patterns

- **Hierarchical Menu Structure**: Master Data → Manajemen Karyawan → Laporan → Pengaturan
- **Logical Workflow**: Setup → Operations → Analysis → Configuration hierarchy
- **Submenu Organization**: 7 master data items + 7 employee management features
- **Professional Placeholders**: Detailed feature descriptions for future development
- **Scalable Architecture**: Ready for rapid feature implementation
- **User Experience**: Intuitive navigation following business logic flow

### Modern UX Patterns Established

- **Employee Management UX**: Professional employee interface with dashboard, list, and detail views
- **Advanced Filtering UX**: Popover-based filter interface with active filter display and management
- **Multi-step Forms UX**: Tabbed forms with photo upload, validation, and progress indication
- **Dashboard UX**: Statistics cards, department distribution, and monthly activity summaries
- **Navigation UX**: Enhanced sidebar navigation with logical business workflow
- **Notification System**: Toast notifications (loading, success, error, info) with action buttons
- **Confirmation Dialogs**: Elegant AlertDialog with contextual information and delete confirmations
- **Tooltips**: Helpful hints on all action buttons for improved clarity
- **Loading States**: Professional loading indicators and progress feedback
- **Error Handling**: User-friendly error messages in Indonesian language
- **Responsive Design**: Mobile-first approach with touch-friendly interactions
- **User Profile**: Dropdown with logout functionality accessible from any page
- **Style Consistency**: All forms follow identical design patterns and button layouts
- **Professional Form Layout UX**: Organized row-based structure with logical field grouping, visual separators, and compact design for enhanced user experience
- **Component Optimization UX**: Space-efficient design with reduced component sizes while maintaining professional appearance and readability
- **Dynamic Field Dependencies UX**: Smart field relationships where department selection depends on division choice with proper validation and user feedback

### Technical Patterns Validated

- **Employee Data Modeling**: Comprehensive MongoDB schema with Personal, HR, and Family data structures
- **Advanced Component Architecture**: Reusable EmployeeStats and EmployeeFilters components with proper separation
- **Custom UI Components**: Tabs and Popover components built with Radix UI integration
- **TypeScript Integration**: Strict typing for both frontend and backend, zero errors
- **MongoDB Patterns**: Mongoose ODM with proper schema validation and data seeding
- **Next.js App Router**: File-based routing with layout components and dynamic routes
- **State Management**: React Context for authentication + local state for complex forms
- **Form Handling**: Multi-step forms with controlled components, validation feedback, and photo upload
- **Error Boundaries**: Graceful error handling and user feedback
- **Component Library**: Complete shadcn/ui integration including custom Tabs and Popover components
- **Layout Patterns**: Module layouts with enhanced sidebar navigation
- **Data Management**: Real API integration + professional mock data systems with 8 employee records
- **Search & Filter Patterns**: Real-time search with advanced multi-criteria filtering
- **Performance Patterns**: Optimized rendering with minimal re-renders and efficient data processing
