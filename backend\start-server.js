const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting backend server...');

// Change to backend directory
process.chdir(__dirname);

// Start the server
const server = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

server.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
});

server.on('close', (code) => {
  console.log(`🔴 Server process exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('👋 Shutting down server...');
  server.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('👋 Shutting down server...');
  server.kill('SIGTERM');
  process.exit(0);
});
