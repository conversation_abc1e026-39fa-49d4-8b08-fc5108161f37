"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Award,
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  ArrowLeft,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface RankCategory {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const RankCategoriesPage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [rankCategories, setRankCategories] = useState<RankCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch rank categories from API
  useEffect(() => {
    fetchRankCategories();
  }, []);

  const fetchRankCategories = async () => {
    try {
      setIsLoading(true);

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        return;
      }

      const response = await fetch(
        "http://localhost:5000/api/hr/rank-categories",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (result.success) {
        setRankCategories(result.data || []);
      } else {
        toast.error("Gagal memuat data", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error fetching rank categories:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat memuat data kategori pangkat",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredRankCategories = rankCategories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (category.description &&
        category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleEdit = (id: string) => {
    router.push(`/hr/rank-categories/edit/${id}`);
  };

  const handleDelete = async (id: string, name: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Menghapus kategori pangkat...", {
        description: `Sedang menghapus ${name}`,
      });

      const token = localStorage.getItem("accessToken");
      if (!token) {
        toast.dismiss(loadingToast);
        toast.error("Sesi telah berakhir", {
          description: "Silakan login kembali",
        });
        return;
      }

      const response = await fetch(
        `http://localhost:5000/api/hr/rank-categories/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success) {
        // Refresh data
        await fetchRankCategories();

        // Show success toast
        toast.success("Kategori pangkat berhasil dihapus!", {
          description: `${name} telah dinonaktifkan dari sistem`,
          action: {
            label: "Undo",
            onClick: () => {
              toast.info("Fitur undo akan segera tersedia");
            },
          },
        });
      } else {
        toast.error("Gagal menghapus kategori pangkat", {
          description: result.message || "Terjadi kesalahan pada server",
        });
      }
    } catch (error) {
      console.error("Error deleting rank category:", error);
      toast.error("Terjadi kesalahan", {
        description: "Tidak dapat menghapus kategori pangkat",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/hr")}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali ke HR
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Kategori Pangkat
              </h1>
              <p className="text-gray-600 mt-1">
                Kelola kategori pangkat karyawan
              </p>
            </div>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => router.push("/hr/rank-categories/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Kategori
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Buat kategori pangkat baru</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari kategori pangkat..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">
                {filteredRankCategories.length} kategori
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Memuat data kategori pangkat...</p>
            </CardContent>
          </Card>
        )}

        {/* Rank Categories Grid */}
        {!isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRankCategories.map((category) => (
              <Card
                key={category.id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <Award className="w-6 h-6 text-purple-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          {category.name}
                        </CardTitle>
                      </div>
                    </div>
                    <Badge
                      variant={category.isActive ? "default" : "secondary"}
                      className={
                        category.isActive ? "bg-green-100 text-green-800" : ""
                      }
                    >
                      {category.isActive ? "Aktif" : "Tidak Aktif"}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">
                    {category.description || "Tidak ada deskripsi"}
                  </p>

                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    Dibuat:{" "}
                    {new Date(category.createdAt).toLocaleDateString("id-ID")}
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(category.id)}
                          className="flex-1"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit kategori pangkat</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleDelete(category.id, category.name)
                          }
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Hapus kategori pangkat</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredRankCategories.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada kategori pangkat
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm
                  ? "Coba sesuaikan kata kunci pencarian."
                  : "Mulai dengan membuat kategori pangkat pertama."}
              </p>
              {!searchTerm && (
                <Button onClick={() => router.push("/hr/rank-categories/add")}>
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Kategori
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </TooltipProvider>
  );
};

export default RankCategoriesPage;
