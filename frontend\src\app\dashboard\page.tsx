"use client";

import { useAuth } from "@/shared/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function DashboardPage() {
  const { user, isLoading, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  const modules = [
    {
      id: "hr",
      name: "Human Resources",
      description: "Kelola data karyawan, cuti, mutasi, dan dokumen personal",
      icon: "👥",
      color: "bg-blue-500",
      href: "/hr",
      permissions: ["hr:read"],
    },
    {
      id: "inventory",
      name: "Inventory Management",
      description: "Kelola aset, barang masuk/keluar, dan tracking QR Code",
      icon: "📦",
      color: "bg-green-500",
      href: "/inventory",
      permissions: ["inventory:read"],
    },
    {
      id: "mess",
      name: "Mess Management",
      description: "Kelola tempat tinggal karyawan dan fasilitas mess",
      icon: "🏠",
      color: "bg-yellow-500",
      href: "/mess",
      permissions: ["mess:read"],
    },
    {
      id: "building",
      name: "Building Management",
      description: "Kelola fasilitas gedung dan aset kantor",
      icon: "🏢",
      color: "bg-purple-500",
      href: "/building",
      permissions: ["building:read"],
    },
    {
      id: "user-access",
      name: "User Access",
      description: "Kelola user, role, dan permission sistem",
      icon: "🔐",
      color: "bg-red-500",
      href: "/user-access",
      permissions: ["user-access:read"],
    },
    {
      id: "chatting",
      name: "Internal Chat",
      description: "Komunikasi internal antar karyawan",
      icon: "💬",
      color: "bg-indigo-500",
      href: "/chat",
      permissions: ["chatting:read"],
    },
  ];

  const hasPermission = (requiredPermissions: string[]) => {
    return requiredPermissions.some((permission) =>
      user.role.permissions.includes(permission)
    );
  };

  const accessibleModules = modules.filter((module) =>
    hasPermission(module.permissions)
  );

  const handleLogout = async () => {
    await logout();
    router.push("/login");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-gray-900">
                  Bebang Information System
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-700">
                <span className="font-medium">{user.fullName}</span>
                <span className="text-gray-500 ml-2">({user.role.name})</span>
              </div>

              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Keluar
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">👋</span>
                </div>
              </div>
              <div className="ml-4">
                <h2 className="text-2xl font-bold text-gray-900">
                  Selamat Datang, {user.firstName}!
                </h2>
                <p className="text-gray-600">
                  Anda login sebagai{" "}
                  <span className="font-medium">{user.role.name}</span>
                  {user.employeeId && (
                    <span className="text-gray-500">
                      {" "}
                      • ID: {user.employeeId}
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                    <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    System Status
                  </p>
                  <p className="text-sm text-green-600">Online & Healthy</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">
                      {accessibleModules.length}
                    </span>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    Modul Tersedia
                  </p>
                  <p className="text-sm text-gray-600">
                    Dari {modules.length} total modul
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-purple-600">
                      {user.role.permissions.length}
                    </span>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    Permissions
                  </p>
                  <p className="text-sm text-gray-600">Hak akses aktif</p>
                </div>
              </div>
            </div>
          </div>

          {/* Modules Grid */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Modul Aplikasi
            </h3>

            {accessibleModules.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {accessibleModules.map((module) => (
                  <div
                    key={module.id}
                    className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200 hover:border-gray-300"
                    onClick={() => router.push(module.href)}
                  >
                    <div className="p-6">
                      <div className="flex items-center">
                        <div
                          className={`flex-shrink-0 h-12 w-12 ${module.color} rounded-lg flex items-center justify-center`}
                        >
                          <span className="text-2xl">{module.icon}</span>
                        </div>
                        <div className="ml-4 flex-1">
                          <h4 className="text-lg font-medium text-gray-900">
                            {module.name}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {module.description}
                          </p>
                        </div>
                      </div>

                      <div className="mt-4">
                        <button className="w-full bg-gray-50 hover:bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors text-sm">
                          Buka Modul →
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                <div className="text-yellow-600 text-4xl mb-4">⚠️</div>
                <h4 className="text-lg font-medium text-yellow-800 mb-2">
                  Tidak Ada Modul yang Dapat Diakses
                </h4>
                <p className="text-yellow-700">
                  Role Anda ({user.role.name}) belum memiliki akses ke modul
                  manapun. Hubungi administrator untuk mendapatkan akses.
                </p>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Aksi Cepat
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <div className="text-2xl mb-2">📊</div>
                <div className="font-medium text-gray-900">Lihat Laporan</div>
                <div className="text-sm text-gray-600">
                  Dashboard & Analytics
                </div>
              </button>

              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <div className="text-2xl mb-2">👤</div>
                <div className="font-medium text-gray-900">Profil Saya</div>
                <div className="text-sm text-gray-600">
                  Edit informasi personal
                </div>
              </button>

              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <div className="text-2xl mb-2">🔔</div>
                <div className="font-medium text-gray-900">Notifikasi</div>
                <div className="text-sm text-gray-600">
                  Pesan & pemberitahuan
                </div>
              </button>

              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <div className="text-2xl mb-2">❓</div>
                <div className="font-medium text-gray-900">Bantuan</div>
                <div className="text-sm text-gray-600">Panduan & support</div>
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
