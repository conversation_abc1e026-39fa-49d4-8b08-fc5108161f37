"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  disabled?: boolean;
}

export function PhoneInput({
  value,
  onChange,
  placeholder = "Masukkan nomor handphone",
  className = "",
  id,
  disabled = false,
}: PhoneInputProps) {
  const [localValue, setLocalValue] = useState(value || "");

  // Update local value when prop value changes (but avoid loops)
  useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value || "");
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange(newValue);
  };

  return (
    <Input
      id={id}
      type="tel"
      value={localValue}
      onChange={handleChange}
      placeholder={placeholder}
      className={`h-11 ${className}`}
      autoComplete="tel"
      disabled={disabled}
    />
  );
}
