# BIS Service Manager
# Script untuk mengelola backend dan frontend service

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "status", "kill-all")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("backend", "frontend", "both")]
    [string]$Service = "both"
)

# Konfigurasi port
$BACKEND_PORT = 5000
$FRONTEND_PORT = 3000

# Fungsi untuk mencari process berdasarkan port
function Get-ProcessByPort {
    param([int]$Port)
    
    try {
        $netstat = netstat -ano | Select-String ":$Port "
        if ($netstat) {
            foreach ($line in $netstat) {
                if ($line -match ":$Port\s+.*LISTENING\s+(\d+)") {
                    $pid = $matches[1]
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process) {
                        return @{
                            PID = $pid
                            Name = $process.ProcessName
                            Port = $Port
                        }
                    }
                }
            }
        }
    }
    catch {
        # Ignore errors
    }
    return $null
}

# Fungsi untuk kill process berdasarkan port
function Stop-ProcessByPort {
    param([int]$Port, [string]$ServiceName)
    
    $process = Get-ProcessByPort -Port $Port
    if ($process) {
        Write-Host "🔴 Menghentikan $ServiceName (PID: $($process.PID), Port: $Port)..." -ForegroundColor Red
        try {
            Stop-Process -Id $process.PID -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
            
            # Verifikasi process sudah mati
            $stillRunning = Get-ProcessByPort -Port $Port
            if ($stillRunning) {
                Write-Host "⚠️  Process masih berjalan, mencoba force kill..." -ForegroundColor Yellow
                taskkill /F /PID $process.PID 2>$null
            } else {
                Write-Host "✅ $ServiceName berhasil dihentikan" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "❌ Gagal menghentikan $ServiceName" -ForegroundColor Red
        }
    } else {
        Write-Host "ℹ️  $ServiceName tidak berjalan di port $Port" -ForegroundColor Gray
    }
}

# Fungsi untuk menampilkan status service
function Show-ServiceStatus {
    Write-Host "`n📊 STATUS SERVICE BIS" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan
    
    # Check Backend
    $backendProcess = Get-ProcessByPort -Port $BACKEND_PORT
    if ($backendProcess) {
        Write-Host "🟢 Backend: RUNNING (PID: $($backendProcess.PID), Port: $BACKEND_PORT)" -ForegroundColor Green
    } else {
        Write-Host "🔴 Backend: STOPPED (Port: $BACKEND_PORT)" -ForegroundColor Red
    }
    
    # Check Frontend
    $frontendProcess = Get-ProcessByPort -Port $FRONTEND_PORT
    if ($frontendProcess) {
        Write-Host "🟢 Frontend: RUNNING (PID: $($frontendProcess.PID), Port: $FRONTEND_PORT)" -ForegroundColor Green
    } else {
        Write-Host "🔴 Frontend: STOPPED (Port: $FRONTEND_PORT)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Fungsi untuk start backend
function Start-Backend {
    $backendProcess = Get-ProcessByPort -Port $BACKEND_PORT
    if ($backendProcess) {
        Write-Host "⚠️  Backend sudah berjalan di port $BACKEND_PORT (PID: $($backendProcess.PID))" -ForegroundColor Yellow
        return
    }
    
    Write-Host "🚀 Memulai Backend Service..." -ForegroundColor Green
    Set-Location "backend"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev" -WindowStyle Normal
    Set-Location ".."
    
    # Wait dan check apakah berhasil start
    Write-Host "⏳ Menunggu backend service siap..." -ForegroundColor Yellow
    for ($i = 1; $i -le 10; $i++) {
        Start-Sleep -Seconds 2
        $process = Get-ProcessByPort -Port $BACKEND_PORT
        if ($process) {
            Write-Host "✅ Backend berhasil dimulai di port $BACKEND_PORT" -ForegroundColor Green
            return
        }
        Write-Host "." -NoNewline -ForegroundColor Yellow
    }
    Write-Host "`n❌ Backend gagal dimulai dalam 20 detik" -ForegroundColor Red
}

# Fungsi untuk start frontend
function Start-Frontend {
    $frontendProcess = Get-ProcessByPort -Port $FRONTEND_PORT
    if ($frontendProcess) {
        Write-Host "⚠️  Frontend sudah berjalan di port $FRONTEND_PORT (PID: $($frontendProcess.PID))" -ForegroundColor Yellow
        return
    }
    
    Write-Host "🚀 Memulai Frontend Service..." -ForegroundColor Green
    Set-Location "frontend"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev" -WindowStyle Normal
    Set-Location ".."
    
    # Wait dan check apakah berhasil start
    Write-Host "⏳ Menunggu frontend service siap..." -ForegroundColor Yellow
    for ($i = 1; $i -le 15; $i++) {
        Start-Sleep -Seconds 2
        $process = Get-ProcessByPort -Port $FRONTEND_PORT
        if ($process) {
            Write-Host "✅ Frontend berhasil dimulai di port $FRONTEND_PORT" -ForegroundColor Green
            return
        }
        Write-Host "." -NoNewline -ForegroundColor Yellow
    }
    Write-Host "`n❌ Frontend gagal dimulai dalam 30 detik" -ForegroundColor Red
}

# Main logic
Write-Host "`n🔧 BIS Service Manager" -ForegroundColor Cyan
Write-Host "=" * 30 -ForegroundColor Cyan

switch ($Action) {
    "status" {
        Show-ServiceStatus
    }
    
    "stop" {
        switch ($Service) {
            "backend" {
                Stop-ProcessByPort -Port $BACKEND_PORT -ServiceName "Backend"
            }
            "frontend" {
                Stop-ProcessByPort -Port $FRONTEND_PORT -ServiceName "Frontend"
            }
            "both" {
                Stop-ProcessByPort -Port $BACKEND_PORT -ServiceName "Backend"
                Stop-ProcessByPort -Port $FRONTEND_PORT -ServiceName "Frontend"
            }
        }
        Show-ServiceStatus
    }
    
    "start" {
        # Tampilkan status awal
        Show-ServiceStatus
        
        switch ($Service) {
            "backend" {
                Start-Backend
            }
            "frontend" {
                Start-Frontend
            }
            "both" {
                Start-Backend
                Start-Sleep -Seconds 3
                Start-Frontend
            }
        }
        
        Start-Sleep -Seconds 3
        Show-ServiceStatus
    }
    
    "restart" {
        Write-Host "🔄 Restart Service..." -ForegroundColor Yellow
        
        # Stop services
        switch ($Service) {
            "backend" {
                Stop-ProcessByPort -Port $BACKEND_PORT -ServiceName "Backend"
            }
            "frontend" {
                Stop-ProcessByPort -Port $FRONTEND_PORT -ServiceName "Frontend"
            }
            "both" {
                Stop-ProcessByPort -Port $BACKEND_PORT -ServiceName "Backend"
                Stop-ProcessByPort -Port $FRONTEND_PORT -ServiceName "Frontend"
            }
        }
        
        Start-Sleep -Seconds 3
        
        # Start services
        switch ($Service) {
            "backend" {
                Start-Backend
            }
            "frontend" {
                Start-Frontend
            }
            "both" {
                Start-Backend
                Start-Sleep -Seconds 3
                Start-Frontend
            }
        }
        
        Start-Sleep -Seconds 3
        Show-ServiceStatus
    }
    
    "kill-all" {
        Write-Host "💀 Menghentikan semua service Node.js dan Next.js..." -ForegroundColor Red
        
        # Kill semua process node dan next
        Get-Process | Where-Object {$_.ProcessName -like "*node*" -or $_.ProcessName -like "*next*"} | ForEach-Object {
            Write-Host "🔴 Menghentikan $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Red
            Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
        }
        
        Start-Sleep -Seconds 2
        Show-ServiceStatus
    }
}

Write-Host "`n✨ Selesai!" -ForegroundColor Green
