import jwt from "jsonwebtoken";
import { config } from "./environment";

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  permissions: string[];
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export class JWTService {
  /**
   * Generate access token
   */
  static generateAccessToken(payload: JWTPayload): string {
    return (jwt.sign as any)(payload, config.JWT_SECRET, {
      expiresIn: config.JWT_EXPIRES_IN,
      issuer: config.APP_NAME,
      audience: config.COMPANY_NAME,
    });
  }

  /**
   * Generate refresh token
   */
  static generateRefreshToken(
    payload: Pick<JWTPayload, "userId" | "email">
  ): string {
    return (jwt.sign as any)(payload, config.JWT_REFRESH_SECRET, {
      expiresIn: config.JWT_REFRESH_EXPIRES_IN,
      issuer: config.APP_NAME,
      audience: config.COMPANY_NAME,
    });
  }

  /**
   * Generate both access and refresh tokens
   */
  static generateTokenPair(payload: JWTPayload): TokenPair {
    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken({
      userId: payload.userId,
      email: payload.email,
    });

    return { accessToken, refreshToken };
  }

  /**
   * Verify access token
   */
  static verifyAccessToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, config.JWT_SECRET, {
        issuer: config.APP_NAME,
        audience: config.COMPANY_NAME,
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error("Token telah kedaluwarsa");
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Token tidak valid");
      } else {
        throw new Error("Gagal memverifikasi token");
      }
    }
  }

  /**
   * Verify refresh token
   */
  static verifyRefreshToken(
    token: string
  ): Pick<JWTPayload, "userId" | "email"> {
    try {
      const decoded = jwt.verify(token, config.JWT_REFRESH_SECRET, {
        issuer: config.APP_NAME,
        audience: config.COMPANY_NAME,
      }) as Pick<JWTPayload, "userId" | "email">;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error("Refresh token telah kedaluwarsa");
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Refresh token tidak valid");
      } else {
        throw new Error("Gagal memverifikasi refresh token");
      }
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  static decodeToken(token: string): any {
    return jwt.decode(token);
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) return true;
    return expiration < new Date();
  }
}

export default JWTService;
