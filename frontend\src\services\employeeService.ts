import { toast } from "sonner";

// Base API URL
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

// Employee interface matching backend model
export interface Employee {
  _id: string;
  personal: {
    employeeId: string;
    fullName: string;
    gender: "La<PERSON>-laki" | "Perempuan";
    placeOfBirth: string;
    dateOfBirth: string;
    email: string;
    phone: string;
    profilePhoto?: string;

    // Identification
    religion:
      | "Islam"
      | "Kristen"
      | "Katolik"
      | "Hindu"
      | "Buddha"
      | "Konghucu"
      | "Lainnya";
    bloodType: "A" | "B" | "AB" | "O";
    familyCardNumber: string;
    idCardNumber: string;
    taxNumber?: string;
    bpjsTkNumber?: string;
    nikKkNumber?: string;
    taxStatus:
      | "TK/0"
      | "TK/1"
      | "TK/2"
      | "TK/3"
      | "K/0"
      | "K/1"
      | "K/2"
      | "K/3";

    // Address - Domisili
    currentAddress: {
      street: string;
      city: string;
      province: string;
    };

    // Address - KTP
    idCardAddress: {
      street: string;
      city: string;
      province: string;
    };

    // Contact Information
    contact: {
      mobilePhone1: string;
      mobilePhone2?: string;
      homePhone1?: string;
      homePhone2?: string;
    };

    // Marital Status and Children
    maritalInfo: {
      status: "Belum Menikah" | "Menikah" | "Cerai" | "Janda/Duda";
      spouseName?: string;
      spouseJob?: string;
      numberOfChildren: number;
    };

    // Bank Account
    bankAccount: {
      accountNumber: string;
      accountHolder: string;
      bankName: string;
    };
  };
  hr: {
    department: {
      _id: string;
      name: string;
    };
    position: {
      _id: string;
      name: string;
    };
    tags?: Array<{
      _id: string;
      name: string;
      color: string;
    }>;
    companyEmail?: string; // Email perusahaan

    // Contract Information
    contract: {
      employmentType: {
        _id: string;
        name: string;
      };
      hireDate: string;
      contractDate?: string;
      contractEndDate?: string;
    };

    // Education
    education: {
      certificateLevel:
        | "SD"
        | "SMP"
        | "SMA"
        | "D1"
        | "D2"
        | "D3"
        | "S1"
        | "S2"
        | "S3";
      fieldOfStudy: string;
      schoolName: string;
      schoolCity: string;
      description?: string;
    };

    // Rank and Grade
    rank: {
      rankCategory?: {
        _id: string;
        name: string;
      };
      rankGrade?: {
        _id: string;
        name: string;
      };
      rankSubgrade?: {
        _id: string;
        name: string;
      };
      pensionFundNumber?: string;
    };

    // Emergency Contact
    emergency: {
      contactName: string;
      contactPhone: string;
      contactPhone2: string;
      relationship: string;
      address: string;
    };

    // POO/POH
    location: {
      pointOfOrigin: string;
      pointOfHire: string;
    };

    // Uniform and Work Shoes
    uniform: {
      workUniformSize: "XS" | "S" | "M" | "L" | "XL" | "XXL" | "XXXL";
      workShoesSize: string;
    };

    // Salary (keeping existing structure)
    salary: {
      basic: number;
      allowances: {
        transport: number;
        meal: number;
        communication: number;
        position: number;
        other: number;
      };
      total?: number;
    };

    workSchedule: "Regular" | "Shift" | "Flexible";
  };
  family: {
    // Spouse and Children
    spouse: {
      name?: string;
      dateOfBirth?: string;
      marriageDate?: string;
      lastEducation?: string;
      occupation?: string;
      numberOfChildren: number;
    };

    // Children Identity (up to 4 children)
    children: Array<{
      name?: string;
      gender?: "Laki-laki" | "Perempuan";
      dateOfBirth?: string;
    }>;

    // Parents Information
    parents: {
      father: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      };
      mother: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      };
    };

    // Siblings Information
    siblings: {
      childOrder: number;
      totalSiblings: number;
      siblingsData: Array<{
        name?: string;
        gender?: "Laki-laki" | "Perempuan";
        dateOfBirth?: string;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      }>;
    };

    // In-laws Information
    inLaws: {
      fatherInLaw?: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        description?: string;
      };
      motherInLaw?: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        description?: string;
      };
    };
  };
  status:
    | "Aktif"
    | "Probation"
    | "Cuti"
    | "Tidak Aktif"
    | "Notice Period"
    | "Resign"
    | "Terminated"
    | "Pension"
    | "Kontrak Habis";
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Create employee data interface for forms
export interface CreateEmployeeData {
  personal: {
    employeeId: string;
    fullName: string;
    gender: "Laki-laki" | "Perempuan";
    placeOfBirth: string;
    dateOfBirth: string;
    email: string;
    phone: string;

    // Identification
    religion:
      | "Islam"
      | "Kristen"
      | "Katolik"
      | "Hindu"
      | "Buddha"
      | "Konghucu"
      | "Lainnya";
    bloodType: "A" | "B" | "AB" | "O";
    familyCardNumber: string;
    idCardNumber: string;
    taxNumber?: string;
    bpjsTkNumber?: string;
    nikKkNumber?: string;
    taxStatus:
      | "TK/0"
      | "TK/1"
      | "TK/2"
      | "TK/3"
      | "K/0"
      | "K/1"
      | "K/2"
      | "K/3";

    // Address - Domisili
    currentAddress: {
      street: string;
      city: string;
      province: string;
    };

    // Address - KTP
    idCardAddress: {
      street: string;
      city: string;
      province: string;
    };

    // Contact Information
    contact: {
      mobilePhone1: string;
      mobilePhone2?: string;
      homePhone1?: string;
      homePhone2?: string;
    };

    // Marital Status and Children
    maritalInfo: {
      status: "Belum Menikah" | "Menikah" | "Cerai" | "Janda/Duda";
      spouseName?: string;
      spouseJob?: string;
      numberOfChildren: number;
    };

    // Bank Account
    bankAccount: {
      accountNumber: string;
      accountHolder: string;
      bankName: string;
    };
  };
  hr: {
    division: string; // ObjectId reference to Division
    department: string; // ObjectId reference to Department
    position: string; // ObjectId reference to Position
    tags?: string[]; // Array of ObjectId references to Tags
    companyEmail?: string; // Email perusahaan
    status?:
      | "Aktif"
      | "Probation"
      | "Cuti"
      | "Tidak Aktif"
      | "Notice Period"
      | "Resign"
      | "Terminated"
      | "Pension"
      | "Kontrak Habis";

    // Contract Information
    contract: {
      employmentType: string;
      hireDate: string;
      contractDate?: string;
      contractEndDate?: string;
      permanentDate?: string;
      exitDate?: string;
    };

    // Education
    education: {
      certificateLevel:
        | "SD"
        | "SMP"
        | "SMA"
        | "SMK"
        | "D1"
        | "D2"
        | "D3"
        | "S1"
        | "S2"
        | "S3";
      fieldOfStudy: string;
      schoolName: string;
      schoolCity: string;
      graduationStatus?: "Lulus" | "Tidak Lulus" | "Sedang Belajar";
      description?: string;
    };

    // Rank and Grade
    rank: {
      rankCategory?: string;
      rankGrade?: string;
      rankSubgrade?: string;
      pensionFundNumber?: string;
    };

    // Emergency Contact
    emergency: {
      contactName: string;
      contactPhone: string;
      contactPhone2?: string;
      relationship: string;
      address: string;
    };

    // POO/POH
    location: {
      pointOfOrigin: string;
      pointOfHire: string;
    };

    // Uniform and Work Shoes
    uniform: {
      workUniformSize: "XS" | "S" | "M" | "L" | "XL" | "XXL" | "XXXL";
      workShoesSize: string;
    };

    // Salary (keeping existing structure)
    salary: {
      basic: number;
      allowances: {
        transport: number;
        meal: number;
        communication: number;
        position: number;
        other: number;
      };
    };

    workSchedule: "Regular" | "Shift" | "Flexible" | "Remote" | "Part Time";
    manager?: string; // ObjectId reference to Employee (Manager)
    directSupervisor?: string; // ObjectId reference to Employee (Atasan Langsung)
  };
  family: {
    // Spouse and Children
    spouse: {
      name?: string;
      dateOfBirth?: string;
      marriageDate?: string;
      lastEducation?: string;
      occupation?: string;
      numberOfChildren: number;
    };

    // Children Identity (up to 4 children)
    children: Array<{
      name?: string;
      gender?: "Laki-laki" | "Perempuan";
      dateOfBirth?: string;
    }>;

    // Parents Information
    parents: {
      father: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      };
      mother: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      };
    };

    // Siblings Information
    siblings: {
      childOrder: number;
      totalSiblings: number;
      siblingsData: Array<{
        name?: string;
        gender?: "Laki-laki" | "Perempuan";
        dateOfBirth?: string;
        lastEducation?: string;
        occupation?: string;
        description?: string;
      }>;
    };

    // In-laws Information
    inLaws: {
      fatherInLaw?: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        description?: string;
      };
      motherInLaw?: {
        name?: string;
        dateOfBirth?: string;
        lastEducation?: string;
        description?: string;
      };
    };
  };
}

// API Response interfaces
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    current: number;
    pages: number;
    total: number;
    limit: number;
  };
}

interface EmployeeStats {
  overview: {
    total: number;
    active: number;
    inactive: number;
    avgSalary: number;
  };
  byDepartment: Array<{
    _id: string;
    count: number;
  }>;
}

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("accessToken");
  }
  return null;
};

// Helper function to make authenticated requests
const makeRequest = async (url: string, options: RequestInit = {}) => {
  const token = getAuthToken();

  const headers = {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));

    // Handle different types of errors with more informative messages
    let errorMessage = "Terjadi kesalahan";

    if (errorData.errors && Array.isArray(errorData.errors)) {
      // Multiple validation errors
      errorMessage = errorData.errors.join(", ");
    } else if (errorData.details) {
      // Detailed error message
      errorMessage = errorData.details;
    } else if (errorData.message) {
      // Single error message
      errorMessage = errorData.message;
    } else {
      // Fallback based on status code
      switch (response.status) {
        case 400:
          errorMessage = "Data yang dikirim tidak valid";
          break;
        case 401:
          errorMessage = "Anda tidak memiliki akses";
          break;
        case 403:
          errorMessage = "Akses ditolak";
          break;
        case 404:
          errorMessage = "Data tidak ditemukan";
          break;
        case 409:
          errorMessage = "Data sudah ada";
          break;
        case 500:
          errorMessage = "Terjadi kesalahan pada server";
          break;
        default:
          errorMessage = `Kesalahan HTTP: ${response.status}`;
      }
    }

    throw new Error(errorMessage);
  }

  return response.json();
};

// Employee Service Functions
export const employeeService = {
  // Get all employees with pagination and filters
  async getEmployees(
    params: {
      page?: number;
      limit?: number;
      search?: string;
      department?: string;
      position?: string;
      status?: string;
    } = {}
  ): Promise<PaginatedResponse<Employee>> {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append("page", params.page.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());
    if (params.search) queryParams.append("search", params.search);
    if (params.department) queryParams.append("department", params.department);
    if (params.position) queryParams.append("position", params.position);
    if (params.status) queryParams.append("status", params.status);

    const url = `/hr/employees${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    return makeRequest(url);
  },

  // Get employee by ID
  async getEmployeeById(id: string): Promise<ApiResponse<Employee>> {
    return makeRequest(`/hr/employees/${id}`);
  },

  // Create new employee
  async createEmployee(
    data: CreateEmployeeData
  ): Promise<ApiResponse<Employee>> {
    try {
      const response = await makeRequest("/hr/employees", {
        method: "POST",
        body: JSON.stringify(data),
      });

      toast.success("Karyawan berhasil ditambahkan", {
        description: `${data.personal.fullName} telah ditambahkan ke sistem`,
      });

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Gagal menambahkan karyawan";

      // Show more informative error message
      toast.error("Gagal menambahkan karyawan", {
        description: errorMessage,
        duration: 6000, // Show longer for detailed messages
      });

      console.error("Create employee error details:", error);
      throw error;
    }
  },

  // Update employee
  async updateEmployee(
    id: string,
    data: Partial<CreateEmployeeData>
  ): Promise<ApiResponse<Employee>> {
    try {
      const response = await makeRequest(`/hr/employees/${id}`, {
        method: "PUT",
        body: JSON.stringify(data),
      });

      toast.success("Data karyawan berhasil diperbarui", {
        description: "Perubahan telah disimpan ke sistem",
      });

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Gagal memperbarui data karyawan";
      toast.error("Gagal memperbarui data karyawan", {
        description: errorMessage,
      });
      throw error;
    }
  },

  // Delete employee (soft delete)
  async deleteEmployee(id: string): Promise<ApiResponse<void>> {
    try {
      const response = await makeRequest(`/hr/employees/${id}`, {
        method: "DELETE",
      });

      toast.success("Karyawan berhasil dihapus", {
        description: "Data karyawan telah dihapus dari sistem",
      });

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Gagal menghapus karyawan";
      toast.error("Gagal menghapus karyawan", {
        description: errorMessage,
      });
      throw error;
    }
  },

  // Get employee statistics
  async getEmployeeStats(): Promise<ApiResponse<EmployeeStats>> {
    return makeRequest("/hr/employees/stats");
  },
};
